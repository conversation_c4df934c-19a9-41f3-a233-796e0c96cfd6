const __vite__mapDeps = (i, m=__vite__mapDeps, d=(m.f || (m.f = ["assets/index-BJ1R8LeI.js", "assets/index-D9PW5A0h.css"]))) => i.map(i => d[i]);
(function() {
    const t = document.createElement("link").relList;
    if (t && t.supports && t.supports("modulepreload"))
        return;
    for (const s of document.querySelectorAll('link[rel="modulepreload"]'))
        r(s);
    new MutationObserver(s => {
        for (const o of s)
            if (o.type === "childList")
                for (const i of o.addedNodes)
                    i.tagName === "LINK" && i.rel === "modulepreload" && r(i)
    }
    ).observe(document, {
        childList: !0,
        subtree: !0
    });
    function n(s) {
        const o = {};
        return s.integrity && (o.integrity = s.integrity),
        s.referrerPolicy && (o.referrerPolicy = s.referrerPolicy),
        s.crossOrigin === "use-credentials" ? o.credentials = "include" : s.crossOrigin === "anonymous" ? o.credentials = "omit" : o.credentials = "same-origin",
        o
    }
    function r(s) {
        if (s.ep)
            return;
        s.ep = !0;
        const o = n(s);
        fetch(s.href, o)
    }
}
)();
(function() {
    if (typeof window > "u")
        return;
    var e, t = "ontouchstart"in window;
    document.createTouch || (document.createTouch = function(a, f, d, g, y, b, w) {
        return new n(f,d,{
            pageX: g,
            pageY: y,
            screenX: b,
            screenY: w,
            clientX: g - window.pageXOffset,
            clientY: y - window.pageYOffset
        },0,0)
    }
    ),
    document.createTouchList || (document.createTouchList = function() {
        for (var a = r(), f = 0; f < arguments.length; f++)
            a[f] = arguments[f];
        return a.length = arguments.length,
        a
    }
    ),
    Element.prototype.matches || (Element.prototype.matches = Element.prototype.msMatchesSelector || Element.prototype.webkitMatchesSelector),
    Element.prototype.closest || (Element.prototype.closest = function(a) {
        var f = this;
        do {
            if (f.matches(a))
                return f;
            f = f.parentElement || f.parentNode
        } while (f !== null && f.nodeType === 1);
        return null
    }
    );
    var n = function(f, d, g, y, b) {
        y = y || 0,
        b = b || 0,
        this.identifier = d,
        this.target = f,
        this.clientX = g.clientX + y,
        this.clientY = g.clientY + b,
        this.screenX = g.screenX + y,
        this.screenY = g.screenY + b,
        this.pageX = g.pageX + y,
        this.pageY = g.pageY + b
    };
    function r() {
        var a = [];
        return a.item = function(f) {
            return this[f] || null
        }
        ,
        a.identifiedTouch = function(f) {
            return this[f + 1] || null
        }
        ,
        a
    }
    var s = !1;
    function o(a) {
        return function(f) {
            f.type === "mousedown" && (s = !0),
            f.type === "mouseup" && (s = !1),
            !(f.type === "mousemove" && !s) && ((f.type === "mousedown" || !e || e && !e.dispatchEvent) && (e = f.target),
            e.closest("[data-no-touch-simulate]") == null && i(a, f),
            f.type === "mouseup" && (e = null))
        }
    }
    function i(a, f) {
        var d = document.createEvent("Event");
        d.initEvent(a, !0, !0),
        d.altKey = f.altKey,
        d.ctrlKey = f.ctrlKey,
        d.metaKey = f.metaKey,
        d.shiftKey = f.shiftKey,
        d.touches = c(f),
        d.targetTouches = c(f),
        d.changedTouches = l(f),
        e.dispatchEvent(d)
    }
    function l(a) {
        var f = r();
        return f.push(new n(e,1,a,0,0)),
        f
    }
    function c(a) {
        return a.type === "mouseup" ? r() : l(a)
    }
    function u() {
        window.addEventListener("mousedown", o("touchstart"), !0),
        window.addEventListener("mousemove", o("touchmove"), !0),
        window.addEventListener("mouseup", o("touchend"), !0)
    }
    u.multiTouchOffset = 75,
    t || new u
}
)();
function eo() {}
const We = Object.assign
  , Yr = typeof window < "u"
  , fr = e => e !== null && typeof e == "object"
  , Be = e => e != null
  , Xn = e => typeof e == "function"
  , Xl = e => fr(e) && Xn(e.then) && Xn(e.catch)
  , Zl = e => typeof e == "number" || /^\d+(\.\d+)?$/.test(e)
  , Fu = () => Yr ? /ios|iphone|ipad|ipod/.test(navigator.userAgent.toLowerCase()) : !1;
function ui(e, t) {
    const n = t.split(".");
    let r = e;
    return n.forEach(s => {
        var o;
        r = fr(r) && (o = r[s]) != null ? o : ""
    }
    ),
    r
}
function Fr(e, t, n) {
    return t.reduce( (r, s) => (r[s] = e[s],
    r), {})
}
const fi = e => Array.isArray(e) ? e : [e]
  , kt = null
  , pe = [Number, String]
  , Re = {
    type: Boolean,
    default: !0
}
  , Rs = e => ({
    type: e,
    required: !0
})
  , Jb = e => ({
    type: Number,
    default: e
})
  , ut = e => ({
    type: pe,
    default: e
})
  , _e = e => ({
    type: String,
    default: e
});
/**
* @vue/shared v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/
/*! #__NO_SIDE_EFFECTS__ */
function Ao(e) {
    const t = Object.create(null);
    for (const n of e.split(","))
        t[n] = 1;
    return n => n in t
}
const ye = {}
  , wn = []
  , _t = () => {}
  , Du = () => !1
  , Gr = e => e.charCodeAt(0) === 111 && e.charCodeAt(1) === 110 && (e.charCodeAt(2) > 122 || e.charCodeAt(2) < 97)
  , Oo = e => e.startsWith("onUpdate:")
  , Le = Object.assign
  , Po = (e, t) => {
    const n = e.indexOf(t);
    n > -1 && e.splice(n, 1)
}
  , Mu = Object.prototype.hasOwnProperty
  , he = (e, t) => Mu.call(e, t)
  , ee = Array.isArray
  , Sn = e => Xr(e) === "[object Map]"
  , Ql = e => Xr(e) === "[object Set]"
  , re = e => typeof e == "function"
  , Ee = e => typeof e == "string"
  , Gt = e => typeof e == "symbol"
  , Se = e => e !== null && typeof e == "object"
  , ec = e => (Se(e) || re(e)) && re(e.then) && re(e.catch)
  , tc = Object.prototype.toString
  , Xr = e => tc.call(e)
  , ju = e => Xr(e).slice(8, -1)
  , nc = e => Xr(e) === "[object Object]"
  , Io = e => Ee(e) && e !== "NaN" && e[0] !== "-" && "" + parseInt(e, 10) === e
  , jn = Ao(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted")
  , Zr = e => {
    const t = Object.create(null);
    return n => t[n] || (t[n] = e(n))
}
  , Hu = /-(\w)/g
  , it = Zr(e => e.replace(Hu, (t, n) => n ? n.toUpperCase() : ""))
  , Uu = /\B([A-Z])/g
  , Nt = Zr(e => e.replace(Uu, "-$1").toLowerCase())
  , Qr = Zr(e => e.charAt(0).toUpperCase() + e.slice(1))
  , As = Zr(e => e ? `on${Qr(e)}` : "")
  , qt = (e, t) => !Object.is(e, t)
  , Os = (e, ...t) => {
    for (let n = 0; n < e.length; n++)
        e[n](...t)
}
  , rc = (e, t, n, r=!1) => {
    Object.defineProperty(e, t, {
        configurable: !0,
        enumerable: !1,
        writable: r,
        value: n
    })
}
  , Vu = e => {
    const t = parseFloat(e);
    return isNaN(t) ? e : t
}
  , zu = e => {
    const t = Ee(e) ? Number(e) : NaN;
    return isNaN(t) ? e : t
}
;
let di;
const es = () => di || (di = typeof globalThis < "u" ? globalThis : typeof self < "u" ? self : typeof window < "u" ? window : typeof global < "u" ? global : {});
function ts(e) {
    if (ee(e)) {
        const t = {};
        for (let n = 0; n < e.length; n++) {
            const r = e[n]
              , s = Ee(r) ? Ju(r) : ts(r);
            if (s)
                for (const o in s)
                    t[o] = s[o]
        }
        return t
    } else if (Ee(e) || Se(e))
        return e
}
const qu = /;(?![^(]*\))/g
  , Ku = /:([^]+)/
  , Wu = /\/\*[^]*?\*\//g;
function Ju(e) {
    const t = {};
    return e.replace(Wu, "").split(qu).forEach(n => {
        if (n) {
            const r = n.split(Ku);
            r.length > 1 && (t[r[0].trim()] = r[1].trim())
        }
    }
    ),
    t
}
function Yu(e) {
    if (!e)
        return "";
    if (Ee(e))
        return e;
    let t = "";
    for (const n in e) {
        const r = e[n];
        if (Ee(r) || typeof r == "number") {
            const s = n.startsWith("--") ? n : Nt(n);
            t += `${s}:${r};`
        }
    }
    return t
}
function ns(e) {
    let t = "";
    if (Ee(e))
        t = e;
    else if (ee(e))
        for (let n = 0; n < e.length; n++) {
            const r = ns(e[n]);
            r && (t += r + " ")
        }
    else if (Se(e))
        for (const n in e)
            e[n] && (t += n + " ");
    return t.trim()
}
const Gu = "itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly"
  , Xu = Ao(Gu);
function sc(e) {
    return !!e || e === ""
}
const oc = e => !!(e && e.__v_isRef === !0)
  , Zu = e => Ee(e) ? e : e == null ? "" : ee(e) || Se(e) && (e.toString === tc || !re(e.toString)) ? oc(e) ? Zu(e.value) : JSON.stringify(e, ic, 2) : String(e)
  , ic = (e, t) => oc(t) ? ic(e, t.value) : Sn(t) ? {
    [`Map(${t.size})`]: [...t.entries()].reduce( (n, [r,s], o) => (n[Ps(r, o) + " =>"] = s,
    n), {})
} : Ql(t) ? {
    [`Set(${t.size})`]: [...t.values()].map(n => Ps(n))
} : Gt(t) ? Ps(t) : Se(t) && !ee(t) && !nc(t) ? String(t) : t
  , Ps = (e, t="") => {
    var n;
    return Gt(e) ? `Symbol(${(n = e.description) != null ? n : t})` : e
}
;
/**
* @vue/reactivity v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/
let Ve;
class lc {
    constructor(t=!1) {
        this.detached = t,
        this._active = !0,
        this.effects = [],
        this.cleanups = [],
        this._isPaused = !1,
        this.parent = Ve,
        !t && Ve && (this.index = (Ve.scopes || (Ve.scopes = [])).push(this) - 1)
    }
    get active() {
        return this._active
    }
    pause() {
        if (this._active) {
            this._isPaused = !0;
            let t, n;
            if (this.scopes)
                for (t = 0,
                n = this.scopes.length; t < n; t++)
                    this.scopes[t].pause();
            for (t = 0,
            n = this.effects.length; t < n; t++)
                this.effects[t].pause()
        }
    }
    resume() {
        if (this._active && this._isPaused) {
            this._isPaused = !1;
            let t, n;
            if (this.scopes)
                for (t = 0,
                n = this.scopes.length; t < n; t++)
                    this.scopes[t].resume();
            for (t = 0,
            n = this.effects.length; t < n; t++)
                this.effects[t].resume()
        }
    }
    run(t) {
        if (this._active) {
            const n = Ve;
            try {
                return Ve = this,
                t()
            } finally {
                Ve = n
            }
        }
    }
    on() {
        Ve = this
    }
    off() {
        Ve = this.parent
    }
    stop(t) {
        if (this._active) {
            this._active = !1;
            let n, r;
            for (n = 0,
            r = this.effects.length; n < r; n++)
                this.effects[n].stop();
            for (this.effects.length = 0,
            n = 0,
            r = this.cleanups.length; n < r; n++)
                this.cleanups[n]();
            if (this.cleanups.length = 0,
            this.scopes) {
                for (n = 0,
                r = this.scopes.length; n < r; n++)
                    this.scopes[n].stop(!0);
                this.scopes.length = 0
            }
            if (!this.detached && this.parent && !t) {
                const s = this.parent.scopes.pop();
                s && s !== this && (this.parent.scopes[this.index] = s,
                s.index = this.index)
            }
            this.parent = void 0
        }
    }
}
function cc(e) {
    return new lc(e)
}
function ac() {
    return Ve
}
function Qu(e, t=!1) {
    Ve && Ve.cleanups.push(e)
}
let ve;
const Is = new WeakSet;
class uc {
    constructor(t) {
        this.fn = t,
        this.deps = void 0,
        this.depsTail = void 0,
        this.flags = 5,
        this.next = void 0,
        this.cleanup = void 0,
        this.scheduler = void 0,
        Ve && Ve.active && Ve.effects.push(this)
    }
    pause() {
        this.flags |= 64
    }
    resume() {
        this.flags & 64 && (this.flags &= -65,
        Is.has(this) && (Is.delete(this),
        this.trigger()))
    }
    notify() {
        this.flags & 2 && !(this.flags & 32) || this.flags & 8 || dc(this)
    }
    run() {
        if (!(this.flags & 1))
            return this.fn();
        this.flags |= 2,
        hi(this),
        hc(this);
        const t = ve
          , n = ft;
        ve = this,
        ft = !0;
        try {
            return this.fn()
        } finally {
            pc(this),
            ve = t,
            ft = n,
            this.flags &= -3
        }
    }
    stop() {
        if (this.flags & 1) {
            for (let t = this.deps; t; t = t.nextDep)
                No(t);
            this.deps = this.depsTail = void 0,
            hi(this),
            this.onStop && this.onStop(),
            this.flags &= -2
        }
    }
    trigger() {
        this.flags & 64 ? Is.add(this) : this.scheduler ? this.scheduler() : this.runIfDirty()
    }
    runIfDirty() {
        to(this) && this.run()
    }
    get dirty() {
        return to(this)
    }
}
let fc = 0, Hn, Un;
function dc(e, t=!1) {
    if (e.flags |= 8,
    t) {
        e.next = Un,
        Un = e;
        return
    }
    e.next = Hn,
    Hn = e
}
function Bo() {
    fc++
}
function ko() {
    if (--fc > 0)
        return;
    if (Un) {
        let t = Un;
        for (Un = void 0; t; ) {
            const n = t.next;
            t.next = void 0,
            t.flags &= -9,
            t = n
        }
    }
    let e;
    for (; Hn; ) {
        let t = Hn;
        for (Hn = void 0; t; ) {
            const n = t.next;
            if (t.next = void 0,
            t.flags &= -9,
            t.flags & 1)
                try {
                    t.trigger()
                } catch (r) {
                    e || (e = r)
                }
            t = n
        }
    }
    if (e)
        throw e
}
function hc(e) {
    for (let t = e.deps; t; t = t.nextDep)
        t.version = -1,
        t.prevActiveLink = t.dep.activeLink,
        t.dep.activeLink = t
}
function pc(e) {
    let t, n = e.depsTail, r = n;
    for (; r; ) {
        const s = r.prevDep;
        r.version === -1 ? (r === n && (n = s),
        No(r),
        ef(r)) : t = r,
        r.dep.activeLink = r.prevActiveLink,
        r.prevActiveLink = void 0,
        r = s
    }
    e.deps = t,
    e.depsTail = n
}
function to(e) {
    for (let t = e.deps; t; t = t.nextDep)
        if (t.dep.version !== t.version || t.dep.computed && (gc(t.dep.computed) || t.dep.version !== t.version))
            return !0;
    return !!e._dirty
}
function gc(e) {
    if (e.flags & 4 && !(e.flags & 16) || (e.flags &= -17,
    e.globalVersion === Zn))
        return;
    e.globalVersion = Zn;
    const t = e.dep;
    if (e.flags |= 2,
    t.version > 0 && !e.isSSR && e.deps && !to(e)) {
        e.flags &= -3;
        return
    }
    const n = ve
      , r = ft;
    ve = e,
    ft = !0;
    try {
        hc(e);
        const s = e.fn(e._value);
        (t.version === 0 || qt(s, e._value)) && (e._value = s,
        t.version++)
    } catch (s) {
        throw t.version++,
        s
    } finally {
        ve = n,
        ft = r,
        pc(e),
        e.flags &= -3
    }
}
function No(e, t=!1) {
    const {dep: n, prevSub: r, nextSub: s} = e;
    if (r && (r.nextSub = s,
    e.prevSub = void 0),
    s && (s.prevSub = r,
    e.nextSub = void 0),
    n.subs === e && (n.subs = r,
    !r && n.computed)) {
        n.computed.flags &= -5;
        for (let o = n.computed.deps; o; o = o.nextDep)
            No(o, !0)
    }
    !t && !--n.sc && n.map && n.map.delete(n.key)
}
function ef(e) {
    const {prevDep: t, nextDep: n} = e;
    t && (t.nextDep = n,
    e.prevDep = void 0),
    n && (n.prevDep = t,
    e.nextDep = void 0)
}
let ft = !0;
const mc = [];
function Xt() {
    mc.push(ft),
    ft = !1
}
function Zt() {
    const e = mc.pop();
    ft = e === void 0 ? !0 : e
}
function hi(e) {
    const {cleanup: t} = e;
    if (e.cleanup = void 0,
    t) {
        const n = ve;
        ve = void 0;
        try {
            t()
        } finally {
            ve = n
        }
    }
}
let Zn = 0;
class tf {
    constructor(t, n) {
        this.sub = t,
        this.dep = n,
        this.version = n.version,
        this.nextDep = this.prevDep = this.nextSub = this.prevSub = this.prevActiveLink = void 0
    }
}
class Lo {
    constructor(t) {
        this.computed = t,
        this.version = 0,
        this.activeLink = void 0,
        this.subs = void 0,
        this.map = void 0,
        this.key = void 0,
        this.sc = 0
    }
    track(t) {
        if (!ve || !ft || ve === this.computed)
            return;
        let n = this.activeLink;
        if (n === void 0 || n.sub !== ve)
            n = this.activeLink = new tf(ve,this),
            ve.deps ? (n.prevDep = ve.depsTail,
            ve.depsTail.nextDep = n,
            ve.depsTail = n) : ve.deps = ve.depsTail = n,
            yc(n);
        else if (n.version === -1 && (n.version = this.version,
        n.nextDep)) {
            const r = n.nextDep;
            r.prevDep = n.prevDep,
            n.prevDep && (n.prevDep.nextDep = r),
            n.prevDep = ve.depsTail,
            n.nextDep = void 0,
            ve.depsTail.nextDep = n,
            ve.depsTail = n,
            ve.deps === n && (ve.deps = r)
        }
        return n
    }
    trigger(t) {
        this.version++,
        Zn++,
        this.notify(t)
    }
    notify(t) {
        Bo();
        try {
            for (let n = this.subs; n; n = n.prevSub)
                n.sub.notify() && n.sub.dep.notify()
        } finally {
            ko()
        }
    }
}
function yc(e) {
    if (e.dep.sc++,
    e.sub.flags & 4) {
        const t = e.dep.computed;
        if (t && !e.dep.subs) {
            t.flags |= 20;
            for (let r = t.deps; r; r = r.nextDep)
                yc(r)
        }
        const n = e.dep.subs;
        n !== e && (e.prevSub = n,
        n && (n.nextSub = e)),
        e.dep.subs = e
    }
}
const Dr = new WeakMap
  , cn = Symbol("")
  , no = Symbol("")
  , Qn = Symbol("");
function $e(e, t, n) {
    if (ft && ve) {
        let r = Dr.get(e);
        r || Dr.set(e, r = new Map);
        let s = r.get(n);
        s || (r.set(n, s = new Lo),
        s.map = r,
        s.key = n),
        s.track()
    }
}
function It(e, t, n, r, s, o) {
    const i = Dr.get(e);
    if (!i) {
        Zn++;
        return
    }
    const l = c => {
        c && c.trigger()
    }
    ;
    if (Bo(),
    t === "clear")
        i.forEach(l);
    else {
        const c = ee(e)
          , u = c && Io(n);
        if (c && n === "length") {
            const a = Number(r);
            i.forEach( (f, d) => {
                (d === "length" || d === Qn || !Gt(d) && d >= a) && l(f)
            }
            )
        } else
            switch ((n !== void 0 || i.has(void 0)) && l(i.get(n)),
            u && l(i.get(Qn)),
            t) {
            case "add":
                c ? u && l(i.get("length")) : (l(i.get(cn)),
                Sn(e) && l(i.get(no)));
                break;
            case "delete":
                c || (l(i.get(cn)),
                Sn(e) && l(i.get(no)));
                break;
            case "set":
                Sn(e) && l(i.get(cn));
                break
            }
    }
    ko()
}
function nf(e, t) {
    const n = Dr.get(e);
    return n && n.get(t)
}
function pn(e) {
    const t = ue(e);
    return t === e ? t : ($e(t, "iterate", Qn),
    ot(e) ? t : t.map(Fe))
}
function rs(e) {
    return $e(e = ue(e), "iterate", Qn),
    e
}
const rf = {
    __proto__: null,
    [Symbol.iterator]() {
        return Bs(this, Symbol.iterator, Fe)
    },
    concat(...e) {
        return pn(this).concat(...e.map(t => ee(t) ? pn(t) : t))
    },
    entries() {
        return Bs(this, "entries", e => (e[1] = Fe(e[1]),
        e))
    },
    every(e, t) {
        return Tt(this, "every", e, t, void 0, arguments)
    },
    filter(e, t) {
        return Tt(this, "filter", e, t, n => n.map(Fe), arguments)
    },
    find(e, t) {
        return Tt(this, "find", e, t, Fe, arguments)
    },
    findIndex(e, t) {
        return Tt(this, "findIndex", e, t, void 0, arguments)
    },
    findLast(e, t) {
        return Tt(this, "findLast", e, t, Fe, arguments)
    },
    findLastIndex(e, t) {
        return Tt(this, "findLastIndex", e, t, void 0, arguments)
    },
    forEach(e, t) {
        return Tt(this, "forEach", e, t, void 0, arguments)
    },
    includes(...e) {
        return ks(this, "includes", e)
    },
    indexOf(...e) {
        return ks(this, "indexOf", e)
    },
    join(e) {
        return pn(this).join(e)
    },
    lastIndexOf(...e) {
        return ks(this, "lastIndexOf", e)
    },
    map(e, t) {
        return Tt(this, "map", e, t, void 0, arguments)
    },
    pop() {
        return Bn(this, "pop")
    },
    push(...e) {
        return Bn(this, "push", e)
    },
    reduce(e, ...t) {
        return pi(this, "reduce", e, t)
    },
    reduceRight(e, ...t) {
        return pi(this, "reduceRight", e, t)
    },
    shift() {
        return Bn(this, "shift")
    },
    some(e, t) {
        return Tt(this, "some", e, t, void 0, arguments)
    },
    splice(...e) {
        return Bn(this, "splice", e)
    },
    toReversed() {
        return pn(this).toReversed()
    },
    toSorted(e) {
        return pn(this).toSorted(e)
    },
    toSpliced(...e) {
        return pn(this).toSpliced(...e)
    },
    unshift(...e) {
        return Bn(this, "unshift", e)
    },
    values() {
        return Bs(this, "values", Fe)
    }
};
function Bs(e, t, n) {
    const r = rs(e)
      , s = r[t]();
    return r !== e && !ot(e) && (s._next = s.next,
    s.next = () => {
        const o = s._next();
        return o.value && (o.value = n(o.value)),
        o
    }
    ),
    s
}
const sf = Array.prototype;
function Tt(e, t, n, r, s, o) {
    const i = rs(e)
      , l = i !== e && !ot(e)
      , c = i[t];
    if (c !== sf[t]) {
        const f = c.apply(e, o);
        return l ? Fe(f) : f
    }
    let u = n;
    i !== e && (l ? u = function(f, d) {
        return n.call(this, Fe(f), d, e)
    }
    : n.length > 2 && (u = function(f, d) {
        return n.call(this, f, d, e)
    }
    ));
    const a = c.call(i, u, r);
    return l && s ? s(a) : a
}
function pi(e, t, n, r) {
    const s = rs(e);
    let o = n;
    return s !== e && (ot(e) ? n.length > 3 && (o = function(i, l, c) {
        return n.call(this, i, l, c, e)
    }
    ) : o = function(i, l, c) {
        return n.call(this, i, Fe(l), c, e)
    }
    ),
    s[t](o, ...r)
}
function ks(e, t, n) {
    const r = ue(e);
    $e(r, "iterate", Qn);
    const s = r[t](...n);
    return (s === -1 || s === !1) && Do(n[0]) ? (n[0] = ue(n[0]),
    r[t](...n)) : s
}
function Bn(e, t, n=[]) {
    Xt(),
    Bo();
    const r = ue(e)[t].apply(e, n);
    return ko(),
    Zt(),
    r
}
const of = Ao("__proto__,__v_isRef,__isVue")
  , bc = new Set(Object.getOwnPropertyNames(Symbol).filter(e => e !== "arguments" && e !== "caller").map(e => Symbol[e]).filter(Gt));
function lf(e) {
    Gt(e) || (e = String(e));
    const t = ue(this);
    return $e(t, "has", e),
    t.hasOwnProperty(e)
}
class vc {
    constructor(t=!1, n=!1) {
        this._isReadonly = t,
        this._isShallow = n
    }
    get(t, n, r) {
        if (n === "__v_skip")
            return t.__v_skip;
        const s = this._isReadonly
          , o = this._isShallow;
        if (n === "__v_isReactive")
            return !s;
        if (n === "__v_isReadonly")
            return s;
        if (n === "__v_isShallow")
            return o;
        if (n === "__v_raw")
            return r === (s ? o ? yf : Ec : o ? _c : Sc).get(t) || Object.getPrototypeOf(t) === Object.getPrototypeOf(r) ? t : void 0;
        const i = ee(t);
        if (!s) {
            let c;
            if (i && (c = rf[n]))
                return c;
            if (n === "hasOwnProperty")
                return lf
        }
        const l = Reflect.get(t, n, Ae(t) ? t : r);
        return (Gt(n) ? bc.has(n) : of(n)) || (s || $e(t, "get", n),
        o) ? l : Ae(l) ? i && Io(n) ? l : l.value : Se(l) ? s ? Cc(l) : Ke(l) : l
    }
}
class wc extends vc {
    constructor(t=!1) {
        super(!1, t)
    }
    set(t, n, r, s) {
        let o = t[n];
        if (!this._isShallow) {
            const c = dn(o);
            if (!ot(r) && !dn(r) && (o = ue(o),
            r = ue(r)),
            !ee(t) && Ae(o) && !Ae(r))
                return c ? !1 : (o.value = r,
                !0)
        }
        const i = ee(t) && Io(n) ? Number(n) < t.length : he(t, n)
          , l = Reflect.set(t, n, r, Ae(t) ? t : s);
        return t === ue(s) && (i ? qt(r, o) && It(t, "set", n, r) : It(t, "add", n, r)),
        l
    }
    deleteProperty(t, n) {
        const r = he(t, n);
        t[n];
        const s = Reflect.deleteProperty(t, n);
        return s && r && It(t, "delete", n, void 0),
        s
    }
    has(t, n) {
        const r = Reflect.has(t, n);
        return (!Gt(n) || !bc.has(n)) && $e(t, "has", n),
        r
    }
    ownKeys(t) {
        return $e(t, "iterate", ee(t) ? "length" : cn),
        Reflect.ownKeys(t)
    }
}
class cf extends vc {
    constructor(t=!1) {
        super(!0, t)
    }
    set(t, n) {
        return !0
    }
    deleteProperty(t, n) {
        return !0
    }
}
const af = new wc
  , uf = new cf
  , ff = new wc(!0);
const ro = e => e
  , wr = e => Reflect.getPrototypeOf(e);
function df(e, t, n) {
    return function(...r) {
        const s = this.__v_raw
          , o = ue(s)
          , i = Sn(o)
          , l = e === "entries" || e === Symbol.iterator && i
          , c = e === "keys" && i
          , u = s[e](...r)
          , a = n ? ro : t ? so : Fe;
        return !t && $e(o, "iterate", c ? no : cn),
        {
            next() {
                const {value: f, done: d} = u.next();
                return d ? {
                    value: f,
                    done: d
                } : {
                    value: l ? [a(f[0]), a(f[1])] : a(f),
                    done: d
                }
            },
            [Symbol.iterator]() {
                return this
            }
        }
    }
}
function Sr(e) {
    return function(...t) {
        return e === "delete" ? !1 : e === "clear" ? void 0 : this
    }
}
function hf(e, t) {
    const n = {
        get(s) {
            const o = this.__v_raw
              , i = ue(o)
              , l = ue(s);
            e || (qt(s, l) && $e(i, "get", s),
            $e(i, "get", l));
            const {has: c} = wr(i)
              , u = t ? ro : e ? so : Fe;
            if (c.call(i, s))
                return u(o.get(s));
            if (c.call(i, l))
                return u(o.get(l));
            o !== i && o.get(s)
        },
        get size() {
            const s = this.__v_raw;
            return !e && $e(ue(s), "iterate", cn),
            Reflect.get(s, "size", s)
        },
        has(s) {
            const o = this.__v_raw
              , i = ue(o)
              , l = ue(s);
            return e || (qt(s, l) && $e(i, "has", s),
            $e(i, "has", l)),
            s === l ? o.has(s) : o.has(s) || o.has(l)
        },
        forEach(s, o) {
            const i = this
              , l = i.__v_raw
              , c = ue(l)
              , u = t ? ro : e ? so : Fe;
            return !e && $e(c, "iterate", cn),
            l.forEach( (a, f) => s.call(o, u(a), u(f), i))
        }
    };
    return Le(n, e ? {
        add: Sr("add"),
        set: Sr("set"),
        delete: Sr("delete"),
        clear: Sr("clear")
    } : {
        add(s) {
            !t && !ot(s) && !dn(s) && (s = ue(s));
            const o = ue(this);
            return wr(o).has.call(o, s) || (o.add(s),
            It(o, "add", s, s)),
            this
        },
        set(s, o) {
            !t && !ot(o) && !dn(o) && (o = ue(o));
            const i = ue(this)
              , {has: l, get: c} = wr(i);
            let u = l.call(i, s);
            u || (s = ue(s),
            u = l.call(i, s));
            const a = c.call(i, s);
            return i.set(s, o),
            u ? qt(o, a) && It(i, "set", s, o) : It(i, "add", s, o),
            this
        },
        delete(s) {
            const o = ue(this)
              , {has: i, get: l} = wr(o);
            let c = i.call(o, s);
            c || (s = ue(s),
            c = i.call(o, s)),
            l && l.call(o, s);
            const u = o.delete(s);
            return c && It(o, "delete", s, void 0),
            u
        },
        clear() {
            const s = ue(this)
              , o = s.size !== 0
              , i = s.clear();
            return o && It(s, "clear", void 0, void 0),
            i
        }
    }),
    ["keys", "values", "entries", Symbol.iterator].forEach(s => {
        n[s] = df(s, e, t)
    }
    ),
    n
}
function $o(e, t) {
    const n = hf(e, t);
    return (r, s, o) => s === "__v_isReactive" ? !e : s === "__v_isReadonly" ? e : s === "__v_raw" ? r : Reflect.get(he(n, s) && s in r ? n : r, s, o)
}
const pf = {
    get: $o(!1, !1)
}
  , gf = {
    get: $o(!1, !0)
}
  , mf = {
    get: $o(!0, !1)
};
const Sc = new WeakMap
  , _c = new WeakMap
  , Ec = new WeakMap
  , yf = new WeakMap;
function bf(e) {
    switch (e) {
    case "Object":
    case "Array":
        return 1;
    case "Map":
    case "Set":
    case "WeakMap":
    case "WeakSet":
        return 2;
    default:
        return 0
    }
}
function vf(e) {
    return e.__v_skip || !Object.isExtensible(e) ? 0 : bf(ju(e))
}
function Ke(e) {
    return dn(e) ? e : Fo(e, !1, af, pf, Sc)
}
function xc(e) {
    return Fo(e, !1, ff, gf, _c)
}
function Cc(e) {
    return Fo(e, !0, uf, mf, Ec)
}
function Fo(e, t, n, r, s) {
    if (!Se(e) || e.__v_raw && !(t && e.__v_isReactive))
        return e;
    const o = s.get(e);
    if (o)
        return o;
    const i = vf(e);
    if (i === 0)
        return e;
    const l = new Proxy(e,i === 2 ? r : n);
    return s.set(e, l),
    l
}
function Kt(e) {
    return dn(e) ? Kt(e.__v_raw) : !!(e && e.__v_isReactive)
}
function dn(e) {
    return !!(e && e.__v_isReadonly)
}
function ot(e) {
    return !!(e && e.__v_isShallow)
}
function Do(e) {
    return e ? !!e.__v_raw : !1
}
function ue(e) {
    const t = e && e.__v_raw;
    return t ? ue(t) : e
}
function Mo(e) {
    return !he(e, "__v_skip") && Object.isExtensible(e) && rc(e, "__v_skip", !0),
    e
}
const Fe = e => Se(e) ? Ke(e) : e
  , so = e => Se(e) ? Cc(e) : e;
function Ae(e) {
    return e ? e.__v_isRef === !0 : !1
}
function se(e) {
    return Tc(e, !1)
}
function wf(e) {
    return Tc(e, !0)
}
function Tc(e, t) {
    return Ae(e) ? e : new Sf(e,t)
}
class Sf {
    constructor(t, n) {
        this.dep = new Lo,
        this.__v_isRef = !0,
        this.__v_isShallow = !1,
        this._rawValue = n ? t : ue(t),
        this._value = n ? t : Fe(t),
        this.__v_isShallow = n
    }
    get value() {
        return this.dep.track(),
        this._value
    }
    set value(t) {
        const n = this._rawValue
          , r = this.__v_isShallow || ot(t) || dn(t);
        t = r ? t : ue(t),
        qt(t, n) && (this._rawValue = t,
        this._value = r ? t : Fe(t),
        this.dep.trigger())
    }
}
function Et(e) {
    return Ae(e) ? e.value : e
}
const _f = {
    get: (e, t, n) => t === "__v_raw" ? e : Et(Reflect.get(e, t, n)),
    set: (e, t, n, r) => {
        const s = e[t];
        return Ae(s) && !Ae(n) ? (s.value = n,
        !0) : Reflect.set(e, t, n, r)
    }
};
function Rc(e) {
    return Kt(e) ? e : new Proxy(e,_f)
}
function Ef(e) {
    const t = ee(e) ? new Array(e.length) : {};
    for (const n in e)
        t[n] = Cf(e, n);
    return t
}
class xf {
    constructor(t, n, r) {
        this._object = t,
        this._key = n,
        this._defaultValue = r,
        this.__v_isRef = !0,
        this._value = void 0
    }
    get value() {
        const t = this._object[this._key];
        return this._value = t === void 0 ? this._defaultValue : t
    }
    set value(t) {
        this._object[this._key] = t
    }
    get dep() {
        return nf(ue(this._object), this._key)
    }
}
function Cf(e, t, n) {
    const r = e[t];
    return Ae(r) ? r : new xf(e,t,n)
}
class Tf {
    constructor(t, n, r) {
        this.fn = t,
        this.setter = n,
        this._value = void 0,
        this.dep = new Lo(this),
        this.__v_isRef = !0,
        this.deps = void 0,
        this.depsTail = void 0,
        this.flags = 16,
        this.globalVersion = Zn - 1,
        this.next = void 0,
        this.effect = this,
        this.__v_isReadonly = !n,
        this.isSSR = r
    }
    notify() {
        if (this.flags |= 16,
        !(this.flags & 8) && ve !== this)
            return dc(this, !0),
            !0
    }
    get value() {
        const t = this.dep.track();
        return gc(this),
        t && (t.version = this.dep.version),
        this._value
    }
    set value(t) {
        this.setter && this.setter(t)
    }
}
function Rf(e, t, n=!1) {
    let r, s;
    return re(e) ? r = e : (r = e.get,
    s = e.set),
    new Tf(r,s,n)
}
const _r = {}
  , Mr = new WeakMap;
let sn;
function Af(e, t=!1, n=sn) {
    if (n) {
        let r = Mr.get(n);
        r || Mr.set(n, r = []),
        r.push(e)
    }
}
function Of(e, t, n=ye) {
    const {immediate: r, deep: s, once: o, scheduler: i, augmentJob: l, call: c} = n
      , u = O => s ? O : ot(O) || s === !1 || s === 0 ? Bt(O, 1) : Bt(O);
    let a, f, d, g, y = !1, b = !1;
    if (Ae(e) ? (f = () => e.value,
    y = ot(e)) : Kt(e) ? (f = () => u(e),
    y = !0) : ee(e) ? (b = !0,
    y = e.some(O => Kt(O) || ot(O)),
    f = () => e.map(O => {
        if (Ae(O))
            return O.value;
        if (Kt(O))
            return u(O);
        if (re(O))
            return c ? c(O, 2) : O()
    }
    )) : re(e) ? t ? f = c ? () => c(e, 2) : e : f = () => {
        if (d) {
            Xt();
            try {
                d()
            } finally {
                Zt()
            }
        }
        const O = sn;
        sn = a;
        try {
            return c ? c(e, 3, [g]) : e(g)
        } finally {
            sn = O
        }
    }
    : f = _t,
    t && s) {
        const O = f
          , k = s === !0 ? 1 / 0 : s;
        f = () => Bt(O(), k)
    }
    const w = ac()
      , _ = () => {
        a.stop(),
        w && w.active && Po(w.effects, a)
    }
    ;
    if (o && t) {
        const O = t;
        t = (...k) => {
            O(...k),
            _()
        }
    }
    let S = b ? new Array(e.length).fill(_r) : _r;
    const I = O => {
        if (!(!(a.flags & 1) || !a.dirty && !O))
            if (t) {
                const k = a.run();
                if (s || y || (b ? k.some( (F, V) => qt(F, S[V])) : qt(k, S))) {
                    d && d();
                    const F = sn;
                    sn = a;
                    try {
                        const V = [k, S === _r ? void 0 : b && S[0] === _r ? [] : S, g];
                        c ? c(t, 3, V) : t(...V),
                        S = k
                    } finally {
                        sn = F
                    }
                }
            } else
                a.run()
    }
    ;
    return l && l(I),
    a = new uc(f),
    a.scheduler = i ? () => i(I, !1) : I,
    g = O => Af(O, !1, a),
    d = a.onStop = () => {
        const O = Mr.get(a);
        if (O) {
            if (c)
                c(O, 4);
            else
                for (const k of O)
                    k();
            Mr.delete(a)
        }
    }
    ,
    t ? r ? I(!0) : S = a.run() : i ? i(I.bind(null, !0), !0) : a.run(),
    _.pause = a.pause.bind(a),
    _.resume = a.resume.bind(a),
    _.stop = _,
    _
}
function Bt(e, t=1 / 0, n) {
    if (t <= 0 || !Se(e) || e.__v_skip || (n = n || new Set,
    n.has(e)))
        return e;
    if (n.add(e),
    t--,
    Ae(e))
        Bt(e.value, t, n);
    else if (ee(e))
        for (let r = 0; r < e.length; r++)
            Bt(e[r], t, n);
    else if (Ql(e) || Sn(e))
        e.forEach(r => {
            Bt(r, t, n)
        }
        );
    else if (nc(e)) {
        for (const r in e)
            Bt(e[r], t, n);
        for (const r of Object.getOwnPropertySymbols(e))
            Object.prototype.propertyIsEnumerable.call(e, r) && Bt(e[r], t, n)
    }
    return e
}
/**
* @vue/runtime-core v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/
function dr(e, t, n, r) {
    try {
        return r ? e(...r) : e()
    } catch (s) {
        ss(s, t, n)
    }
}
function dt(e, t, n, r) {
    if (re(e)) {
        const s = dr(e, t, n, r);
        return s && ec(s) && s.catch(o => {
            ss(o, t, n)
        }
        ),
        s
    }
    if (ee(e)) {
        const s = [];
        for (let o = 0; o < e.length; o++)
            s.push(dt(e[o], t, n, r));
        return s
    }
}
function ss(e, t, n, r=!0) {
    const s = t ? t.vnode : null
      , {errorHandler: o, throwUnhandledErrorInProduction: i} = t && t.appContext.config || ye;
    if (t) {
        let l = t.parent;
        const c = t.proxy
          , u = `https://vuejs.org/error-reference/#runtime-${n}`;
        for (; l; ) {
            const a = l.ec;
            if (a) {
                for (let f = 0; f < a.length; f++)
                    if (a[f](e, c, u) === !1)
                        return
            }
            l = l.parent
        }
        if (o) {
            Xt(),
            dr(o, null, 10, [e, c, u]),
            Zt();
            return
        }
    }
    Pf(e, n, s, r, i)
}
function Pf(e, t, n, r=!0, s=!1) {
    if (s)
        throw e;
    console.error(e)
}
const ze = [];
let wt = -1;
const _n = [];
let jt = null
  , bn = 0;
const Ac = Promise.resolve();
let jr = null;
function Pe(e) {
    const t = jr || Ac;
    return e ? t.then(this ? e.bind(this) : e) : t
}
function If(e) {
    let t = wt + 1
      , n = ze.length;
    for (; t < n; ) {
        const r = t + n >>> 1
          , s = ze[r]
          , o = er(s);
        o < e || o === e && s.flags & 2 ? t = r + 1 : n = r
    }
    return t
}
function jo(e) {
    if (!(e.flags & 1)) {
        const t = er(e)
          , n = ze[ze.length - 1];
        !n || !(e.flags & 2) && t >= er(n) ? ze.push(e) : ze.splice(If(t), 0, e),
        e.flags |= 1,
        Oc()
    }
}
function Oc() {
    jr || (jr = Ac.then(Ic))
}
function Bf(e) {
    ee(e) ? _n.push(...e) : jt && e.id === -1 ? jt.splice(bn + 1, 0, e) : e.flags & 1 || (_n.push(e),
    e.flags |= 1),
    Oc()
}
function gi(e, t, n=wt + 1) {
    for (; n < ze.length; n++) {
        const r = ze[n];
        if (r && r.flags & 2) {
            if (e && r.id !== e.uid)
                continue;
            ze.splice(n, 1),
            n--,
            r.flags & 4 && (r.flags &= -2),
            r(),
            r.flags & 4 || (r.flags &= -2)
        }
    }
}
function Pc(e) {
    if (_n.length) {
        const t = [...new Set(_n)].sort( (n, r) => er(n) - er(r));
        if (_n.length = 0,
        jt) {
            jt.push(...t);
            return
        }
        for (jt = t,
        bn = 0; bn < jt.length; bn++) {
            const n = jt[bn];
            n.flags & 4 && (n.flags &= -2),
            n.flags & 8 || n(),
            n.flags &= -2
        }
        jt = null,
        bn = 0
    }
}
const er = e => e.id == null ? e.flags & 2 ? -1 : 1 / 0 : e.id;
function Ic(e) {
    try {
        for (wt = 0; wt < ze.length; wt++) {
            const t = ze[wt];
            t && !(t.flags & 8) && (t.flags & 4 && (t.flags &= -2),
            dr(t, t.i, t.i ? 15 : 14),
            t.flags & 4 || (t.flags &= -2))
        }
    } finally {
        for (; wt < ze.length; wt++) {
            const t = ze[wt];
            t && (t.flags &= -2)
        }
        wt = -1,
        ze.length = 0,
        Pc(),
        jr = null,
        (ze.length || _n.length) && Ic()
    }
}
let qe = null
  , Bc = null;
function Hr(e) {
    const t = qe;
    return qe = e,
    Bc = e && e.type.__scopeId || null,
    t
}
function kf(e, t=qe, n) {
    if (!t || e._n)
        return e;
    const r = (...s) => {
        r._d && Oi(-1);
        const o = Hr(t);
        let i;
        try {
            i = e(...s)
        } finally {
            Hr(o),
            r._d && Oi(1)
        }
        return i
    }
    ;
    return r._n = !0,
    r._c = !0,
    r._d = !0,
    r
}
function Ho(e, t) {
    if (qe === null)
        return e;
    const n = us(qe)
      , r = e.dirs || (e.dirs = []);
    for (let s = 0; s < t.length; s++) {
        let[o,i,l,c=ye] = t[s];
        o && (re(o) && (o = {
            mounted: o,
            updated: o
        }),
        o.deep && Bt(i),
        r.push({
            dir: o,
            instance: n,
            value: i,
            oldValue: void 0,
            arg: l,
            modifiers: c
        }))
    }
    return e
}
function en(e, t, n, r) {
    const s = e.dirs
      , o = t && t.dirs;
    for (let i = 0; i < s.length; i++) {
        const l = s[i];
        o && (l.oldValue = o[i].value);
        let c = l.dir[r];
        c && (Xt(),
        dt(c, n, 8, [e.el, l, e, t]),
        Zt())
    }
}
const kc = Symbol("_vte")
  , Nc = e => e.__isTeleport
  , Vn = e => e && (e.disabled || e.disabled === "")
  , mi = e => e && (e.defer || e.defer === "")
  , yi = e => typeof SVGElement < "u" && e instanceof SVGElement
  , bi = e => typeof MathMLElement == "function" && e instanceof MathMLElement
  , oo = (e, t) => {
    const n = e && e.to;
    return Ee(n) ? t ? t(n) : null : n
}
  , Lc = {
    name: "Teleport",
    __isTeleport: !0,
    process(e, t, n, r, s, o, i, l, c, u) {
        const {mc: a, pc: f, pbc: d, o: {insert: g, querySelector: y, createText: b, createComment: w}} = u
          , _ = Vn(t.props);
        let {shapeFlag: S, children: I, dynamicChildren: O} = t;
        if (e == null) {
            const k = t.el = b("")
              , F = t.anchor = b("");
            g(k, n, r),
            g(F, n, r);
            const V = (B, z) => {
                S & 16 && (s && s.isCE && (s.ce._teleportTarget = B),
                a(I, B, z, s, o, i, l, c))
            }
              , U = () => {
                const B = t.target = oo(t.props, y)
                  , z = Fc(B, t, b, g);
                B && (i !== "svg" && yi(B) ? i = "svg" : i !== "mathml" && bi(B) && (i = "mathml"),
                _ || (V(B, z),
                Pr(t, !1)))
            }
            ;
            _ && (V(n, F),
            Pr(t, !0)),
            mi(t.props) ? Ue( () => {
                U(),
                t.el.__isMounted = !0
            }
            , o) : U()
        } else {
            if (mi(t.props) && !e.el.__isMounted) {
                Ue( () => {
                    Lc.process(e, t, n, r, s, o, i, l, c, u),
                    delete e.el.__isMounted
                }
                , o);
                return
            }
            t.el = e.el,
            t.targetStart = e.targetStart;
            const k = t.anchor = e.anchor
              , F = t.target = e.target
              , V = t.targetAnchor = e.targetAnchor
              , U = Vn(e.props)
              , B = U ? n : F
              , z = U ? k : V;
            if (i === "svg" || yi(F) ? i = "svg" : (i === "mathml" || bi(F)) && (i = "mathml"),
            O ? (d(e.dynamicChildren, O, B, s, o, i, l),
            Vo(e, t, !0)) : c || f(e, t, B, z, s, o, i, l, !1),
            _)
                U ? t.props && e.props && t.props.to !== e.props.to && (t.props.to = e.props.to) : Er(t, n, k, u, 1);
            else if ((t.props && t.props.to) !== (e.props && e.props.to)) {
                const X = t.target = oo(t.props, y);
                X && Er(t, X, null, u, 0)
            } else
                U && Er(t, F, V, u, 1);
            Pr(t, _)
        }
    },
    remove(e, t, n, {um: r, o: {remove: s}}, o) {
        const {shapeFlag: i, children: l, anchor: c, targetStart: u, targetAnchor: a, target: f, props: d} = e;
        if (f && (s(u),
        s(a)),
        o && s(c),
        i & 16) {
            const g = o || !Vn(d);
            for (let y = 0; y < l.length; y++) {
                const b = l[y];
                r(b, t, n, g, !!b.dynamicChildren)
            }
        }
    },
    move: Er,
    hydrate: Nf
};
function Er(e, t, n, {o: {insert: r}, m: s}, o=2) {
    o === 0 && r(e.targetAnchor, t, n);
    const {el: i, anchor: l, shapeFlag: c, children: u, props: a} = e
      , f = o === 2;
    if (f && r(i, t, n),
    (!f || Vn(a)) && c & 16)
        for (let d = 0; d < u.length; d++)
            s(u[d], t, n, 2);
    f && r(l, t, n)
}
function Nf(e, t, n, r, s, o, {o: {nextSibling: i, parentNode: l, querySelector: c, insert: u, createText: a}}, f) {
    const d = t.target = oo(t.props, c);
    if (d) {
        const g = Vn(t.props)
          , y = d._lpa || d.firstChild;
        if (t.shapeFlag & 16)
            if (g)
                t.anchor = f(i(e), t, l(e), n, r, s, o),
                t.targetStart = y,
                t.targetAnchor = y && i(y);
            else {
                t.anchor = i(e);
                let b = y;
                for (; b; ) {
                    if (b && b.nodeType === 8) {
                        if (b.data === "teleport start anchor")
                            t.targetStart = b;
                        else if (b.data === "teleport anchor") {
                            t.targetAnchor = b,
                            d._lpa = t.targetAnchor && i(t.targetAnchor);
                            break
                        }
                    }
                    b = i(b)
                }
                t.targetAnchor || Fc(d, t, a, u),
                f(y && i(y), t, d, n, r, s, o)
            }
        Pr(t, g)
    }
    return t.anchor && i(t.anchor)
}
const $c = Lc;
function Pr(e, t) {
    const n = e.ctx;
    if (n && n.ut) {
        let r, s;
        for (t ? (r = e.el,
        s = e.anchor) : (r = e.targetStart,
        s = e.targetAnchor); r && r !== s; )
            r.nodeType === 1 && r.setAttribute("data-v-owner", n.uid),
            r = r.nextSibling;
        n.ut()
    }
}
function Fc(e, t, n, r) {
    const s = t.targetStart = n("")
      , o = t.targetAnchor = n("");
    return s[kc] = o,
    e && (r(s, e),
    r(o, e)),
    o
}
const Ht = Symbol("_leaveCb")
  , xr = Symbol("_enterCb");
function Lf() {
    const e = {
        isMounted: !1,
        isLeaving: !1,
        isUnmounting: !1,
        leavingVNodes: new Map
    };
    return Ct( () => {
        e.isMounted = !0
    }
    ),
    pr( () => {
        e.isUnmounting = !0
    }
    ),
    e
}
const rt = [Function, Array]
  , Dc = {
    mode: String,
    appear: Boolean,
    persisted: Boolean,
    onBeforeEnter: rt,
    onEnter: rt,
    onAfterEnter: rt,
    onEnterCancelled: rt,
    onBeforeLeave: rt,
    onLeave: rt,
    onAfterLeave: rt,
    onLeaveCancelled: rt,
    onBeforeAppear: rt,
    onAppear: rt,
    onAfterAppear: rt,
    onAppearCancelled: rt
}
  , Mc = e => {
    const t = e.subTree;
    return t.component ? Mc(t.component) : t
}
  , $f = {
    name: "BaseTransition",
    props: Dc,
    setup(e, {slots: t}) {
        const n = Qt()
          , r = Lf();
        return () => {
            const s = t.default && Uc(t.default(), !0);
            if (!s || !s.length)
                return;
            const o = jc(s)
              , i = ue(e)
              , {mode: l} = i;
            if (r.isLeaving)
                return Ns(o);
            const c = vi(o);
            if (!c)
                return Ns(o);
            let u = io(c, i, r, n, f => u = f);
            c.type !== Ge && tr(c, u);
            let a = n.subTree && vi(n.subTree);
            if (a && a.type !== Ge && !on(c, a) && Mc(n).type !== Ge) {
                let f = io(a, i, r, n);
                if (tr(a, f),
                l === "out-in" && c.type !== Ge)
                    return r.isLeaving = !0,
                    f.afterLeave = () => {
                        r.isLeaving = !1,
                        n.job.flags & 8 || n.update(),
                        delete f.afterLeave,
                        a = void 0
                    }
                    ,
                    Ns(o);
                l === "in-out" && c.type !== Ge ? f.delayLeave = (d, g, y) => {
                    const b = Hc(r, a);
                    b[String(a.key)] = a,
                    d[Ht] = () => {
                        g(),
                        d[Ht] = void 0,
                        delete u.delayedLeave,
                        a = void 0
                    }
                    ,
                    u.delayedLeave = () => {
                        y(),
                        delete u.delayedLeave,
                        a = void 0
                    }
                }
                : a = void 0
            } else
                a && (a = void 0);
            return o
        }
    }
};
function jc(e) {
    let t = e[0];
    if (e.length > 1) {
        for (const n of e)
            if (n.type !== Ge) {
                t = n;
                break
            }
    }
    return t
}
const Ff = $f;
function Hc(e, t) {
    const {leavingVNodes: n} = e;
    let r = n.get(t.type);
    return r || (r = Object.create(null),
    n.set(t.type, r)),
    r
}
function io(e, t, n, r, s) {
    const {appear: o, mode: i, persisted: l=!1, onBeforeEnter: c, onEnter: u, onAfterEnter: a, onEnterCancelled: f, onBeforeLeave: d, onLeave: g, onAfterLeave: y, onLeaveCancelled: b, onBeforeAppear: w, onAppear: _, onAfterAppear: S, onAppearCancelled: I} = t
      , O = String(e.key)
      , k = Hc(n, e)
      , F = (B, z) => {
        B && dt(B, r, 9, z)
    }
      , V = (B, z) => {
        const X = z[1];
        F(B, z),
        ee(B) ? B.every($ => $.length <= 1) && X() : B.length <= 1 && X()
    }
      , U = {
        mode: i,
        persisted: l,
        beforeEnter(B) {
            let z = c;
            if (!n.isMounted)
                if (o)
                    z = w || c;
                else
                    return;
            B[Ht] && B[Ht](!0);
            const X = k[O];
            X && on(e, X) && X.el[Ht] && X.el[Ht](),
            F(z, [B])
        },
        enter(B) {
            let z = u
              , X = a
              , $ = f;
            if (!n.isMounted)
                if (o)
                    z = _ || u,
                    X = S || a,
                    $ = I || f;
                else
                    return;
            let ne = !1;
            const me = B[xr] = Te => {
                ne || (ne = !0,
                Te ? F($, [B]) : F(X, [B]),
                U.delayedLeave && U.delayedLeave(),
                B[xr] = void 0)
            }
            ;
            z ? V(z, [B, me]) : me()
        },
        leave(B, z) {
            const X = String(e.key);
            if (B[xr] && B[xr](!0),
            n.isUnmounting)
                return z();
            F(d, [B]);
            let $ = !1;
            const ne = B[Ht] = me => {
                $ || ($ = !0,
                z(),
                me ? F(b, [B]) : F(y, [B]),
                B[Ht] = void 0,
                k[X] === e && delete k[X])
            }
            ;
            k[X] = e,
            g ? V(g, [B, ne]) : ne()
        },
        clone(B) {
            const z = io(B, t, n, r, s);
            return s && s(z),
            z
        }
    };
    return U
}
function Ns(e) {
    if (os(e))
        return e = Yt(e),
        e.children = null,
        e
}
function vi(e) {
    if (!os(e))
        return Nc(e.type) && e.children ? jc(e.children) : e;
    const {shapeFlag: t, children: n} = e;
    if (n) {
        if (t & 16)
            return n[0];
        if (t & 32 && re(n.default))
            return n.default()
    }
}
function tr(e, t) {
    e.shapeFlag & 6 && e.component ? (e.transition = t,
    tr(e.component.subTree, t)) : e.shapeFlag & 128 ? (e.ssContent.transition = t.clone(e.ssContent),
    e.ssFallback.transition = t.clone(e.ssFallback)) : e.transition = t
}
function Uc(e, t=!1, n) {
    let r = []
      , s = 0;
    for (let o = 0; o < e.length; o++) {
        let i = e[o];
        const l = n == null ? i.key : String(n) + String(i.key != null ? i.key : o);
        i.type === at ? (i.patchFlag & 128 && s++,
        r = r.concat(Uc(i.children, t, l))) : (t || i.type !== Ge) && r.push(l != null ? Yt(i, {
            key: l
        }) : i)
    }
    if (s > 1)
        for (let o = 0; o < r.length; o++)
            r[o].patchFlag = -2;
    return r
}
/*! #__NO_SIDE_EFFECTS__ */
function Ce(e, t) {
    return re(e) ? Le({
        name: e.name
    }, t, {
        setup: e
    }) : e
}
function Vc(e) {
    e.ids = [e.ids[0] + e.ids[2]++ + "-", 0, 0]
}
function Ur(e, t, n, r, s=!1) {
    if (ee(e)) {
        e.forEach( (y, b) => Ur(y, t && (ee(t) ? t[b] : t), n, r, s));
        return
    }
    if (zn(r) && !s) {
        r.shapeFlag & 512 && r.type.__asyncResolved && r.component.subTree.component && Ur(e, t, n, r.component.subTree);
        return
    }
    const o = r.shapeFlag & 4 ? us(r.component) : r.el
      , i = s ? null : o
      , {i: l, r: c} = e
      , u = t && t.r
      , a = l.refs === ye ? l.refs = {} : l.refs
      , f = l.setupState
      , d = ue(f)
      , g = f === ye ? () => !1 : y => he(d, y);
    if (u != null && u !== c && (Ee(u) ? (a[u] = null,
    g(u) && (f[u] = null)) : Ae(u) && (u.value = null)),
    re(c))
        dr(c, l, 12, [i, a]);
    else {
        const y = Ee(c)
          , b = Ae(c);
        if (y || b) {
            const w = () => {
                if (e.f) {
                    const _ = y ? g(c) ? f[c] : a[c] : c.value;
                    s ? ee(_) && Po(_, o) : ee(_) ? _.includes(o) || _.push(o) : y ? (a[c] = [o],
                    g(c) && (f[c] = a[c])) : (c.value = [o],
                    e.k && (a[e.k] = c.value))
                } else
                    y ? (a[c] = i,
                    g(c) && (f[c] = i)) : b && (c.value = i,
                    e.k && (a[e.k] = i))
            }
            ;
            i ? (w.id = -1,
            Ue(w, n)) : w()
        }
    }
}
es().requestIdleCallback;
es().cancelIdleCallback;
const zn = e => !!e.type.__asyncLoader
  , os = e => e.type.__isKeepAlive;
function hr(e, t) {
    zc(e, "a", t)
}
function Tn(e, t) {
    zc(e, "da", t)
}
function zc(e, t, n=Ne) {
    const r = e.__wdc || (e.__wdc = () => {
        let s = n;
        for (; s; ) {
            if (s.isDeactivated)
                return;
            s = s.parent
        }
        return e()
    }
    );
    if (is(t, r, n),
    n) {
        let s = n.parent;
        for (; s && s.parent; )
            os(s.parent.vnode) && Df(r, t, n, s),
            s = s.parent
    }
}
function Df(e, t, n, r) {
    const s = is(t, e, r, !0);
    ls( () => {
        Po(r[t], s)
    }
    , n)
}
function is(e, t, n=Ne, r=!1) {
    if (n) {
        const s = n[e] || (n[e] = [])
          , o = t.__weh || (t.__weh = (...i) => {
            Xt();
            const l = gr(n)
              , c = dt(t, n, e, i);
            return l(),
            Zt(),
            c
        }
        );
        return r ? s.unshift(o) : s.push(o),
        o
    }
}
const Lt = e => (t, n=Ne) => {
    (!sr || e === "sp") && is(e, (...r) => t(...r), n)
}
  , Mf = Lt("bm")
  , Ct = Lt("m")
  , qc = Lt("bu")
  , jf = Lt("u")
  , pr = Lt("bum")
  , ls = Lt("um")
  , Hf = Lt("sp")
  , Uf = Lt("rtg")
  , Vf = Lt("rtc");
function zf(e, t=Ne) {
    is("ec", e, t)
}
const qf = "components";
function Kf(e, t) {
    return Jf(qf, e, !0, t) || e
}
const Wf = Symbol.for("v-ndc");
function Jf(e, t, n=!0, r=!1) {
    const s = qe || Ne;
    if (s) {
        const o = s.type;
        {
            const l = Ld(o, !1);
            if (l && (l === t || l === it(t) || l === Qr(it(t))))
                return o
        }
        const i = wi(s[e] || o[e], t) || wi(s.appContext[e], t);
        return !i && r ? o : i
    }
}
function wi(e, t) {
    return e && (e[t] || e[it(t)] || e[Qr(it(t))])
}
function Yb(e, t, n, r) {
    let s;
    const o = n
      , i = ee(e);
    if (i || Ee(e)) {
        const l = i && Kt(e);
        let c = !1;
        l && (c = !ot(e),
        e = rs(e)),
        s = new Array(e.length);
        for (let u = 0, a = e.length; u < a; u++)
            s[u] = t(c ? Fe(e[u]) : e[u], u, void 0, o)
    } else if (typeof e == "number") {
        s = new Array(e);
        for (let l = 0; l < e; l++)
            s[l] = t(l + 1, l, void 0, o)
    } else if (Se(e))
        if (e[Symbol.iterator])
            s = Array.from(e, (l, c) => t(l, c, void 0, o));
        else {
            const l = Object.keys(e);
            s = new Array(l.length);
            for (let c = 0, u = l.length; c < u; c++) {
                const a = l[c];
                s[c] = t(e[a], a, c, o)
            }
        }
    else
        s = [];
    return s
}
const lo = e => e ? pa(e) ? us(e) : lo(e.parent) : null
  , qn = Le(Object.create(null), {
    $: e => e,
    $el: e => e.vnode.el,
    $data: e => e.data,
    $props: e => e.props,
    $attrs: e => e.attrs,
    $slots: e => e.slots,
    $refs: e => e.refs,
    $parent: e => lo(e.parent),
    $root: e => lo(e.root),
    $host: e => e.ce,
    $emit: e => e.emit,
    $options: e => Wc(e),
    $forceUpdate: e => e.f || (e.f = () => {
        jo(e.update)
    }
    ),
    $nextTick: e => e.n || (e.n = Pe.bind(e.proxy)),
    $watch: e => yd.bind(e)
})
  , Ls = (e, t) => e !== ye && !e.__isScriptSetup && he(e, t)
  , Yf = {
    get({_: e}, t) {
        if (t === "__v_skip")
            return !0;
        const {ctx: n, setupState: r, data: s, props: o, accessCache: i, type: l, appContext: c} = e;
        let u;
        if (t[0] !== "$") {
            const g = i[t];
            if (g !== void 0)
                switch (g) {
                case 1:
                    return r[t];
                case 2:
                    return s[t];
                case 4:
                    return n[t];
                case 3:
                    return o[t]
                }
            else {
                if (Ls(r, t))
                    return i[t] = 1,
                    r[t];
                if (s !== ye && he(s, t))
                    return i[t] = 2,
                    s[t];
                if ((u = e.propsOptions[0]) && he(u, t))
                    return i[t] = 3,
                    o[t];
                if (n !== ye && he(n, t))
                    return i[t] = 4,
                    n[t];
                co && (i[t] = 0)
            }
        }
        const a = qn[t];
        let f, d;
        if (a)
            return t === "$attrs" && $e(e.attrs, "get", ""),
            a(e);
        if ((f = l.__cssModules) && (f = f[t]))
            return f;
        if (n !== ye && he(n, t))
            return i[t] = 4,
            n[t];
        if (d = c.config.globalProperties,
        he(d, t))
            return d[t]
    },
    set({_: e}, t, n) {
        const {data: r, setupState: s, ctx: o} = e;
        return Ls(s, t) ? (s[t] = n,
        !0) : r !== ye && he(r, t) ? (r[t] = n,
        !0) : he(e.props, t) || t[0] === "$" && t.slice(1)in e ? !1 : (o[t] = n,
        !0)
    },
    has({_: {data: e, setupState: t, accessCache: n, ctx: r, appContext: s, propsOptions: o}}, i) {
        let l;
        return !!n[i] || e !== ye && he(e, i) || Ls(t, i) || (l = o[0]) && he(l, i) || he(r, i) || he(qn, i) || he(s.config.globalProperties, i)
    },
    defineProperty(e, t, n) {
        return n.get != null ? e._.accessCache[t] = 0 : he(n, "value") && this.set(e, t, n.value, null),
        Reflect.defineProperty(e, t, n)
    }
};
function Si(e) {
    return ee(e) ? e.reduce( (t, n) => (t[n] = null,
    t), {}) : e
}
let co = !0;
function Gf(e) {
    const t = Wc(e)
      , n = e.proxy
      , r = e.ctx;
    co = !1,
    t.beforeCreate && _i(t.beforeCreate, e, "bc");
    const {data: s, computed: o, methods: i, watch: l, provide: c, inject: u, created: a, beforeMount: f, mounted: d, beforeUpdate: g, updated: y, activated: b, deactivated: w, beforeDestroy: _, beforeUnmount: S, destroyed: I, unmounted: O, render: k, renderTracked: F, renderTriggered: V, errorCaptured: U, serverPrefetch: B, expose: z, inheritAttrs: X, components: $, directives: ne, filters: me} = t;
    if (u && Xf(u, r, null),
    i)
        for (const Q in i) {
            const le = i[Q];
            re(le) && (r[Q] = le.bind(n))
        }
    if (s) {
        const Q = s.call(n, n);
        Se(Q) && (e.data = Ke(Q))
    }
    if (co = !0,
    o)
        for (const Q in o) {
            const le = o[Q]
              , je = re(le) ? le.bind(n, n) : re(le.get) ? le.get.bind(n, n) : _t
              , Je = !re(le) && re(le.set) ? le.set.bind(n) : _t
              , K = G({
                get: je,
                set: Je
            });
            Object.defineProperty(r, Q, {
                enumerable: !0,
                configurable: !0,
                get: () => K.value,
                set: ie => K.value = ie
            })
        }
    if (l)
        for (const Q in l)
            Kc(l[Q], r, n, Q);
    if (c) {
        const Q = re(c) ? c.call(n) : c;
        Reflect.ownKeys(Q).forEach(le => {
            Wt(le, Q[le])
        }
        )
    }
    a && _i(a, e, "c");
    function ae(Q, le) {
        ee(le) ? le.forEach(je => Q(je.bind(n))) : le && Q(le.bind(n))
    }
    if (ae(Mf, f),
    ae(Ct, d),
    ae(qc, g),
    ae(jf, y),
    ae(hr, b),
    ae(Tn, w),
    ae(zf, U),
    ae(Vf, F),
    ae(Uf, V),
    ae(pr, S),
    ae(ls, O),
    ae(Hf, B),
    ee(z))
        if (z.length) {
            const Q = e.exposed || (e.exposed = {});
            z.forEach(le => {
                Object.defineProperty(Q, le, {
                    get: () => n[le],
                    set: je => n[le] = je
                })
            }
            )
        } else
            e.exposed || (e.exposed = {});
    k && e.render === _t && (e.render = k),
    X != null && (e.inheritAttrs = X),
    $ && (e.components = $),
    ne && (e.directives = ne),
    B && Vc(e)
}
function Xf(e, t, n=_t) {
    ee(e) && (e = ao(e));
    for (const r in e) {
        const s = e[r];
        let o;
        Se(s) ? "default"in s ? o = Xe(s.from || r, s.default, !0) : o = Xe(s.from || r) : o = Xe(s),
        Ae(o) ? Object.defineProperty(t, r, {
            enumerable: !0,
            configurable: !0,
            get: () => o.value,
            set: i => o.value = i
        }) : t[r] = o
    }
}
function _i(e, t, n) {
    dt(ee(e) ? e.map(r => r.bind(t.proxy)) : e.bind(t.proxy), t, n)
}
function Kc(e, t, n, r) {
    let s = r.includes(".") ? oa(n, r) : () => n[r];
    if (Ee(e)) {
        const o = t[e];
        re(o) && we(s, o)
    } else if (re(e))
        we(s, e.bind(n));
    else if (Se(e))
        if (ee(e))
            e.forEach(o => Kc(o, t, n, r));
        else {
            const o = re(e.handler) ? e.handler.bind(n) : t[e.handler];
            re(o) && we(s, o, e)
        }
}
function Wc(e) {
    const t = e.type
      , {mixins: n, extends: r} = t
      , {mixins: s, optionsCache: o, config: {optionMergeStrategies: i}} = e.appContext
      , l = o.get(t);
    let c;
    return l ? c = l : !s.length && !n && !r ? c = t : (c = {},
    s.length && s.forEach(u => Vr(c, u, i, !0)),
    Vr(c, t, i)),
    Se(t) && o.set(t, c),
    c
}
function Vr(e, t, n, r=!1) {
    const {mixins: s, extends: o} = t;
    o && Vr(e, o, n, !0),
    s && s.forEach(i => Vr(e, i, n, !0));
    for (const i in t)
        if (!(r && i === "expose")) {
            const l = Zf[i] || n && n[i];
            e[i] = l ? l(e[i], t[i]) : t[i]
        }
    return e
}
const Zf = {
    data: Ei,
    props: xi,
    emits: xi,
    methods: Dn,
    computed: Dn,
    beforeCreate: He,
    created: He,
    beforeMount: He,
    mounted: He,
    beforeUpdate: He,
    updated: He,
    beforeDestroy: He,
    beforeUnmount: He,
    destroyed: He,
    unmounted: He,
    activated: He,
    deactivated: He,
    errorCaptured: He,
    serverPrefetch: He,
    components: Dn,
    directives: Dn,
    watch: ed,
    provide: Ei,
    inject: Qf
};
function Ei(e, t) {
    return t ? e ? function() {
        return Le(re(e) ? e.call(this, this) : e, re(t) ? t.call(this, this) : t)
    }
    : t : e
}
function Qf(e, t) {
    return Dn(ao(e), ao(t))
}
function ao(e) {
    if (ee(e)) {
        const t = {};
        for (let n = 0; n < e.length; n++)
            t[e[n]] = e[n];
        return t
    }
    return e
}
function He(e, t) {
    return e ? [...new Set([].concat(e, t))] : t
}
function Dn(e, t) {
    return e ? Le(Object.create(null), e, t) : t
}
function xi(e, t) {
    return e ? ee(e) && ee(t) ? [...new Set([...e, ...t])] : Le(Object.create(null), Si(e), Si(t ?? {})) : t
}
function ed(e, t) {
    if (!e)
        return t;
    if (!t)
        return e;
    const n = Le(Object.create(null), e);
    for (const r in t)
        n[r] = He(e[r], t[r]);
    return n
}
function Jc() {
    return {
        app: null,
        config: {
            isNativeTag: Du,
            performance: !1,
            globalProperties: {},
            optionMergeStrategies: {},
            errorHandler: void 0,
            warnHandler: void 0,
            compilerOptions: {}
        },
        mixins: [],
        components: {},
        directives: {},
        provides: Object.create(null),
        optionsCache: new WeakMap,
        propsCache: new WeakMap,
        emitsCache: new WeakMap
    }
}
let td = 0;
function nd(e, t) {
    return function(r, s=null) {
        re(r) || (r = Le({}, r)),
        s != null && !Se(s) && (s = null);
        const o = Jc()
          , i = new WeakSet
          , l = [];
        let c = !1;
        const u = o.app = {
            _uid: td++,
            _component: r,
            _props: s,
            _container: null,
            _context: o,
            _instance: null,
            version: Fd,
            get config() {
                return o.config
            },
            set config(a) {},
            use(a, ...f) {
                return i.has(a) || (a && re(a.install) ? (i.add(a),
                a.install(u, ...f)) : re(a) && (i.add(a),
                a(u, ...f))),
                u
            },
            mixin(a) {
                return o.mixins.includes(a) || o.mixins.push(a),
                u
            },
            component(a, f) {
                return f ? (o.components[a] = f,
                u) : o.components[a]
            },
            directive(a, f) {
                return f ? (o.directives[a] = f,
                u) : o.directives[a]
            },
            mount(a, f, d) {
                if (!c) {
                    const g = u._ceVNode || P(r, s);
                    return g.appContext = o,
                    d === !0 ? d = "svg" : d === !1 && (d = void 0),
                    e(g, a, d),
                    c = !0,
                    u._container = a,
                    a.__vue_app__ = u,
                    us(g.component)
                }
            },
            onUnmount(a) {
                l.push(a)
            },
            unmount() {
                c && (dt(l, u._instance, 16),
                e(null, u._container),
                delete u._container.__vue_app__)
            },
            provide(a, f) {
                return o.provides[a] = f,
                u
            },
            runWithContext(a) {
                const f = an;
                an = u;
                try {
                    return a()
                } finally {
                    an = f
                }
            }
        };
        return u
    }
}
let an = null;
function Wt(e, t) {
    if (Ne) {
        let n = Ne.provides;
        const r = Ne.parent && Ne.parent.provides;
        r === n && (n = Ne.provides = Object.create(r)),
        n[e] = t
    }
}
function Xe(e, t, n=!1) {
    const r = Ne || qe;
    if (r || an) {
        const s = an ? an._context.provides : r ? r.parent == null ? r.vnode.appContext && r.vnode.appContext.provides : r.parent.provides : void 0;
        if (s && e in s)
            return s[e];
        if (arguments.length > 1)
            return n && re(t) ? t.call(r && r.proxy) : t
    }
}
function rd() {
    return !!(Ne || qe || an)
}
const Yc = {}
  , Gc = () => Object.create(Yc)
  , Xc = e => Object.getPrototypeOf(e) === Yc;
function sd(e, t, n, r=!1) {
    const s = {}
      , o = Gc();
    e.propsDefaults = Object.create(null),
    Zc(e, t, s, o);
    for (const i in e.propsOptions[0])
        i in s || (s[i] = void 0);
    n ? e.props = r ? s : xc(s) : e.type.props ? e.props = s : e.props = o,
    e.attrs = o
}
function od(e, t, n, r) {
    const {props: s, attrs: o, vnode: {patchFlag: i}} = e
      , l = ue(s)
      , [c] = e.propsOptions;
    let u = !1;
    if ((r || i > 0) && !(i & 16)) {
        if (i & 8) {
            const a = e.vnode.dynamicProps;
            for (let f = 0; f < a.length; f++) {
                let d = a[f];
                if (cs(e.emitsOptions, d))
                    continue;
                const g = t[d];
                if (c)
                    if (he(o, d))
                        g !== o[d] && (o[d] = g,
                        u = !0);
                    else {
                        const y = it(d);
                        s[y] = uo(c, l, y, g, e, !1)
                    }
                else
                    g !== o[d] && (o[d] = g,
                    u = !0)
            }
        }
    } else {
        Zc(e, t, s, o) && (u = !0);
        let a;
        for (const f in l)
            (!t || !he(t, f) && ((a = Nt(f)) === f || !he(t, a))) && (c ? n && (n[f] !== void 0 || n[a] !== void 0) && (s[f] = uo(c, l, f, void 0, e, !0)) : delete s[f]);
        if (o !== l)
            for (const f in o)
                (!t || !he(t, f)) && (delete o[f],
                u = !0)
    }
    u && It(e.attrs, "set", "")
}
function Zc(e, t, n, r) {
    const [s,o] = e.propsOptions;
    let i = !1, l;
    if (t)
        for (let c in t) {
            if (jn(c))
                continue;
            const u = t[c];
            let a;
            s && he(s, a = it(c)) ? !o || !o.includes(a) ? n[a] = u : (l || (l = {}))[a] = u : cs(e.emitsOptions, c) || (!(c in r) || u !== r[c]) && (r[c] = u,
            i = !0)
        }
    if (o) {
        const c = ue(n)
          , u = l || ye;
        for (let a = 0; a < o.length; a++) {
            const f = o[a];
            n[f] = uo(s, c, f, u[f], e, !he(u, f))
        }
    }
    return i
}
function uo(e, t, n, r, s, o) {
    const i = e[n];
    if (i != null) {
        const l = he(i, "default");
        if (l && r === void 0) {
            const c = i.default;
            if (i.type !== Function && !i.skipFactory && re(c)) {
                const {propsDefaults: u} = s;
                if (n in u)
                    r = u[n];
                else {
                    const a = gr(s);
                    r = u[n] = c.call(null, t),
                    a()
                }
            } else
                r = c;
            s.ce && s.ce._setProp(n, r)
        }
        i[0] && (o && !l ? r = !1 : i[1] && (r === "" || r === Nt(n)) && (r = !0))
    }
    return r
}
const id = new WeakMap;
function Qc(e, t, n=!1) {
    const r = n ? id : t.propsCache
      , s = r.get(e);
    if (s)
        return s;
    const o = e.props
      , i = {}
      , l = [];
    let c = !1;
    if (!re(e)) {
        const a = f => {
            c = !0;
            const [d,g] = Qc(f, t, !0);
            Le(i, d),
            g && l.push(...g)
        }
        ;
        !n && t.mixins.length && t.mixins.forEach(a),
        e.extends && a(e.extends),
        e.mixins && e.mixins.forEach(a)
    }
    if (!o && !c)
        return Se(e) && r.set(e, wn),
        wn;
    if (ee(o))
        for (let a = 0; a < o.length; a++) {
            const f = it(o[a]);
            Ci(f) && (i[f] = ye)
        }
    else if (o)
        for (const a in o) {
            const f = it(a);
            if (Ci(f)) {
                const d = o[a]
                  , g = i[f] = ee(d) || re(d) ? {
                    type: d
                } : Le({}, d)
                  , y = g.type;
                let b = !1
                  , w = !0;
                if (ee(y))
                    for (let _ = 0; _ < y.length; ++_) {
                        const S = y[_]
                          , I = re(S) && S.name;
                        if (I === "Boolean") {
                            b = !0;
                            break
                        } else
                            I === "String" && (w = !1)
                    }
                else
                    b = re(y) && y.name === "Boolean";
                g[0] = b,
                g[1] = w,
                (b || he(g, "default")) && l.push(f)
            }
        }
    const u = [i, l];
    return Se(e) && r.set(e, u),
    u
}
function Ci(e) {
    return e[0] !== "$" && !jn(e)
}
const ea = e => e[0] === "_" || e === "$stable"
  , Uo = e => ee(e) ? e.map(St) : [St(e)]
  , ld = (e, t, n) => {
    if (t._n)
        return t;
    const r = kf( (...s) => Uo(t(...s)), n);
    return r._c = !1,
    r
}
  , ta = (e, t, n) => {
    const r = e._ctx;
    for (const s in e) {
        if (ea(s))
            continue;
        const o = e[s];
        if (re(o))
            t[s] = ld(s, o, r);
        else if (o != null) {
            const i = Uo(o);
            t[s] = () => i
        }
    }
}
  , na = (e, t) => {
    const n = Uo(t);
    e.slots.default = () => n
}
  , ra = (e, t, n) => {
    for (const r in t)
        (n || r !== "_") && (e[r] = t[r])
}
  , cd = (e, t, n) => {
    const r = e.slots = Gc();
    if (e.vnode.shapeFlag & 32) {
        const s = t._;
        s ? (ra(r, t, n),
        n && rc(r, "_", s, !0)) : ta(t, r)
    } else
        t && na(e, t)
}
  , ad = (e, t, n) => {
    const {vnode: r, slots: s} = e;
    let o = !0
      , i = ye;
    if (r.shapeFlag & 32) {
        const l = t._;
        l ? n && l === 1 ? o = !1 : ra(s, t, n) : (o = !t.$stable,
        ta(t, s)),
        i = t
    } else
        t && (na(e, t),
        i = {
            default: 1
        });
    if (o)
        for (const l in s)
            !ea(l) && i[l] == null && delete s[l]
}
  , Ue = xd;
function ud(e) {
    return fd(e)
}
function fd(e, t) {
    const n = es();
    n.__VUE__ = !0;
    const {insert: r, remove: s, patchProp: o, createElement: i, createText: l, createComment: c, setText: u, setElementText: a, parentNode: f, nextSibling: d, setScopeId: g=_t, insertStaticContent: y} = e
      , b = (h, m, v, C=null, A=null, R=null, j=void 0, M=null, D=!!m.dynamicChildren) => {
        if (h === m)
            return;
        h && !on(h, m) && (C = p(h),
        ie(h, A, R, !0),
        h = null),
        m.patchFlag === -2 && (D = !1,
        m.dynamicChildren = null);
        const {type: N, ref: Z, shapeFlag: q} = m;
        switch (N) {
        case as:
            w(h, m, v, C);
            break;
        case Ge:
            _(h, m, v, C);
            break;
        case Fs:
            h == null && S(m, v, C, j);
            break;
        case at:
            $(h, m, v, C, A, R, j, M, D);
            break;
        default:
            q & 1 ? k(h, m, v, C, A, R, j, M, D) : q & 6 ? ne(h, m, v, C, A, R, j, M, D) : (q & 64 || q & 128) && N.process(h, m, v, C, A, R, j, M, D, L)
        }
        Z != null && A && Ur(Z, h && h.ref, R, m || h, !m)
    }
      , w = (h, m, v, C) => {
        if (h == null)
            r(m.el = l(m.children), v, C);
        else {
            const A = m.el = h.el;
            m.children !== h.children && u(A, m.children)
        }
    }
      , _ = (h, m, v, C) => {
        h == null ? r(m.el = c(m.children || ""), v, C) : m.el = h.el
    }
      , S = (h, m, v, C) => {
        [h.el,h.anchor] = y(h.children, m, v, C, h.el, h.anchor)
    }
      , I = ({el: h, anchor: m}, v, C) => {
        let A;
        for (; h && h !== m; )
            A = d(h),
            r(h, v, C),
            h = A;
        r(m, v, C)
    }
      , O = ({el: h, anchor: m}) => {
        let v;
        for (; h && h !== m; )
            v = d(h),
            s(h),
            h = v;
        s(m)
    }
      , k = (h, m, v, C, A, R, j, M, D) => {
        m.type === "svg" ? j = "svg" : m.type === "math" && (j = "mathml"),
        h == null ? F(m, v, C, A, R, j, M, D) : B(h, m, A, R, j, M, D)
    }
      , F = (h, m, v, C, A, R, j, M) => {
        let D, N;
        const {props: Z, shapeFlag: q, transition: Y, dirs: te} = h;
        if (D = h.el = i(h.type, R, Z && Z.is, Z),
        q & 8 ? a(D, h.children) : q & 16 && U(h.children, D, null, C, A, $s(h, R), j, M),
        te && en(h, null, C, "created"),
        V(D, h, h.scopeId, j, C),
        Z) {
            for (const be in Z)
                be !== "value" && !jn(be) && o(D, be, null, Z[be], R, C);
            "value"in Z && o(D, "value", null, Z.value, R),
            (N = Z.onVnodeBeforeMount) && bt(N, C, h)
        }
        te && en(h, null, C, "beforeMount");
        const ce = dd(A, Y);
        ce && Y.beforeEnter(D),
        r(D, m, v),
        ((N = Z && Z.onVnodeMounted) || ce || te) && Ue( () => {
            N && bt(N, C, h),
            ce && Y.enter(D),
            te && en(h, null, C, "mounted")
        }
        , A)
    }
      , V = (h, m, v, C, A) => {
        if (v && g(h, v),
        C)
            for (let R = 0; R < C.length; R++)
                g(h, C[R]);
        if (A) {
            let R = A.subTree;
            if (m === R || la(R.type) && (R.ssContent === m || R.ssFallback === m)) {
                const j = A.vnode;
                V(h, j, j.scopeId, j.slotScopeIds, A.parent)
            }
        }
    }
      , U = (h, m, v, C, A, R, j, M, D=0) => {
        for (let N = D; N < h.length; N++) {
            const Z = h[N] = M ? Ut(h[N]) : St(h[N]);
            b(null, Z, m, v, C, A, R, j, M)
        }
    }
      , B = (h, m, v, C, A, R, j) => {
        const M = m.el = h.el;
        let {patchFlag: D, dynamicChildren: N, dirs: Z} = m;
        D |= h.patchFlag & 16;
        const q = h.props || ye
          , Y = m.props || ye;
        let te;
        if (v && tn(v, !1),
        (te = Y.onVnodeBeforeUpdate) && bt(te, v, m, h),
        Z && en(m, h, v, "beforeUpdate"),
        v && tn(v, !0),
        (q.innerHTML && Y.innerHTML == null || q.textContent && Y.textContent == null) && a(M, ""),
        N ? z(h.dynamicChildren, N, M, v, C, $s(m, A), R) : j || le(h, m, M, null, v, C, $s(m, A), R, !1),
        D > 0) {
            if (D & 16)
                X(M, q, Y, v, A);
            else if (D & 2 && q.class !== Y.class && o(M, "class", null, Y.class, A),
            D & 4 && o(M, "style", q.style, Y.style, A),
            D & 8) {
                const ce = m.dynamicProps;
                for (let be = 0; be < ce.length; be++) {
                    const ge = ce[be]
                      , Qe = q[ge]
                      , Ye = Y[ge];
                    (Ye !== Qe || ge === "value") && o(M, ge, Qe, Ye, A, v)
                }
            }
            D & 1 && h.children !== m.children && a(M, m.children)
        } else
            !j && N == null && X(M, q, Y, v, A);
        ((te = Y.onVnodeUpdated) || Z) && Ue( () => {
            te && bt(te, v, m, h),
            Z && en(m, h, v, "updated")
        }
        , C)
    }
      , z = (h, m, v, C, A, R, j) => {
        for (let M = 0; M < m.length; M++) {
            const D = h[M]
              , N = m[M]
              , Z = D.el && (D.type === at || !on(D, N) || D.shapeFlag & 70) ? f(D.el) : v;
            b(D, N, Z, null, C, A, R, j, !0)
        }
    }
      , X = (h, m, v, C, A) => {
        if (m !== v) {
            if (m !== ye)
                for (const R in m)
                    !jn(R) && !(R in v) && o(h, R, m[R], null, A, C);
            for (const R in v) {
                if (jn(R))
                    continue;
                const j = v[R]
                  , M = m[R];
                j !== M && R !== "value" && o(h, R, M, j, A, C)
            }
            "value"in v && o(h, "value", m.value, v.value, A)
        }
    }
      , $ = (h, m, v, C, A, R, j, M, D) => {
        const N = m.el = h ? h.el : l("")
          , Z = m.anchor = h ? h.anchor : l("");
        let {patchFlag: q, dynamicChildren: Y, slotScopeIds: te} = m;
        te && (M = M ? M.concat(te) : te),
        h == null ? (r(N, v, C),
        r(Z, v, C),
        U(m.children || [], v, Z, A, R, j, M, D)) : q > 0 && q & 64 && Y && h.dynamicChildren ? (z(h.dynamicChildren, Y, v, A, R, j, M),
        (m.key != null || A && m === A.subTree) && Vo(h, m, !0)) : le(h, m, v, Z, A, R, j, M, D)
    }
      , ne = (h, m, v, C, A, R, j, M, D) => {
        m.slotScopeIds = M,
        h == null ? m.shapeFlag & 512 ? A.ctx.activate(m, v, C, j, D) : me(m, v, C, A, R, j, D) : Te(h, m, D)
    }
      , me = (h, m, v, C, A, R, j) => {
        const M = h.component = Pd(h, C, A);
        if (os(h) && (M.ctx.renderer = L),
        Id(M, !1, j),
        M.asyncDep) {
            if (A && A.registerDep(M, ae, j),
            !h.el) {
                const D = M.subTree = P(Ge);
                _(null, D, m, v)
            }
        } else
            ae(M, h, m, v, A, R, j)
    }
      , Te = (h, m, v) => {
        const C = m.component = h.component;
        if (_d(h, m, v))
            if (C.asyncDep && !C.asyncResolved) {
                Q(C, m, v);
                return
            } else
                C.next = m,
                C.update();
        else
            m.el = h.el,
            C.vnode = m
    }
      , ae = (h, m, v, C, A, R, j) => {
        const M = () => {
            if (h.isMounted) {
                let {next: q, bu: Y, u: te, parent: ce, vnode: be} = h;
                {
                    const mt = sa(h);
                    if (mt) {
                        q && (q.el = be.el,
                        Q(h, q, j)),
                        mt.asyncDep.then( () => {
                            h.isUnmounted || M()
                        }
                        );
                        return
                    }
                }
                let ge = q, Qe;
                tn(h, !1),
                q ? (q.el = be.el,
                Q(h, q, j)) : q = be,
                Y && Os(Y),
                (Qe = q.props && q.props.onVnodeBeforeUpdate) && bt(Qe, ce, q, be),
                tn(h, !0);
                const Ye = Ri(h)
                  , gt = h.subTree;
                h.subTree = Ye,
                b(gt, Ye, f(gt.el), p(gt), h, A, R),
                q.el = Ye.el,
                ge === null && Ed(h, Ye.el),
                te && Ue(te, A),
                (Qe = q.props && q.props.onVnodeUpdated) && Ue( () => bt(Qe, ce, q, be), A)
            } else {
                let q;
                const {el: Y, props: te} = m
                  , {bm: ce, m: be, parent: ge, root: Qe, type: Ye} = h
                  , gt = zn(m);
                tn(h, !1),
                ce && Os(ce),
                !gt && (q = te && te.onVnodeBeforeMount) && bt(q, ge, m),
                tn(h, !0);
                {
                    Qe.ce && Qe.ce._injectChildStyle(Ye);
                    const mt = h.subTree = Ri(h);
                    b(null, mt, v, C, h, A, R),
                    m.el = mt.el
                }
                if (be && Ue(be, A),
                !gt && (q = te && te.onVnodeMounted)) {
                    const mt = m;
                    Ue( () => bt(q, ge, mt), A)
                }
                (m.shapeFlag & 256 || ge && zn(ge.vnode) && ge.vnode.shapeFlag & 256) && h.a && Ue(h.a, A),
                h.isMounted = !0,
                m = v = C = null
            }
        }
        ;
        h.scope.on();
        const D = h.effect = new uc(M);
        h.scope.off();
        const N = h.update = D.run.bind(D)
          , Z = h.job = D.runIfDirty.bind(D);
        Z.i = h,
        Z.id = h.uid,
        D.scheduler = () => jo(Z),
        tn(h, !0),
        N()
    }
      , Q = (h, m, v) => {
        m.component = h;
        const C = h.vnode.props;
        h.vnode = m,
        h.next = null,
        od(h, m.props, C, v),
        ad(h, m.children, v),
        Xt(),
        gi(h),
        Zt()
    }
      , le = (h, m, v, C, A, R, j, M, D=!1) => {
        const N = h && h.children
          , Z = h ? h.shapeFlag : 0
          , q = m.children
          , {patchFlag: Y, shapeFlag: te} = m;
        if (Y > 0) {
            if (Y & 128) {
                Je(N, q, v, C, A, R, j, M, D);
                return
            } else if (Y & 256) {
                je(N, q, v, C, A, R, j, M, D);
                return
            }
        }
        te & 8 ? (Z & 16 && W(N, A, R),
        q !== N && a(v, q)) : Z & 16 ? te & 16 ? Je(N, q, v, C, A, R, j, M, D) : W(N, A, R, !0) : (Z & 8 && a(v, ""),
        te & 16 && U(q, v, C, A, R, j, M, D))
    }
      , je = (h, m, v, C, A, R, j, M, D) => {
        h = h || wn,
        m = m || wn;
        const N = h.length
          , Z = m.length
          , q = Math.min(N, Z);
        let Y;
        for (Y = 0; Y < q; Y++) {
            const te = m[Y] = D ? Ut(m[Y]) : St(m[Y]);
            b(h[Y], te, v, null, A, R, j, M, D)
        }
        N > Z ? W(h, A, R, !0, !1, q) : U(m, v, C, A, R, j, M, D, q)
    }
      , Je = (h, m, v, C, A, R, j, M, D) => {
        let N = 0;
        const Z = m.length;
        let q = h.length - 1
          , Y = Z - 1;
        for (; N <= q && N <= Y; ) {
            const te = h[N]
              , ce = m[N] = D ? Ut(m[N]) : St(m[N]);
            if (on(te, ce))
                b(te, ce, v, null, A, R, j, M, D);
            else
                break;
            N++
        }
        for (; N <= q && N <= Y; ) {
            const te = h[q]
              , ce = m[Y] = D ? Ut(m[Y]) : St(m[Y]);
            if (on(te, ce))
                b(te, ce, v, null, A, R, j, M, D);
            else
                break;
            q--,
            Y--
        }
        if (N > q) {
            if (N <= Y) {
                const te = Y + 1
                  , ce = te < Z ? m[te].el : C;
                for (; N <= Y; )
                    b(null, m[N] = D ? Ut(m[N]) : St(m[N]), v, ce, A, R, j, M, D),
                    N++
            }
        } else if (N > Y)
            for (; N <= q; )
                ie(h[N], A, R, !0),
                N++;
        else {
            const te = N
              , ce = N
              , be = new Map;
            for (N = ce; N <= Y; N++) {
                const et = m[N] = D ? Ut(m[N]) : St(m[N]);
                et.key != null && be.set(et.key, N)
            }
            let ge, Qe = 0;
            const Ye = Y - ce + 1;
            let gt = !1
              , mt = 0;
            const In = new Array(Ye);
            for (N = 0; N < Ye; N++)
                In[N] = 0;
            for (N = te; N <= q; N++) {
                const et = h[N];
                if (Qe >= Ye) {
                    ie(et, A, R, !0);
                    continue
                }
                let yt;
                if (et.key != null)
                    yt = be.get(et.key);
                else
                    for (ge = ce; ge <= Y; ge++)
                        if (In[ge - ce] === 0 && on(et, m[ge])) {
                            yt = ge;
                            break
                        }
                yt === void 0 ? ie(et, A, R, !0) : (In[yt - ce] = N + 1,
                yt >= mt ? mt = yt : gt = !0,
                b(et, m[yt], v, null, A, R, j, M, D),
                Qe++)
            }
            const ci = gt ? hd(In) : wn;
            for (ge = ci.length - 1,
            N = Ye - 1; N >= 0; N--) {
                const et = ce + N
                  , yt = m[et]
                  , ai = et + 1 < Z ? m[et + 1].el : C;
                In[N] === 0 ? b(null, yt, v, ai, A, R, j, M, D) : gt && (ge < 0 || N !== ci[ge] ? K(yt, v, ai, 2) : ge--)
            }
        }
    }
      , K = (h, m, v, C, A=null) => {
        const {el: R, type: j, transition: M, children: D, shapeFlag: N} = h;
        if (N & 6) {
            K(h.component.subTree, m, v, C);
            return
        }
        if (N & 128) {
            h.suspense.move(m, v, C);
            return
        }
        if (N & 64) {
            j.move(h, m, v, L);
            return
        }
        if (j === at) {
            r(R, m, v);
            for (let q = 0; q < D.length; q++)
                K(D[q], m, v, C);
            r(h.anchor, m, v);
            return
        }
        if (j === Fs) {
            I(h, m, v);
            return
        }
        if (C !== 2 && N & 1 && M)
            if (C === 0)
                M.beforeEnter(R),
                r(R, m, v),
                Ue( () => M.enter(R), A);
            else {
                const {leave: q, delayLeave: Y, afterLeave: te} = M
                  , ce = () => r(R, m, v)
                  , be = () => {
                    q(R, () => {
                        ce(),
                        te && te()
                    }
                    )
                }
                ;
                Y ? Y(R, ce, be) : be()
            }
        else
            r(R, m, v)
    }
      , ie = (h, m, v, C=!1, A=!1) => {
        const {type: R, props: j, ref: M, children: D, dynamicChildren: N, shapeFlag: Z, patchFlag: q, dirs: Y, cacheIndex: te} = h;
        if (q === -2 && (A = !1),
        M != null && Ur(M, null, v, h, !0),
        te != null && (m.renderCache[te] = void 0),
        Z & 256) {
            m.ctx.deactivate(h);
            return
        }
        const ce = Z & 1 && Y
          , be = !zn(h);
        let ge;
        if (be && (ge = j && j.onVnodeBeforeUnmount) && bt(ge, m, h),
        Z & 6)
            H(h.component, v, C);
        else {
            if (Z & 128) {
                h.suspense.unmount(v, C);
                return
            }
            ce && en(h, null, m, "beforeUnmount"),
            Z & 64 ? h.type.remove(h, m, v, L, C) : N && !N.hasOnce && (R !== at || q > 0 && q & 64) ? W(N, m, v, !1, !0) : (R === at && q & 384 || !A && Z & 16) && W(D, m, v),
            C && fe(h)
        }
        (be && (ge = j && j.onVnodeUnmounted) || ce) && Ue( () => {
            ge && bt(ge, m, h),
            ce && en(h, null, m, "unmounted")
        }
        , v)
    }
      , fe = h => {
        const {type: m, el: v, anchor: C, transition: A} = h;
        if (m === at) {
            xe(v, C);
            return
        }
        if (m === Fs) {
            O(h);
            return
        }
        const R = () => {
            s(v),
            A && !A.persisted && A.afterLeave && A.afterLeave()
        }
        ;
        if (h.shapeFlag & 1 && A && !A.persisted) {
            const {leave: j, delayLeave: M} = A
              , D = () => j(v, R);
            M ? M(h.el, R, D) : D()
        } else
            R()
    }
      , xe = (h, m) => {
        let v;
        for (; h !== m; )
            v = d(h),
            s(h),
            h = v;
        s(m)
    }
      , H = (h, m, v) => {
        const {bum: C, scope: A, job: R, subTree: j, um: M, m: D, a: N} = h;
        Ti(D),
        Ti(N),
        C && Os(C),
        A.stop(),
        R && (R.flags |= 8,
        ie(j, h, m, v)),
        M && Ue(M, m),
        Ue( () => {
            h.isUnmounted = !0
        }
        , m),
        m && m.pendingBranch && !m.isUnmounted && h.asyncDep && !h.asyncResolved && h.suspenseId === m.pendingId && (m.deps--,
        m.deps === 0 && m.resolve())
    }
      , W = (h, m, v, C=!1, A=!1, R=0) => {
        for (let j = R; j < h.length; j++)
            ie(h[j], m, v, C, A)
    }
      , p = h => {
        if (h.shapeFlag & 6)
            return p(h.component.subTree);
        if (h.shapeFlag & 128)
            return h.suspense.next();
        const m = d(h.anchor || h.el)
          , v = m && m[kc];
        return v ? d(v) : m
    }
    ;
    let T = !1;
    const x = (h, m, v) => {
        h == null ? m._vnode && ie(m._vnode, null, null, !0) : b(m._vnode || null, h, m, null, null, null, v),
        m._vnode = h,
        T || (T = !0,
        gi(),
        Pc(),
        T = !1)
    }
      , L = {
        p: b,
        um: ie,
        m: K,
        r: fe,
        mt: me,
        mc: U,
        pc: le,
        pbc: z,
        n: p,
        o: e
    };
    return {
        render: x,
        hydrate: void 0,
        createApp: nd(x)
    }
}
function $s({type: e, props: t}, n) {
    return n === "svg" && e === "foreignObject" || n === "mathml" && e === "annotation-xml" && t && t.encoding && t.encoding.includes("html") ? void 0 : n
}
function tn({effect: e, job: t}, n) {
    n ? (e.flags |= 32,
    t.flags |= 4) : (e.flags &= -33,
    t.flags &= -5)
}
function dd(e, t) {
    return (!e || e && !e.pendingBranch) && t && !t.persisted
}
function Vo(e, t, n=!1) {
    const r = e.children
      , s = t.children;
    if (ee(r) && ee(s))
        for (let o = 0; o < r.length; o++) {
            const i = r[o];
            let l = s[o];
            l.shapeFlag & 1 && !l.dynamicChildren && ((l.patchFlag <= 0 || l.patchFlag === 32) && (l = s[o] = Ut(s[o]),
            l.el = i.el),
            !n && l.patchFlag !== -2 && Vo(i, l)),
            l.type === as && (l.el = i.el)
        }
}
function hd(e) {
    const t = e.slice()
      , n = [0];
    let r, s, o, i, l;
    const c = e.length;
    for (r = 0; r < c; r++) {
        const u = e[r];
        if (u !== 0) {
            if (s = n[n.length - 1],
            e[s] < u) {
                t[r] = s,
                n.push(r);
                continue
            }
            for (o = 0,
            i = n.length - 1; o < i; )
                l = o + i >> 1,
                e[n[l]] < u ? o = l + 1 : i = l;
            u < e[n[o]] && (o > 0 && (t[r] = n[o - 1]),
            n[o] = r)
        }
    }
    for (o = n.length,
    i = n[o - 1]; o-- > 0; )
        n[o] = i,
        i = t[i];
    return n
}
function sa(e) {
    const t = e.subTree.component;
    if (t)
        return t.asyncDep && !t.asyncResolved ? t : sa(t)
}
function Ti(e) {
    if (e)
        for (let t = 0; t < e.length; t++)
            e[t].flags |= 8
}
const pd = Symbol.for("v-scx")
  , gd = () => Xe(pd);
function md(e, t) {
    return zo(e, null, t)
}
function we(e, t, n) {
    return zo(e, t, n)
}
function zo(e, t, n=ye) {
    const {immediate: r, deep: s, flush: o, once: i} = n
      , l = Le({}, n)
      , c = t && r || !t && o !== "post";
    let u;
    if (sr) {
        if (o === "sync") {
            const g = gd();
            u = g.__watcherHandles || (g.__watcherHandles = [])
        } else if (!c) {
            const g = () => {}
            ;
            return g.stop = _t,
            g.resume = _t,
            g.pause = _t,
            g
        }
    }
    const a = Ne;
    l.call = (g, y, b) => dt(g, a, y, b);
    let f = !1;
    o === "post" ? l.scheduler = g => {
        Ue(g, a && a.suspense)
    }
    : o !== "sync" && (f = !0,
    l.scheduler = (g, y) => {
        y ? g() : jo(g)
    }
    ),
    l.augmentJob = g => {
        t && (g.flags |= 4),
        f && (g.flags |= 2,
        a && (g.id = a.uid,
        g.i = a))
    }
    ;
    const d = Of(e, t, l);
    return sr && (u ? u.push(d) : c && d()),
    d
}
function yd(e, t, n) {
    const r = this.proxy
      , s = Ee(e) ? e.includes(".") ? oa(r, e) : () => r[e] : e.bind(r, r);
    let o;
    re(t) ? o = t : (o = t.handler,
    n = t);
    const i = gr(this)
      , l = zo(s, o.bind(r), n);
    return i(),
    l
}
function oa(e, t) {
    const n = t.split(".");
    return () => {
        let r = e;
        for (let s = 0; s < n.length && r; s++)
            r = r[n[s]];
        return r
    }
}
const bd = (e, t) => t === "modelValue" || t === "model-value" ? e.modelModifiers : e[`${t}Modifiers`] || e[`${it(t)}Modifiers`] || e[`${Nt(t)}Modifiers`];
function vd(e, t, ...n) {
    if (e.isUnmounted)
        return;
    const r = e.vnode.props || ye;
    let s = n;
    const o = t.startsWith("update:")
      , i = o && bd(r, t.slice(7));
    i && (i.trim && (s = n.map(a => Ee(a) ? a.trim() : a)),
    i.number && (s = n.map(Vu)));
    let l, c = r[l = As(t)] || r[l = As(it(t))];
    !c && o && (c = r[l = As(Nt(t))]),
    c && dt(c, e, 6, s);
    const u = r[l + "Once"];
    if (u) {
        if (!e.emitted)
            e.emitted = {};
        else if (e.emitted[l])
            return;
        e.emitted[l] = !0,
        dt(u, e, 6, s)
    }
}
function ia(e, t, n=!1) {
    const r = t.emitsCache
      , s = r.get(e);
    if (s !== void 0)
        return s;
    const o = e.emits;
    let i = {}
      , l = !1;
    if (!re(e)) {
        const c = u => {
            const a = ia(u, t, !0);
            a && (l = !0,
            Le(i, a))
        }
        ;
        !n && t.mixins.length && t.mixins.forEach(c),
        e.extends && c(e.extends),
        e.mixins && e.mixins.forEach(c)
    }
    return !o && !l ? (Se(e) && r.set(e, null),
    null) : (ee(o) ? o.forEach(c => i[c] = null) : Le(i, o),
    Se(e) && r.set(e, i),
    i)
}
function cs(e, t) {
    return !e || !Gr(t) ? !1 : (t = t.slice(2).replace(/Once$/, ""),
    he(e, t[0].toLowerCase() + t.slice(1)) || he(e, Nt(t)) || he(e, t))
}
function Ri(e) {
    const {type: t, vnode: n, proxy: r, withProxy: s, propsOptions: [o], slots: i, attrs: l, emit: c, render: u, renderCache: a, props: f, data: d, setupState: g, ctx: y, inheritAttrs: b} = e
      , w = Hr(e);
    let _, S;
    try {
        if (n.shapeFlag & 4) {
            const O = s || r
              , k = O;
            _ = St(u.call(k, O, a, f, g, d, y)),
            S = l
        } else {
            const O = t;
            _ = St(O.length > 1 ? O(f, {
                attrs: l,
                slots: i,
                emit: c
            }) : O(f, null)),
            S = t.props ? l : wd(l)
        }
    } catch (O) {
        Kn.length = 0,
        ss(O, e, 1),
        _ = P(Ge)
    }
    let I = _;
    if (S && b !== !1) {
        const O = Object.keys(S)
          , {shapeFlag: k} = I;
        O.length && k & 7 && (o && O.some(Oo) && (S = Sd(S, o)),
        I = Yt(I, S, !1, !0))
    }
    return n.dirs && (I = Yt(I, null, !1, !0),
    I.dirs = I.dirs ? I.dirs.concat(n.dirs) : n.dirs),
    n.transition && tr(I, n.transition),
    _ = I,
    Hr(w),
    _
}
const wd = e => {
    let t;
    for (const n in e)
        (n === "class" || n === "style" || Gr(n)) && ((t || (t = {}))[n] = e[n]);
    return t
}
  , Sd = (e, t) => {
    const n = {};
    for (const r in e)
        (!Oo(r) || !(r.slice(9)in t)) && (n[r] = e[r]);
    return n
}
;
function _d(e, t, n) {
    const {props: r, children: s, component: o} = e
      , {props: i, children: l, patchFlag: c} = t
      , u = o.emitsOptions;
    if (t.dirs || t.transition)
        return !0;
    if (n && c >= 0) {
        if (c & 1024)
            return !0;
        if (c & 16)
            return r ? Ai(r, i, u) : !!i;
        if (c & 8) {
            const a = t.dynamicProps;
            for (let f = 0; f < a.length; f++) {
                const d = a[f];
                if (i[d] !== r[d] && !cs(u, d))
                    return !0
            }
        }
    } else
        return (s || l) && (!l || !l.$stable) ? !0 : r === i ? !1 : r ? i ? Ai(r, i, u) : !0 : !!i;
    return !1
}
function Ai(e, t, n) {
    const r = Object.keys(t);
    if (r.length !== Object.keys(e).length)
        return !0;
    for (let s = 0; s < r.length; s++) {
        const o = r[s];
        if (t[o] !== e[o] && !cs(n, o))
            return !0
    }
    return !1
}
function Ed({vnode: e, parent: t}, n) {
    for (; t; ) {
        const r = t.subTree;
        if (r.suspense && r.suspense.activeBranch === e && (r.el = e.el),
        r === e)
            (e = t.vnode).el = n,
            t = t.parent;
        else
            break
    }
}
const la = e => e.__isSuspense;
function xd(e, t) {
    t && t.pendingBranch ? ee(e) ? t.effects.push(...e) : t.effects.push(e) : Bf(e)
}
const at = Symbol.for("v-fgt")
  , as = Symbol.for("v-txt")
  , Ge = Symbol.for("v-cmt")
  , Fs = Symbol.for("v-stc")
  , Kn = [];
let tt = null;
function ca(e=!1) {
    Kn.push(tt = e ? null : [])
}
function Cd() {
    Kn.pop(),
    tt = Kn[Kn.length - 1] || null
}
let nr = 1;
function Oi(e, t=!1) {
    nr += e,
    e < 0 && tt && t && (tt.hasOnce = !0)
}
function aa(e) {
    return e.dynamicChildren = nr > 0 ? tt || wn : null,
    Cd(),
    nr > 0 && tt && tt.push(e),
    e
}
function Gb(e, t, n, r, s, o) {
    return aa(da(e, t, n, r, s, o, !0))
}
function ua(e, t, n, r, s) {
    return aa(P(e, t, n, r, s, !0))
}
function rr(e) {
    return e ? e.__v_isVNode === !0 : !1
}
function on(e, t) {
    return e.type === t.type && e.key === t.key
}
const fa = ({key: e}) => e ?? null
  , Ir = ({ref: e, ref_key: t, ref_for: n}) => (typeof e == "number" && (e = "" + e),
e != null ? Ee(e) || Ae(e) || re(e) ? {
    i: qe,
    r: e,
    k: t,
    f: !!n
} : e : null);
function da(e, t=null, n=null, r=0, s=null, o=e === at ? 0 : 1, i=!1, l=!1) {
    const c = {
        __v_isVNode: !0,
        __v_skip: !0,
        type: e,
        props: t,
        key: t && fa(t),
        ref: t && Ir(t),
        scopeId: Bc,
        slotScopeIds: null,
        children: n,
        component: null,
        suspense: null,
        ssContent: null,
        ssFallback: null,
        dirs: null,
        transition: null,
        el: null,
        anchor: null,
        target: null,
        targetStart: null,
        targetAnchor: null,
        staticCount: 0,
        shapeFlag: o,
        patchFlag: r,
        dynamicProps: s,
        dynamicChildren: null,
        appContext: null,
        ctx: qe
    };
    return l ? (qo(c, n),
    o & 128 && e.normalize(c)) : n && (c.shapeFlag |= Ee(n) ? 8 : 16),
    nr > 0 && !i && tt && (c.patchFlag > 0 || o & 6) && c.patchFlag !== 32 && tt.push(c),
    c
}
const P = Td;
function Td(e, t=null, n=null, r=0, s=null, o=!1) {
    if ((!e || e === Wf) && (e = Ge),
    rr(e)) {
        const l = Yt(e, t, !0);
        return n && qo(l, n),
        nr > 0 && !o && tt && (l.shapeFlag & 6 ? tt[tt.indexOf(e)] = l : tt.push(l)),
        l.patchFlag = -2,
        l
    }
    if ($d(e) && (e = e.__vccOpts),
    t) {
        t = Rd(t);
        let {class: l, style: c} = t;
        l && !Ee(l) && (t.class = ns(l)),
        Se(c) && (Do(c) && !ee(c) && (c = Le({}, c)),
        t.style = ts(c))
    }
    const i = Ee(e) ? 1 : la(e) ? 128 : Nc(e) ? 64 : Se(e) ? 4 : re(e) ? 2 : 0;
    return da(e, t, n, r, s, i, o, !0)
}
function Rd(e) {
    return e ? Do(e) || Xc(e) ? Le({}, e) : e : null
}
function Yt(e, t, n=!1, r=!1) {
    const {props: s, ref: o, patchFlag: i, children: l, transition: c} = e
      , u = t ? xt(s || {}, t) : s
      , a = {
        __v_isVNode: !0,
        __v_skip: !0,
        type: e.type,
        props: u,
        key: u && fa(u),
        ref: t && t.ref ? n && o ? ee(o) ? o.concat(Ir(t)) : [o, Ir(t)] : Ir(t) : o,
        scopeId: e.scopeId,
        slotScopeIds: e.slotScopeIds,
        children: l,
        target: e.target,
        targetStart: e.targetStart,
        targetAnchor: e.targetAnchor,
        staticCount: e.staticCount,
        shapeFlag: e.shapeFlag,
        patchFlag: t && e.type !== at ? i === -1 ? 16 : i | 16 : i,
        dynamicProps: e.dynamicProps,
        dynamicChildren: e.dynamicChildren,
        appContext: e.appContext,
        dirs: e.dirs,
        transition: c,
        component: e.component,
        suspense: e.suspense,
        ssContent: e.ssContent && Yt(e.ssContent),
        ssFallback: e.ssFallback && Yt(e.ssFallback),
        el: e.el,
        anchor: e.anchor,
        ctx: e.ctx,
        ce: e.ce
    };
    return c && r && tr(a, c.clone(a)),
    a
}
function ha(e=" ", t=0) {
    return P(as, null, e, t)
}
function Xb(e="", t=!1) {
    return t ? (ca(),
    ua(Ge, null, e)) : P(Ge, null, e)
}
function St(e) {
    return e == null || typeof e == "boolean" ? P(Ge) : ee(e) ? P(at, null, e.slice()) : rr(e) ? Ut(e) : P(as, null, String(e))
}
function Ut(e) {
    return e.el === null && e.patchFlag !== -1 || e.memo ? e : Yt(e)
}
function qo(e, t) {
    let n = 0;
    const {shapeFlag: r} = e;
    if (t == null)
        t = null;
    else if (ee(t))
        n = 16;
    else if (typeof t == "object")
        if (r & 65) {
            const s = t.default;
            s && (s._c && (s._d = !1),
            qo(e, s()),
            s._c && (s._d = !0));
            return
        } else {
            n = 32;
            const s = t._;
            !s && !Xc(t) ? t._ctx = qe : s === 3 && qe && (qe.slots._ === 1 ? t._ = 1 : (t._ = 2,
            e.patchFlag |= 1024))
        }
    else
        re(t) ? (t = {
            default: t,
            _ctx: qe
        },
        n = 32) : (t = String(t),
        r & 64 ? (n = 16,
        t = [ha(t)]) : n = 8);
    e.children = t,
    e.shapeFlag |= n
}
function xt(...e) {
    const t = {};
    for (let n = 0; n < e.length; n++) {
        const r = e[n];
        for (const s in r)
            if (s === "class")
                t.class !== r.class && (t.class = ns([t.class, r.class]));
            else if (s === "style")
                t.style = ts([t.style, r.style]);
            else if (Gr(s)) {
                const o = t[s]
                  , i = r[s];
                i && o !== i && !(ee(o) && o.includes(i)) && (t[s] = o ? [].concat(o, i) : i)
            } else
                s !== "" && (t[s] = r[s])
    }
    return t
}
function bt(e, t, n, r=null) {
    dt(e, t, 7, [n, r])
}
const Ad = Jc();
let Od = 0;
function Pd(e, t, n) {
    const r = e.type
      , s = (t ? t.appContext : e.appContext) || Ad
      , o = {
        uid: Od++,
        vnode: e,
        type: r,
        parent: t,
        appContext: s,
        root: null,
        next: null,
        subTree: null,
        effect: null,
        update: null,
        job: null,
        scope: new lc(!0),
        render: null,
        proxy: null,
        exposed: null,
        exposeProxy: null,
        withProxy: null,
        provides: t ? t.provides : Object.create(s.provides),
        ids: t ? t.ids : ["", 0, 0],
        accessCache: null,
        renderCache: [],
        components: null,
        directives: null,
        propsOptions: Qc(r, s),
        emitsOptions: ia(r, s),
        emit: null,
        emitted: null,
        propsDefaults: ye,
        inheritAttrs: r.inheritAttrs,
        ctx: ye,
        data: ye,
        props: ye,
        attrs: ye,
        slots: ye,
        refs: ye,
        setupState: ye,
        setupContext: null,
        suspense: n,
        suspenseId: n ? n.pendingId : 0,
        asyncDep: null,
        asyncResolved: !1,
        isMounted: !1,
        isUnmounted: !1,
        isDeactivated: !1,
        bc: null,
        c: null,
        bm: null,
        m: null,
        bu: null,
        u: null,
        um: null,
        bum: null,
        da: null,
        a: null,
        rtg: null,
        rtc: null,
        ec: null,
        sp: null
    };
    return o.ctx = {
        _: o
    },
    o.root = t ? t.root : o,
    o.emit = vd.bind(null, o),
    e.ce && e.ce(o),
    o
}
let Ne = null;
const Qt = () => Ne || qe;
let zr, fo;
{
    const e = es()
      , t = (n, r) => {
        let s;
        return (s = e[n]) || (s = e[n] = []),
        s.push(r),
        o => {
            s.length > 1 ? s.forEach(i => i(o)) : s[0](o)
        }
    }
    ;
    zr = t("__VUE_INSTANCE_SETTERS__", n => Ne = n),
    fo = t("__VUE_SSR_SETTERS__", n => sr = n)
}
const gr = e => {
    const t = Ne;
    return zr(e),
    e.scope.on(),
    () => {
        e.scope.off(),
        zr(t)
    }
}
  , Pi = () => {
    Ne && Ne.scope.off(),
    zr(null)
}
;
function pa(e) {
    return e.vnode.shapeFlag & 4
}
let sr = !1;
function Id(e, t=!1, n=!1) {
    t && fo(t);
    const {props: r, children: s} = e.vnode
      , o = pa(e);
    sd(e, r, o, t),
    cd(e, s, n);
    const i = o ? Bd(e, t) : void 0;
    return t && fo(!1),
    i
}
function Bd(e, t) {
    const n = e.type;
    e.accessCache = Object.create(null),
    e.proxy = new Proxy(e.ctx,Yf);
    const {setup: r} = n;
    if (r) {
        Xt();
        const s = e.setupContext = r.length > 1 ? Nd(e) : null
          , o = gr(e)
          , i = dr(r, e, 0, [e.props, s])
          , l = ec(i);
        if (Zt(),
        o(),
        (l || e.sp) && !zn(e) && Vc(e),
        l) {
            if (i.then(Pi, Pi),
            t)
                return i.then(c => {
                    Ii(e, c)
                }
                ).catch(c => {
                    ss(c, e, 0)
                }
                );
            e.asyncDep = i
        } else
            Ii(e, i)
    } else
        ga(e)
}
function Ii(e, t, n) {
    re(t) ? e.type.__ssrInlineRender ? e.ssrRender = t : e.render = t : Se(t) && (e.setupState = Rc(t)),
    ga(e)
}
function ga(e, t, n) {
    const r = e.type;
    e.render || (e.render = r.render || _t);
    {
        const s = gr(e);
        Xt();
        try {
            Gf(e)
        } finally {
            Zt(),
            s()
        }
    }
}
const kd = {
    get(e, t) {
        return $e(e, "get", ""),
        e[t]
    }
};
function Nd(e) {
    const t = n => {
        e.exposed = n || {}
    }
    ;
    return {
        attrs: new Proxy(e.attrs,kd),
        slots: e.slots,
        emit: e.emit,
        expose: t
    }
}
function us(e) {
    return e.exposed ? e.exposeProxy || (e.exposeProxy = new Proxy(Rc(Mo(e.exposed)),{
        get(t, n) {
            if (n in t)
                return t[n];
            if (n in qn)
                return qn[n](e)
        },
        has(t, n) {
            return n in t || n in qn
        }
    })) : e.proxy
}
function Ld(e, t=!0) {
    return re(e) ? e.displayName || e.name : e.name || t && e.__name
}
function $d(e) {
    return re(e) && "__vccOpts"in e
}
const G = (e, t) => Rf(e, t, sr);
function Ko(e, t, n) {
    const r = arguments.length;
    return r === 2 ? Se(t) && !ee(t) ? rr(t) ? P(e, null, [t]) : P(e, t) : P(e, null, t) : (r > 3 ? n = Array.prototype.slice.call(arguments, 2) : r === 3 && rr(n) && (n = [n]),
    P(e, t, n))
}
const Fd = "3.5.13";
/**
* @vue/runtime-dom v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/
let ho;
const Bi = typeof window < "u" && window.trustedTypes;
if (Bi)
    try {
        ho = Bi.createPolicy("vue", {
            createHTML: e => e
        })
    } catch {}
const ma = ho ? e => ho.createHTML(e) : e => e
  , Dd = "http://www.w3.org/2000/svg"
  , Md = "http://www.w3.org/1998/Math/MathML"
  , Pt = typeof document < "u" ? document : null
  , ki = Pt && Pt.createElement("template")
  , jd = {
    insert: (e, t, n) => {
        t.insertBefore(e, n || null)
    }
    ,
    remove: e => {
        const t = e.parentNode;
        t && t.removeChild(e)
    }
    ,
    createElement: (e, t, n, r) => {
        const s = t === "svg" ? Pt.createElementNS(Dd, e) : t === "mathml" ? Pt.createElementNS(Md, e) : n ? Pt.createElement(e, {
            is: n
        }) : Pt.createElement(e);
        return e === "select" && r && r.multiple != null && s.setAttribute("multiple", r.multiple),
        s
    }
    ,
    createText: e => Pt.createTextNode(e),
    createComment: e => Pt.createComment(e),
    setText: (e, t) => {
        e.nodeValue = t
    }
    ,
    setElementText: (e, t) => {
        e.textContent = t
    }
    ,
    parentNode: e => e.parentNode,
    nextSibling: e => e.nextSibling,
    querySelector: e => Pt.querySelector(e),
    setScopeId(e, t) {
        e.setAttribute(t, "")
    },
    insertStaticContent(e, t, n, r, s, o) {
        const i = n ? n.previousSibling : t.lastChild;
        if (s && (s === o || s.nextSibling))
            for (; t.insertBefore(s.cloneNode(!0), n),
            !(s === o || !(s = s.nextSibling)); )
                ;
        else {
            ki.innerHTML = ma(r === "svg" ? `<svg>${e}</svg>` : r === "mathml" ? `<math>${e}</math>` : e);
            const l = ki.content;
            if (r === "svg" || r === "mathml") {
                const c = l.firstChild;
                for (; c.firstChild; )
                    l.appendChild(c.firstChild);
                l.removeChild(c)
            }
            t.insertBefore(l, n)
        }
        return [i ? i.nextSibling : t.firstChild, n ? n.previousSibling : t.lastChild]
    }
}
  , Ft = "transition"
  , kn = "animation"
  , or = Symbol("_vtc")
  , ya = {
    name: String,
    type: String,
    css: {
        type: Boolean,
        default: !0
    },
    duration: [String, Number, Object],
    enterFromClass: String,
    enterActiveClass: String,
    enterToClass: String,
    appearFromClass: String,
    appearActiveClass: String,
    appearToClass: String,
    leaveFromClass: String,
    leaveActiveClass: String,
    leaveToClass: String
}
  , Hd = Le({}, Dc, ya)
  , Ud = e => (e.displayName = "Transition",
e.props = Hd,
e)
  , ba = Ud( (e, {slots: t}) => Ko(Ff, Vd(e), t))
  , nn = (e, t=[]) => {
    ee(e) ? e.forEach(n => n(...t)) : e && e(...t)
}
  , Ni = e => e ? ee(e) ? e.some(t => t.length > 1) : e.length > 1 : !1;
function Vd(e) {
    const t = {};
    for (const $ in e)
        $ in ya || (t[$] = e[$]);
    if (e.css === !1)
        return t;
    const {name: n="v", type: r, duration: s, enterFromClass: o=`${n}-enter-from`, enterActiveClass: i=`${n}-enter-active`, enterToClass: l=`${n}-enter-to`, appearFromClass: c=o, appearActiveClass: u=i, appearToClass: a=l, leaveFromClass: f=`${n}-leave-from`, leaveActiveClass: d=`${n}-leave-active`, leaveToClass: g=`${n}-leave-to`} = e
      , y = zd(s)
      , b = y && y[0]
      , w = y && y[1]
      , {onBeforeEnter: _, onEnter: S, onEnterCancelled: I, onLeave: O, onLeaveCancelled: k, onBeforeAppear: F=_, onAppear: V=S, onAppearCancelled: U=I} = t
      , B = ($, ne, me, Te) => {
        $._enterCancelled = Te,
        rn($, ne ? a : l),
        rn($, ne ? u : i),
        me && me()
    }
      , z = ($, ne) => {
        $._isLeaving = !1,
        rn($, f),
        rn($, g),
        rn($, d),
        ne && ne()
    }
      , X = $ => (ne, me) => {
        const Te = $ ? V : S
          , ae = () => B(ne, $, me);
        nn(Te, [ne, ae]),
        Li( () => {
            rn(ne, $ ? c : o),
            Rt(ne, $ ? a : l),
            Ni(Te) || $i(ne, r, b, ae)
        }
        )
    }
    ;
    return Le(t, {
        onBeforeEnter($) {
            nn(_, [$]),
            Rt($, o),
            Rt($, i)
        },
        onBeforeAppear($) {
            nn(F, [$]),
            Rt($, c),
            Rt($, u)
        },
        onEnter: X(!1),
        onAppear: X(!0),
        onLeave($, ne) {
            $._isLeaving = !0;
            const me = () => z($, ne);
            Rt($, f),
            $._enterCancelled ? (Rt($, d),
            Mi()) : (Mi(),
            Rt($, d)),
            Li( () => {
                $._isLeaving && (rn($, f),
                Rt($, g),
                Ni(O) || $i($, r, w, me))
            }
            ),
            nn(O, [$, me])
        },
        onEnterCancelled($) {
            B($, !1, void 0, !0),
            nn(I, [$])
        },
        onAppearCancelled($) {
            B($, !0, void 0, !0),
            nn(U, [$])
        },
        onLeaveCancelled($) {
            z($),
            nn(k, [$])
        }
    })
}
function zd(e) {
    if (e == null)
        return null;
    if (Se(e))
        return [Ds(e.enter), Ds(e.leave)];
    {
        const t = Ds(e);
        return [t, t]
    }
}
function Ds(e) {
    return zu(e)
}
function Rt(e, t) {
    t.split(/\s+/).forEach(n => n && e.classList.add(n)),
    (e[or] || (e[or] = new Set)).add(t)
}
function rn(e, t) {
    t.split(/\s+/).forEach(r => r && e.classList.remove(r));
    const n = e[or];
    n && (n.delete(t),
    n.size || (e[or] = void 0))
}
function Li(e) {
    requestAnimationFrame( () => {
        requestAnimationFrame(e)
    }
    )
}
let qd = 0;
function $i(e, t, n, r) {
    const s = e._endId = ++qd
      , o = () => {
        s === e._endId && r()
    }
    ;
    if (n != null)
        return setTimeout(o, n);
    const {type: i, timeout: l, propCount: c} = Kd(e, t);
    if (!i)
        return r();
    const u = i + "end";
    let a = 0;
    const f = () => {
        e.removeEventListener(u, d),
        o()
    }
      , d = g => {
        g.target === e && ++a >= c && f()
    }
    ;
    setTimeout( () => {
        a < c && f()
    }
    , l + 1),
    e.addEventListener(u, d)
}
function Kd(e, t) {
    const n = window.getComputedStyle(e)
      , r = y => (n[y] || "").split(", ")
      , s = r(`${Ft}Delay`)
      , o = r(`${Ft}Duration`)
      , i = Fi(s, o)
      , l = r(`${kn}Delay`)
      , c = r(`${kn}Duration`)
      , u = Fi(l, c);
    let a = null
      , f = 0
      , d = 0;
    t === Ft ? i > 0 && (a = Ft,
    f = i,
    d = o.length) : t === kn ? u > 0 && (a = kn,
    f = u,
    d = c.length) : (f = Math.max(i, u),
    a = f > 0 ? i > u ? Ft : kn : null,
    d = a ? a === Ft ? o.length : c.length : 0);
    const g = a === Ft && /\b(transform|all)(,|$)/.test(r(`${Ft}Property`).toString());
    return {
        type: a,
        timeout: f,
        propCount: d,
        hasTransform: g
    }
}
function Fi(e, t) {
    for (; e.length < t.length; )
        e = e.concat(e);
    return Math.max(...t.map( (n, r) => Di(n) + Di(e[r])))
}
function Di(e) {
    return e === "auto" ? 0 : Number(e.slice(0, -1).replace(",", ".")) * 1e3
}
function Mi() {
    return document.body.offsetHeight
}
function Wd(e, t, n) {
    const r = e[or];
    r && (t = (t ? [t, ...r] : [...r]).join(" ")),
    t == null ? e.removeAttribute("class") : n ? e.setAttribute("class", t) : e.className = t
}
const qr = Symbol("_vod")
  , va = Symbol("_vsh")
  , Wo = {
    beforeMount(e, {value: t}, {transition: n}) {
        e[qr] = e.style.display === "none" ? "" : e.style.display,
        n && t ? n.beforeEnter(e) : Nn(e, t)
    },
    mounted(e, {value: t}, {transition: n}) {
        n && t && n.enter(e)
    },
    updated(e, {value: t, oldValue: n}, {transition: r}) {
        !t != !n && (r ? t ? (r.beforeEnter(e),
        Nn(e, !0),
        r.enter(e)) : r.leave(e, () => {
            Nn(e, !1)
        }
        ) : Nn(e, t))
    },
    beforeUnmount(e, {value: t}) {
        Nn(e, t)
    }
};
function Nn(e, t) {
    e.style.display = t ? e[qr] : "none",
    e[va] = !t
}
const Jd = Symbol("")
  , Yd = /(^|;)\s*display\s*:/;
function Gd(e, t, n) {
    const r = e.style
      , s = Ee(n);
    let o = !1;
    if (n && !s) {
        if (t)
            if (Ee(t))
                for (const i of t.split(";")) {
                    const l = i.slice(0, i.indexOf(":")).trim();
                    n[l] == null && Br(r, l, "")
                }
            else
                for (const i in t)
                    n[i] == null && Br(r, i, "");
        for (const i in n)
            i === "display" && (o = !0),
            Br(r, i, n[i])
    } else if (s) {
        if (t !== n) {
            const i = r[Jd];
            i && (n += ";" + i),
            r.cssText = n,
            o = Yd.test(n)
        }
    } else
        t && e.removeAttribute("style");
    qr in e && (e[qr] = o ? r.display : "",
    e[va] && (r.display = "none"))
}
const ji = /\s*!important$/;
function Br(e, t, n) {
    if (ee(n))
        n.forEach(r => Br(e, t, r));
    else if (n == null && (n = ""),
    t.startsWith("--"))
        e.setProperty(t, n);
    else {
        const r = Xd(e, t);
        ji.test(n) ? e.setProperty(Nt(r), n.replace(ji, ""), "important") : e[r] = n
    }
}
const Hi = ["Webkit", "Moz", "ms"]
  , Ms = {};
function Xd(e, t) {
    const n = Ms[t];
    if (n)
        return n;
    let r = it(t);
    if (r !== "filter" && r in e)
        return Ms[t] = r;
    r = Qr(r);
    for (let s = 0; s < Hi.length; s++) {
        const o = Hi[s] + r;
        if (o in e)
            return Ms[t] = o
    }
    return t
}
const Ui = "http://www.w3.org/1999/xlink";
function Vi(e, t, n, r, s, o=Xu(t)) {
    r && t.startsWith("xlink:") ? n == null ? e.removeAttributeNS(Ui, t.slice(6, t.length)) : e.setAttributeNS(Ui, t, n) : n == null || o && !sc(n) ? e.removeAttribute(t) : e.setAttribute(t, o ? "" : Gt(n) ? String(n) : n)
}
function zi(e, t, n, r, s) {
    if (t === "innerHTML" || t === "textContent") {
        n != null && (e[t] = t === "innerHTML" ? ma(n) : n);
        return
    }
    const o = e.tagName;
    if (t === "value" && o !== "PROGRESS" && !o.includes("-")) {
        const l = o === "OPTION" ? e.getAttribute("value") || "" : e.value
          , c = n == null ? e.type === "checkbox" ? "on" : "" : String(n);
        (l !== c || !("_value"in e)) && (e.value = c),
        n == null && e.removeAttribute(t),
        e._value = n;
        return
    }
    let i = !1;
    if (n === "" || n == null) {
        const l = typeof e[t];
        l === "boolean" ? n = sc(n) : n == null && l === "string" ? (n = "",
        i = !0) : l === "number" && (n = 0,
        i = !0)
    }
    try {
        e[t] = n
    } catch {}
    i && e.removeAttribute(s || t)
}
function Zd(e, t, n, r) {
    e.addEventListener(t, n, r)
}
function Qd(e, t, n, r) {
    e.removeEventListener(t, n, r)
}
const qi = Symbol("_vei");
function eh(e, t, n, r, s=null) {
    const o = e[qi] || (e[qi] = {})
      , i = o[t];
    if (r && i)
        i.value = r;
    else {
        const [l,c] = th(t);
        if (r) {
            const u = o[t] = sh(r, s);
            Zd(e, l, u, c)
        } else
            i && (Qd(e, l, i, c),
            o[t] = void 0)
    }
}
const Ki = /(?:Once|Passive|Capture)$/;
function th(e) {
    let t;
    if (Ki.test(e)) {
        t = {};
        let r;
        for (; r = e.match(Ki); )
            e = e.slice(0, e.length - r[0].length),
            t[r[0].toLowerCase()] = !0
    }
    return [e[2] === ":" ? e.slice(3) : Nt(e.slice(2)), t]
}
let js = 0;
const nh = Promise.resolve()
  , rh = () => js || (nh.then( () => js = 0),
js = Date.now());
function sh(e, t) {
    const n = r => {
        if (!r._vts)
            r._vts = Date.now();
        else if (r._vts <= n.attached)
            return;
        dt(oh(r, n.value), t, 5, [r])
    }
    ;
    return n.value = e,
    n.attached = rh(),
    n
}
function oh(e, t) {
    if (ee(t)) {
        const n = e.stopImmediatePropagation;
        return e.stopImmediatePropagation = () => {
            n.call(e),
            e._stopped = !0
        }
        ,
        t.map(r => s => !s._stopped && r && r(s))
    } else
        return t
}
const Wi = e => e.charCodeAt(0) === 111 && e.charCodeAt(1) === 110 && e.charCodeAt(2) > 96 && e.charCodeAt(2) < 123
  , ih = (e, t, n, r, s, o) => {
    const i = s === "svg";
    t === "class" ? Wd(e, r, i) : t === "style" ? Gd(e, n, r) : Gr(t) ? Oo(t) || eh(e, t, n, r, o) : (t[0] === "." ? (t = t.slice(1),
    !0) : t[0] === "^" ? (t = t.slice(1),
    !1) : lh(e, t, r, i)) ? (zi(e, t, r),
    !e.tagName.includes("-") && (t === "value" || t === "checked" || t === "selected") && Vi(e, t, r, i, o, t !== "value")) : e._isVueCE && (/[A-Z]/.test(t) || !Ee(r)) ? zi(e, it(t), r, o, t) : (t === "true-value" ? e._trueValue = r : t === "false-value" && (e._falseValue = r),
    Vi(e, t, r, i))
}
;
function lh(e, t, n, r) {
    if (r)
        return !!(t === "innerHTML" || t === "textContent" || t in e && Wi(t) && re(n));
    if (t === "spellcheck" || t === "draggable" || t === "translate" || t === "form" || t === "list" && e.tagName === "INPUT" || t === "type" && e.tagName === "TEXTAREA")
        return !1;
    if (t === "width" || t === "height") {
        const s = e.tagName;
        if (s === "IMG" || s === "VIDEO" || s === "CANVAS" || s === "SOURCE")
            return !1
    }
    return Wi(t) && Ee(n) ? !1 : t in e
}
const ch = {
    esc: "escape",
    space: " ",
    up: "arrow-up",
    left: "arrow-left",
    right: "arrow-right",
    down: "arrow-down",
    delete: "backspace"
}
  , ah = (e, t) => {
    const n = e._withKeys || (e._withKeys = {})
      , r = t.join(".");
    return n[r] || (n[r] = s => {
        if (!("key"in s))
            return;
        const o = Nt(s.key);
        if (t.some(i => i === o || ch[i] === o))
            return e(s)
    }
    )
}
  , uh = Le({
    patchProp: ih
}, jd);
let Ji;
function fh() {
    return Ji || (Ji = ud(uh))
}
const wa = (...e) => {
    const t = fh().createApp(...e)
      , {mount: n} = t;
    return t.mount = r => {
        const s = hh(r);
        if (!s)
            return;
        const o = t._component;
        !re(o) && !o.render && !o.template && (o.template = s.innerHTML),
        s.nodeType === 1 && (s.textContent = "");
        const i = n(s, !1, dh(s));
        return s instanceof Element && (s.removeAttribute("v-cloak"),
        s.setAttribute("data-v-app", "")),
        i
    }
    ,
    t
}
;
function dh(e) {
    if (e instanceof SVGElement)
        return "svg";
    if (typeof MathMLElement == "function" && e instanceof MathMLElement)
        return "mathml"
}
function hh(e) {
    return Ee(e) ? document.querySelector(e) : e
}
var Rn = typeof window < "u";
function ir(e) {
    return Rn ? requestAnimationFrame(e) : -1
}
function Sa(e) {
    Rn && cancelAnimationFrame(e)
}
function Mn(e) {
    ir( () => ir(e))
}
var ph = e => e === window
  , Yi = (e, t) => ({
    top: 0,
    left: 0,
    right: e,
    bottom: t,
    width: e,
    height: t
})
  , zt = e => {
    const t = Et(e);
    if (ph(t)) {
        const n = t.innerWidth
          , r = t.innerHeight;
        return Yi(n, r)
    }
    return t != null && t.getBoundingClientRect ? t.getBoundingClientRect() : Yi(0, 0)
}
;
function mr(e) {
    const t = Xe(e, null);
    if (t) {
        const n = Qt()
          , {link: r, unlink: s, internalChildren: o} = t;
        r(n),
        ls( () => s(n));
        const i = G( () => o.indexOf(n));
        return {
            parent: t,
            index: i
        }
    }
    return {
        parent: null,
        index: se(-1)
    }
}
function gh(e) {
    const t = []
      , n = r => {
        Array.isArray(r) && r.forEach(s => {
            var o;
            rr(s) && (t.push(s),
            (o = s.component) != null && o.subTree && (t.push(s.component.subTree),
            n(s.component.subTree.children)),
            s.children && n(s.children))
        }
        )
    }
    ;
    return n(e),
    t
}
var Gi = (e, t) => {
    const n = e.indexOf(t);
    return n === -1 ? e.findIndex(r => t.key !== void 0 && t.key !== null && r.type === t.type && r.key === t.key) : n
}
;
function mh(e, t, n) {
    const r = gh(e.subTree.children);
    n.sort( (o, i) => Gi(r, o.vnode) - Gi(r, i.vnode));
    const s = n.map(o => o.proxy);
    t.sort( (o, i) => {
        const l = s.indexOf(o)
          , c = s.indexOf(i);
        return l - c
    }
    )
}
function fs(e) {
    const t = Ke([])
      , n = Ke([])
      , r = Qt();
    return {
        children: t,
        linkChildren: o => {
            Wt(e, Object.assign({
                link: c => {
                    c.proxy && (n.push(c),
                    t.push(c.proxy),
                    mh(r, t, n))
                }
                ,
                unlink: c => {
                    const u = n.indexOf(c);
                    t.splice(u, 1),
                    n.splice(u, 1)
                }
                ,
                children: t,
                internalChildren: n
            }, o))
        }
    }
}
function ds(e) {
    let t;
    Ct( () => {
        e(),
        Pe( () => {
            t = !0
        }
        )
    }
    ),
    hr( () => {
        t && e()
    }
    )
}
function An(e, t, n={}) {
    if (!Rn)
        return;
    const {target: r=window, passive: s=!1, capture: o=!1} = n;
    let i = !1, l;
    const c = f => {
        if (i)
            return;
        const d = Et(f);
        d && !l && (d.addEventListener(e, t, {
            capture: o,
            passive: s
        }),
        l = !0)
    }
      , u = f => {
        if (i)
            return;
        const d = Et(f);
        d && l && (d.removeEventListener(e, t, o),
        l = !1)
    }
    ;
    ls( () => u(r)),
    Tn( () => u(r)),
    ds( () => c(r));
    let a;
    return Ae(r) && (a = we(r, (f, d) => {
        u(d),
        c(f)
    }
    )),
    () => {
        a == null || a(),
        u(r),
        i = !0
    }
}
var Cr, Hs;
function yh() {
    if (!Cr && (Cr = se(0),
    Hs = se(0),
    Rn)) {
        const e = () => {
            Cr.value = window.innerWidth,
            Hs.value = window.innerHeight
        }
        ;
        e(),
        window.addEventListener("resize", e, {
            passive: !0
        }),
        window.addEventListener("orientationchange", e, {
            passive: !0
        })
    }
    return {
        width: Cr,
        height: Hs
    }
}
var bh = /scroll|auto|overlay/i
  , _a = Rn ? window : void 0;
function vh(e) {
    return e.tagName !== "HTML" && e.tagName !== "BODY" && e.nodeType === 1
}
function Ea(e, t=_a) {
    let n = e;
    for (; n && n !== t && vh(n); ) {
        const {overflowY: r} = window.getComputedStyle(n);
        if (bh.test(r))
            return n;
        n = n.parentNode
    }
    return t
}
function xa(e, t=_a) {
    const n = se();
    return Ct( () => {
        e.value && (n.value = Ea(e.value, t))
    }
    ),
    n
}
var Tr;
function wh() {
    if (!Tr && (Tr = se("visible"),
    Rn)) {
        const e = () => {
            Tr.value = document.hidden ? "hidden" : "visible"
        }
        ;
        e(),
        window.addEventListener("visibilitychange", e)
    }
    return Tr
}
var Sh = Symbol("van-field");
function Jo(e) {
    const t = "scrollTop"in e ? e.scrollTop : e.pageYOffset;
    return Math.max(t, 0)
}
function po(e, t) {
    "scrollTop"in e ? e.scrollTop = t : e.scrollTo(e.scrollX, t)
}
function Yo() {
    return window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0
}
function Go(e) {
    po(window, e),
    po(document.body, e)
}
function Xi(e, t) {
    if (e === window)
        return 0;
    const n = t ? Jo(t) : Yo();
    return zt(e).top + n
}
const _h = Fu();
function Eh() {
    _h && Go(Yo())
}
const xh = e => e.stopPropagation();
function un(e, t) {
    (typeof e.cancelable != "boolean" || e.cancelable) && e.preventDefault(),
    t && xh(e)
}
function lr(e) {
    const t = Et(e);
    if (!t)
        return !1;
    const n = window.getComputedStyle(t)
      , r = n.display === "none"
      , s = t.offsetParent === null && n.position !== "fixed";
    return r || s
}
const {width: yr, height: hs} = yh();
function De(e) {
    if (Be(e))
        return Zl(e) ? `${e}px` : String(e)
}
function Ch(e) {
    if (Be(e)) {
        if (Array.isArray(e))
            return {
                width: De(e[0]),
                height: De(e[1])
            };
        const t = De(e);
        return {
            width: t,
            height: t
        }
    }
}
function Ca(e) {
    const t = {};
    return e !== void 0 && (t.zIndex = +e),
    t
}
let Us;
function Th() {
    if (!Us) {
        const e = document.documentElement
          , t = e.style.fontSize || window.getComputedStyle(e).fontSize;
        Us = parseFloat(t)
    }
    return Us
}
function Rh(e) {
    return e = e.replace(/rem/g, ""),
    +e * Th()
}
function Ah(e) {
    return e = e.replace(/vw/g, ""),
    +e * yr.value / 100
}
function Oh(e) {
    return e = e.replace(/vh/g, ""),
    +e * hs.value / 100
}
function Ta(e) {
    if (typeof e == "number")
        return e;
    if (Yr) {
        if (e.includes("rem"))
            return Rh(e);
        if (e.includes("vw"))
            return Ah(e);
        if (e.includes("vh"))
            return Oh(e)
    }
    return parseFloat(e)
}
const Ph = /-(\w)/g
  , Ra = e => e.replace(Ph, (t, n) => n.toUpperCase())
  , kr = (e, t, n) => Math.min(Math.max(e, t), n);
function Zi(e, t, n) {
    const r = e.indexOf(t);
    return r === -1 ? e : t === "-" && r !== 0 ? e.slice(0, r) : e.slice(0, r + 1) + e.slice(r).replace(n, "")
}
function Ih(e, t=!0, n=!0) {
    t ? e = Zi(e, ".", /\./g) : e = e.split(".")[0],
    n ? e = Zi(e, "-", /-/g) : e = e.replace(/-/, "");
    const r = t ? /[^-0-9.]/g : /[^-0-9]/g;
    return e.replace(r, "")
}
const {hasOwnProperty: Bh} = Object.prototype;
function kh(e, t, n) {
    const r = t[n];
    Be(r) && (!Bh.call(e, n) || !fr(r) ? e[n] = r : e[n] = Aa(Object(e[n]), r))
}
function Aa(e, t) {
    return Object.keys(t).forEach(n => {
        kh(e, t, n)
    }
    ),
    e
}
var Nh = {
    name: "姓名",
    tel: "电话",
    save: "保存",
    clear: "清空",
    cancel: "取消",
    confirm: "确认",
    delete: "删除",
    loading: "加载中...",
    noCoupon: "暂无优惠券",
    nameEmpty: "请填写姓名",
    addContact: "添加联系人",
    telInvalid: "请填写正确的电话",
    vanCalendar: {
        end: "结束",
        start: "开始",
        title: "日期选择",
        weekdays: ["日", "一", "二", "三", "四", "五", "六"],
        monthTitle: (e, t) => `${e}年${t}月`,
        rangePrompt: e => `最多选择 ${e} 天`
    },
    vanCascader: {
        select: "请选择"
    },
    vanPagination: {
        prev: "上一页",
        next: "下一页"
    },
    vanPullRefresh: {
        pulling: "下拉即可刷新...",
        loosing: "释放即可刷新..."
    },
    vanSubmitBar: {
        label: "合计:"
    },
    vanCoupon: {
        unlimited: "无门槛",
        discount: e => `${e}折`,
        condition: e => `满${e}元可用`
    },
    vanCouponCell: {
        title: "优惠券",
        count: e => `${e}张可用`
    },
    vanCouponList: {
        exchange: "兑换",
        close: "不使用",
        enable: "可用",
        disabled: "不可用",
        placeholder: "输入优惠码"
    },
    vanAddressEdit: {
        area: "地区",
        areaEmpty: "请选择地区",
        addressEmpty: "请填写详细地址",
        addressDetail: "详细地址",
        defaultAddress: "设为默认收货地址"
    },
    vanAddressList: {
        add: "新增地址"
    }
};
const Qi = se("zh-CN")
  , el = Ke({
    "zh-CN": Nh
})
  , Lh = {
    messages() {
        return el[Qi.value]
    },
    use(e, t) {
        Qi.value = e,
        this.add({
            [e]: t
        })
    },
    add(e={}) {
        Aa(el, e)
    }
};
var $h = Lh;
function Fh(e) {
    const t = Ra(e) + ".";
    return (n, ...r) => {
        const s = $h.messages()
          , o = ui(s, t + n) || ui(s, n);
        return Xn(o) ? o(...r) : o
    }
}
function go(e, t) {
    return t ? typeof t == "string" ? ` ${e}--${t}` : Array.isArray(t) ? t.reduce( (n, r) => n + go(e, r), "") : Object.keys(t).reduce( (n, r) => n + (t[r] ? go(e, r) : ""), "") : ""
}
function Dh(e) {
    return (t, n) => (t && typeof t != "string" && (n = t,
    t = ""),
    t = t ? `${e}__${t}` : e,
    `${t}${go(t, n)}`)
}
function Oe(e) {
    const t = `van-${e}`;
    return [t, Dh(t), Fh(t)]
}
const ps = "van-hairline"
  , Mh = `${ps}--top`
  , jh = `${ps}--left`
  , Hh = `${ps}--surround`
  , Uh = `${ps}--top-bottom`
  , Vh = "van-haptics-feedback"
  , zh = Symbol("van-form")
  , tl = 5;
function Xo(e, {args: t=[], done: n, canceled: r, error: s}) {
    if (e) {
        const o = e.apply(null, t);
        Xl(o) ? o.then(i => {
            i ? n() : r && r()
        }
        ).catch(s || eo) : o ? n() : r && r()
    } else
        n()
}
function ke(e) {
    return e.install = t => {
        const {name: n} = e;
        n && (t.component(n, e),
        t.component(Ra(`-${n}`), e))
    }
    ,
    e
}
const Oa = Symbol();
function Zo(e) {
    const t = Xe(Oa, null);
    t && we(t, n => {
        n && e()
    }
    )
}
const qh = (e, t) => {
    const n = se()
      , r = () => {
        n.value = zt(e).height
    }
    ;
    return Ct( () => {
        Pe(r);
        for (let s = 1; s <= 3; s++)
            setTimeout(r, 100 * s)
    }
    ),
    Zo( () => Pe(r)),
    we([yr, hs], r),
    n
}
;
function Kh(e, t) {
    const n = qh(e);
    return r => P("div", {
        class: t("placeholder"),
        style: {
            height: n.value ? `${n.value}px` : void 0
        }
    }, [r()])
}
const [Pa,nl] = Oe("action-bar")
  , Ia = Symbol(Pa)
  , Wh = {
    placeholder: Boolean,
    safeAreaInsetBottom: Re
};
var Jh = Ce({
    name: Pa,
    props: Wh,
    setup(e, {slots: t}) {
        const n = se()
          , r = Kh(n, nl)
          , {linkChildren: s} = fs(Ia);
        s();
        const o = () => {
            var i;
            return P("div", {
                ref: n,
                class: [nl(), {
                    "van-safe-area-bottom": e.safeAreaInsetBottom
                }]
            }, [(i = t.default) == null ? void 0 : i.call(t)])
        }
        ;
        return () => e.placeholder ? r(o) : o()
    }
});
const Yh = ke(Jh);
function $t(e) {
    const t = Qt();
    t && We(t.proxy, e)
}
const gs = {
    to: [String, Object],
    url: String,
    replace: Boolean
};
function Ba({to: e, url: t, replace: n, $router: r}) {
    e && r ? r[n ? "replace" : "push"](e) : t && (n ? location.replace(t) : location.href = t)
}
function Qo() {
    const e = Qt().proxy;
    return () => Ba(e)
}
const [Gh,rl] = Oe("badge")
  , Xh = {
    dot: Boolean,
    max: pe,
    tag: _e("div"),
    color: String,
    offset: Array,
    content: pe,
    showZero: Re,
    position: _e("top-right")
};
var Zh = Ce({
    name: Gh,
    props: Xh,
    setup(e, {slots: t}) {
        const n = () => {
            if (t.content)
                return !0;
            const {content: l, showZero: c} = e;
            return Be(l) && l !== "" && (c || l !== 0 && l !== "0")
        }
          , r = () => {
            const {dot: l, max: c, content: u} = e;
            if (!l && n())
                return t.content ? t.content() : Be(c) && Zl(u) && +u > +c ? `${c}+` : u
        }
          , s = l => l.startsWith("-") ? l.replace("-", "") : `-${l}`
          , o = G( () => {
            const l = {
                background: e.color
            };
            if (e.offset) {
                const [c,u] = e.offset
                  , {position: a} = e
                  , [f,d] = a.split("-");
                t.default ? (typeof u == "number" ? l[f] = De(f === "top" ? u : -u) : l[f] = f === "top" ? De(u) : s(u),
                typeof c == "number" ? l[d] = De(d === "left" ? c : -c) : l[d] = d === "left" ? De(c) : s(c)) : (l.marginTop = De(u),
                l.marginLeft = De(c))
            }
            return l
        }
        )
          , i = () => {
            if (n() || e.dot)
                return P("div", {
                    class: rl([e.position, {
                        dot: e.dot,
                        fixed: !!t.default
                    }]),
                    style: o.value
                }, [r()])
        }
        ;
        return () => {
            if (t.default) {
                const {tag: l} = e;
                return P(l, {
                    class: rl("wrapper")
                }, {
                    default: () => [t.default(), i()]
                })
            }
            return i()
        }
    }
});
const ka = ke(Zh);
let Qh = 2e3;
const ep = () => ++Qh
  , [tp,Zb] = Oe("config-provider")
  , np = Symbol(tp)
  , [rp,sl] = Oe("icon")
  , sp = e => e == null ? void 0 : e.includes("/")
  , op = {
    dot: Boolean,
    tag: _e("i"),
    name: String,
    size: pe,
    badge: pe,
    color: String,
    badgeProps: Object,
    classPrefix: String
};
var ip = Ce({
    name: rp,
    props: op,
    setup(e, {slots: t}) {
        const n = Xe(np, null)
          , r = G( () => e.classPrefix || (n == null ? void 0 : n.iconPrefix) || sl());
        return () => {
            const {tag: s, dot: o, name: i, size: l, badge: c, color: u} = e
              , a = sp(i);
            return P(ka, xt({
                dot: o,
                tag: s,
                class: [r.value, a ? "" : `${r.value}-${i}`],
                style: {
                    color: u,
                    fontSize: De(l)
                },
                content: c
            }, e.badgeProps), {
                default: () => {
                    var f;
                    return [(f = t.default) == null ? void 0 : f.call(t), a && P("img", {
                        class: sl("image"),
                        src: i
                    }, null)]
                }
            })
        }
    }
});
const Jt = ke(ip)
  , [lp,Wn] = Oe("loading")
  , cp = Array(12).fill(null).map( (e, t) => P("i", {
    class: Wn("line", String(t + 1))
}, null))
  , ap = P("svg", {
    class: Wn("circular"),
    viewBox: "25 25 50 50"
}, [P("circle", {
    cx: "50",
    cy: "50",
    r: "20",
    fill: "none"
}, null)])
  , up = {
    size: pe,
    type: _e("circular"),
    color: String,
    vertical: Boolean,
    textSize: pe,
    textColor: String
};
var fp = Ce({
    name: lp,
    props: up,
    setup(e, {slots: t}) {
        const n = G( () => We({
            color: e.color
        }, Ch(e.size)))
          , r = () => {
            const o = e.type === "spinner" ? cp : ap;
            return P("span", {
                class: Wn("spinner", e.type),
                style: n.value
            }, [t.icon ? t.icon() : o])
        }
          , s = () => {
            var o;
            if (t.default)
                return P("span", {
                    class: Wn("text"),
                    style: {
                        fontSize: De(e.textSize),
                        color: (o = e.textColor) != null ? o : e.color
                    }
                }, [t.default()])
        }
        ;
        return () => {
            const {type: o, vertical: i} = e;
            return P("div", {
                class: Wn([o, {
                    vertical: i
                }]),
                "aria-live": "polite",
                "aria-busy": !0
            }, [r(), s()])
        }
    }
});
const dp = ke(fp)
  , [hp,gn] = Oe("button")
  , pp = We({}, gs, {
    tag: _e("button"),
    text: String,
    icon: String,
    type: _e("default"),
    size: _e("normal"),
    color: String,
    block: Boolean,
    plain: Boolean,
    round: Boolean,
    square: Boolean,
    loading: Boolean,
    hairline: Boolean,
    disabled: Boolean,
    iconPrefix: String,
    nativeType: _e("button"),
    loadingSize: pe,
    loadingText: String,
    loadingType: String,
    iconPosition: _e("left")
});
var gp = Ce({
    name: hp,
    props: pp,
    emits: ["click"],
    setup(e, {emit: t, slots: n}) {
        const r = Qo()
          , s = () => n.loading ? n.loading() : P(dp, {
            size: e.loadingSize,
            type: e.loadingType,
            class: gn("loading")
        }, null)
          , o = () => {
            if (e.loading)
                return s();
            if (n.icon)
                return P("div", {
                    class: gn("icon")
                }, [n.icon()]);
            if (e.icon)
                return P(Jt, {
                    name: e.icon,
                    class: gn("icon"),
                    classPrefix: e.iconPrefix
                }, null)
        }
          , i = () => {
            let u;
            if (e.loading ? u = e.loadingText : u = n.default ? n.default() : e.text,
            u)
                return P("span", {
                    class: gn("text")
                }, [u])
        }
          , l = () => {
            const {color: u, plain: a} = e;
            if (u) {
                const f = {
                    color: a ? u : "white"
                };
                return a || (f.background = u),
                u.includes("gradient") ? f.border = 0 : f.borderColor = u,
                f
            }
        }
          , c = u => {
            e.loading ? un(u) : e.disabled || (t("click", u),
            r())
        }
        ;
        return () => {
            const {tag: u, type: a, size: f, block: d, round: g, plain: y, square: b, loading: w, disabled: _, hairline: S, nativeType: I, iconPosition: O} = e
              , k = [gn([a, f, {
                plain: y,
                block: d,
                round: g,
                square: b,
                loading: w,
                disabled: _,
                hairline: S
            }]), {
                [Hh]: S
            }];
            return P(u, {
                type: I,
                class: k,
                style: l(),
                disabled: _,
                onClick: c
            }, {
                default: () => [P("div", {
                    class: gn("content")
                }, [O === "left" && o(), i(), O === "right" && o()])]
            })
        }
    }
});
const Kr = ke(gp)
  , [mp,yp] = Oe("action-bar-button")
  , bp = We({}, gs, {
    type: String,
    text: String,
    icon: String,
    color: String,
    loading: Boolean,
    disabled: Boolean
});
var vp = Ce({
    name: mp,
    props: bp,
    setup(e, {slots: t}) {
        const n = Qo()
          , {parent: r, index: s} = mr(Ia)
          , o = G( () => {
            if (r) {
                const l = r.children[s.value - 1];
                return !(l && "isButton"in l)
            }
        }
        )
          , i = G( () => {
            if (r) {
                const l = r.children[s.value + 1];
                return !(l && "isButton"in l)
            }
        }
        );
        return $t({
            isButton: !0
        }),
        () => {
            const {type: l, icon: c, text: u, color: a, loading: f, disabled: d} = e;
            return P(Kr, {
                class: yp([l, {
                    last: i.value,
                    first: o.value
                }]),
                size: "large",
                type: l,
                icon: c,
                color: a,
                loading: f,
                disabled: d,
                onClick: n
            }, {
                default: () => [t.default ? t.default() : u]
            })
        }
    }
});
const ol = ke(vp)
  , ms = {
    show: Boolean,
    zIndex: pe,
    overlay: Re,
    duration: pe,
    teleport: [String, Object],
    lockScroll: Re,
    lazyRender: Re,
    beforeClose: Function,
    overlayStyle: Object,
    overlayClass: kt,
    transitionAppear: Boolean,
    closeOnClickOverlay: Re
}
  , wp = Object.keys(ms);
function Sp(e, t) {
    return e > t ? "horizontal" : t > e ? "vertical" : ""
}
function Na() {
    const e = se(0)
      , t = se(0)
      , n = se(0)
      , r = se(0)
      , s = se(0)
      , o = se(0)
      , i = se("")
      , l = se(!0)
      , c = () => i.value === "vertical"
      , u = () => i.value === "horizontal"
      , a = () => {
        n.value = 0,
        r.value = 0,
        s.value = 0,
        o.value = 0,
        i.value = "",
        l.value = !0
    }
    ;
    return {
        move: g => {
            const y = g.touches[0];
            n.value = (y.clientX < 0 ? 0 : y.clientX) - e.value,
            r.value = y.clientY - t.value,
            s.value = Math.abs(n.value),
            o.value = Math.abs(r.value);
            const b = 10;
            (!i.value || s.value < b && o.value < b) && (i.value = Sp(s.value, o.value)),
            l.value && (s.value > tl || o.value > tl) && (l.value = !1)
        }
        ,
        start: g => {
            a(),
            e.value = g.touches[0].clientX,
            t.value = g.touches[0].clientY
        }
        ,
        reset: a,
        startX: e,
        startY: t,
        deltaX: n,
        deltaY: r,
        offsetX: s,
        offsetY: o,
        direction: i,
        isVertical: c,
        isHorizontal: u,
        isTap: l
    }
}
let Ln = 0;
const il = "van-overflow-hidden";
function _p(e, t) {
    const n = Na()
      , r = "01"
      , s = "10"
      , o = a => {
        n.move(a);
        const f = n.deltaY.value > 0 ? s : r
          , d = Ea(a.target, e.value)
          , {scrollHeight: g, offsetHeight: y, scrollTop: b} = d;
        let w = "11";
        b === 0 ? w = y >= g ? "00" : "01" : b + y >= g && (w = "10"),
        w !== "11" && n.isVertical() && !(parseInt(w, 2) & parseInt(f, 2)) && un(a, !0)
    }
      , i = () => {
        document.addEventListener("touchstart", n.start),
        document.addEventListener("touchmove", o, {
            passive: !1
        }),
        Ln || document.body.classList.add(il),
        Ln++
    }
      , l = () => {
        Ln && (document.removeEventListener("touchstart", n.start),
        document.removeEventListener("touchmove", o),
        Ln--,
        Ln || document.body.classList.remove(il))
    }
      , c = () => t() && i()
      , u = () => t() && l();
    ds(c),
    Tn(u),
    pr(u),
    we(t, a => {
        a ? i() : l()
    }
    )
}
function La(e) {
    const t = se(!1);
    return we(e, n => {
        n && (t.value = n)
    }
    , {
        immediate: !0
    }),
    n => () => t.value ? n() : null
}
const ll = () => {
    var e;
    const {scopeId: t} = ((e = Qt()) == null ? void 0 : e.vnode) || {};
    return t ? {
        [t]: ""
    } : null
}
  , [Ep,xp] = Oe("overlay")
  , Cp = {
    show: Boolean,
    zIndex: pe,
    duration: pe,
    className: kt,
    lockScroll: Re,
    lazyRender: Re,
    customStyle: Object,
    teleport: [String, Object]
};
var Tp = Ce({
    name: Ep,
    props: Cp,
    setup(e, {slots: t}) {
        const n = se()
          , r = La( () => e.show || !e.lazyRender)
          , s = i => {
            e.lockScroll && un(i, !0)
        }
          , o = r( () => {
            var i;
            const l = We(Ca(e.zIndex), e.customStyle);
            return Be(e.duration) && (l.animationDuration = `${e.duration}s`),
            Ho(P("div", {
                ref: n,
                style: l,
                class: [xp(), e.className]
            }, [(i = t.default) == null ? void 0 : i.call(t)]), [[Wo, e.show]])
        }
        );
        return An("touchmove", s, {
            target: n
        }),
        () => {
            const i = P(ba, {
                name: "van-fade",
                appear: !0
            }, {
                default: o
            });
            return e.teleport ? P($c, {
                to: e.teleport
            }, {
                default: () => [i]
            }) : i
        }
    }
});
const Rp = ke(Tp)
  , Ap = We({}, ms, {
    round: Boolean,
    position: _e("center"),
    closeIcon: _e("cross"),
    closeable: Boolean,
    transition: String,
    iconPrefix: String,
    closeOnPopstate: Boolean,
    closeIconPosition: _e("top-right"),
    destroyOnClose: Boolean,
    safeAreaInsetTop: Boolean,
    safeAreaInsetBottom: Boolean
})
  , [Op,cl] = Oe("popup");
var Pp = Ce({
    name: Op,
    inheritAttrs: !1,
    props: Ap,
    emits: ["open", "close", "opened", "closed", "keydown", "update:show", "clickOverlay", "clickCloseIcon"],
    setup(e, {emit: t, attrs: n, slots: r}) {
        let s, o;
        const i = se()
          , l = se()
          , c = La( () => e.show || !e.lazyRender)
          , u = G( () => {
            const F = {
                zIndex: i.value
            };
            if (Be(e.duration)) {
                const V = e.position === "center" ? "animationDuration" : "transitionDuration";
                F[V] = `${e.duration}s`
            }
            return F
        }
        )
          , a = () => {
            s || (s = !0,
            i.value = e.zIndex !== void 0 ? +e.zIndex : ep(),
            t("open"))
        }
          , f = () => {
            s && Xo(e.beforeClose, {
                done() {
                    s = !1,
                    t("close"),
                    t("update:show", !1)
                }
            })
        }
          , d = F => {
            t("clickOverlay", F),
            e.closeOnClickOverlay && f()
        }
          , g = () => {
            if (e.overlay)
                return P(Rp, xt({
                    show: e.show,
                    class: e.overlayClass,
                    zIndex: i.value,
                    duration: e.duration,
                    customStyle: e.overlayStyle,
                    role: e.closeOnClickOverlay ? "button" : void 0,
                    tabindex: e.closeOnClickOverlay ? 0 : void 0
                }, ll(), {
                    onClick: d
                }), {
                    default: r["overlay-content"]
                })
        }
          , y = F => {
            t("clickCloseIcon", F),
            f()
        }
          , b = () => {
            if (e.closeable)
                return P(Jt, {
                    role: "button",
                    tabindex: 0,
                    name: e.closeIcon,
                    class: [cl("close-icon", e.closeIconPosition), Vh],
                    classPrefix: e.iconPrefix,
                    onClick: y
                }, null)
        }
        ;
        let w;
        const _ = () => {
            w && clearTimeout(w),
            w = setTimeout( () => {
                t("opened")
            }
            )
        }
          , S = () => t("closed")
          , I = F => t("keydown", F)
          , O = c( () => {
            var F;
            const {destroyOnClose: V, round: U, position: B, safeAreaInsetTop: z, safeAreaInsetBottom: X, show: $} = e;
            if (!(!$ && V))
                return Ho(P("div", xt({
                    ref: l,
                    style: u.value,
                    role: "dialog",
                    tabindex: 0,
                    class: [cl({
                        round: U,
                        [B]: B
                    }), {
                        "van-safe-area-top": z,
                        "van-safe-area-bottom": X
                    }],
                    onKeydown: I
                }, n, ll()), [(F = r.default) == null ? void 0 : F.call(r), b()]), [[Wo, $]])
        }
        )
          , k = () => {
            const {position: F, transition: V, transitionAppear: U} = e
              , B = F === "center" ? "van-fade" : `van-popup-slide-${F}`;
            return P(ba, {
                name: V || B,
                appear: U,
                onAfterEnter: _,
                onAfterLeave: S
            }, {
                default: O
            })
        }
        ;
        return we( () => e.show, F => {
            F && !s && (a(),
            n.tabindex === 0 && Pe( () => {
                var V;
                (V = l.value) == null || V.focus()
            }
            )),
            !F && s && (s = !1,
            t("close"))
        }
        ),
        $t({
            popupRef: l
        }),
        _p(l, () => e.show && e.lockScroll),
        An("popstate", () => {
            e.closeOnPopstate && (f(),
            o = !1)
        }
        ),
        Ct( () => {
            e.show && a()
        }
        ),
        hr( () => {
            o && (t("update:show", !0),
            o = !1)
        }
        ),
        Tn( () => {
            e.show && e.teleport && (f(),
            o = !0)
        }
        ),
        Wt(Oa, () => e.show),
        () => e.teleport ? P($c, {
            to: e.teleport
        }, {
            default: () => [g(), k()]
        }) : P(at, null, [g(), k()])
    }
});
const ei = ke(Pp);
function Ip(e, t, n) {
    let r, s = 0;
    const o = e.scrollLeft
      , i = n === 0 ? 1 : Math.round(n * 1e3 / 16);
    let l = o;
    function c() {
        Sa(r)
    }
    function u() {
        l += (t - o) / i,
        e.scrollLeft = l,
        ++s < i && (r = ir(u))
    }
    return u(),
    c
}
function Bp(e, t, n, r) {
    let s, o = Jo(e);
    const i = o < t
      , l = n === 0 ? 1 : Math.round(n * 1e3 / 16)
      , c = (t - o) / l;
    function u() {
        Sa(s)
    }
    function a() {
        o += c,
        (i && o > t || !i && o < t) && (o = t),
        po(e, o),
        i && o < t || !i && o > t ? s = ir(a) : s = ir(r)
    }
    return a(),
    u
}
let kp = 0;
function ti() {
    const e = Qt()
      , {name: t="unknown"} = (e == null ? void 0 : e.type) || {};
    return `${t}-${++kp}`
}
function Np() {
    const e = se([])
      , t = [];
    return qc( () => {
        e.value = []
    }
    ),
    [e, r => (t[r] || (t[r] = s => {
        e.value[r] = s
    }
    ),
    t[r])]
}
function $a(e, t) {
    if (!Yr || !window.IntersectionObserver)
        return;
    const n = new IntersectionObserver(o => {
        t(o[0].intersectionRatio > 0)
    }
    ,{
        root: document.body
    })
      , r = () => {
        e.value && n.observe(e.value)
    }
      , s = () => {
        e.value && n.unobserve(e.value)
    }
    ;
    Tn(s),
    pr(s),
    ds(r)
}
const [Lp,$p] = Oe("sticky")
  , Fp = {
    zIndex: pe,
    position: _e("top"),
    container: Object,
    offsetTop: ut(0),
    offsetBottom: ut(0)
};
var Dp = Ce({
    name: Lp,
    props: Fp,
    emits: ["scroll", "change"],
    setup(e, {emit: t, slots: n}) {
        const r = se()
          , s = xa(r)
          , o = Ke({
            fixed: !1,
            width: 0,
            height: 0,
            transform: 0
        })
          , i = se(!1)
          , l = G( () => Ta(e.position === "top" ? e.offsetTop : e.offsetBottom))
          , c = G( () => {
            if (i.value)
                return;
            const {fixed: d, height: g, width: y} = o;
            if (d)
                return {
                    width: `${y}px`,
                    height: `${g}px`
                }
        }
        )
          , u = G( () => {
            if (!o.fixed || i.value)
                return;
            const d = We(Ca(e.zIndex), {
                width: `${o.width}px`,
                height: `${o.height}px`,
                [e.position]: `${l.value}px`
            });
            return o.transform && (d.transform = `translate3d(0, ${o.transform}px, 0)`),
            d
        }
        )
          , a = d => t("scroll", {
            scrollTop: d,
            isFixed: o.fixed
        })
          , f = () => {
            if (!r.value || lr(r))
                return;
            const {container: d, position: g} = e
              , y = zt(r)
              , b = Jo(window);
            if (o.width = y.width,
            o.height = y.height,
            g === "top")
                if (d) {
                    const w = zt(d)
                      , _ = w.bottom - l.value - o.height;
                    o.fixed = l.value > y.top && w.bottom > 0,
                    o.transform = _ < 0 ? _ : 0
                } else
                    o.fixed = l.value > y.top;
            else {
                const {clientHeight: w} = document.documentElement;
                if (d) {
                    const _ = zt(d)
                      , S = w - _.top - l.value - o.height;
                    o.fixed = w - l.value < y.bottom && w > _.top,
                    o.transform = S < 0 ? -S : 0
                } else
                    o.fixed = w - l.value < y.bottom
            }
            a(b)
        }
        ;
        return we( () => o.fixed, d => t("change", d)),
        An("scroll", f, {
            target: s,
            passive: !0
        }),
        $a(r, f),
        we([yr, hs], () => {
            !r.value || lr(r) || !o.fixed || (i.value = !0,
            Pe( () => {
                const d = zt(r);
                o.width = d.width,
                o.height = d.height,
                i.value = !1
            }
            ))
        }
        ),
        () => {
            var d;
            return P("div", {
                ref: r,
                style: c.value
            }, [P("div", {
                class: $p({
                    fixed: o.fixed && !i.value
                }),
                style: u.value
            }, [(d = n.default) == null ? void 0 : d.call(n)])])
        }
    }
});
const Mp = ke(Dp)
  , [Fa,Rr] = Oe("swipe")
  , jp = {
    loop: Re,
    width: pe,
    height: pe,
    vertical: Boolean,
    autoplay: ut(0),
    duration: ut(500),
    touchable: Re,
    lazyRender: Boolean,
    initialSwipe: ut(0),
    indicatorColor: String,
    showIndicators: Re,
    stopPropagation: Re
}
  , Da = Symbol(Fa);
var Hp = Ce({
    name: Fa,
    props: jp,
    emits: ["change", "dragStart", "dragEnd"],
    setup(e, {emit: t, slots: n}) {
        const r = se()
          , s = se()
          , o = Ke({
            rect: null,
            width: 0,
            height: 0,
            offset: 0,
            active: 0,
            swiping: !1
        });
        let i = !1;
        const l = Na()
          , {children: c, linkChildren: u} = fs(Da)
          , a = G( () => c.length)
          , f = G( () => o[e.vertical ? "height" : "width"])
          , d = G( () => e.vertical ? l.deltaY.value : l.deltaX.value)
          , g = G( () => o.rect ? (e.vertical ? o.rect.height : o.rect.width) - f.value * a.value : 0)
          , y = G( () => f.value ? Math.ceil(Math.abs(g.value) / f.value) : a.value)
          , b = G( () => a.value * f.value)
          , w = G( () => (o.active + a.value) % a.value)
          , _ = G( () => {
            const K = e.vertical ? "vertical" : "horizontal";
            return l.direction.value === K
        }
        )
          , S = G( () => {
            const K = {
                transitionDuration: `${o.swiping ? 0 : e.duration}ms`,
                transform: `translate${e.vertical ? "Y" : "X"}(${+o.offset.toFixed(2)}px)`
            };
            if (f.value) {
                const ie = e.vertical ? "height" : "width"
                  , fe = e.vertical ? "width" : "height";
                K[ie] = `${b.value}px`,
                K[fe] = e[fe] ? `${e[fe]}px` : ""
            }
            return K
        }
        )
          , I = K => {
            const {active: ie} = o;
            return K ? e.loop ? kr(ie + K, -1, a.value) : kr(ie + K, 0, y.value) : ie
        }
          , O = (K, ie=0) => {
            let fe = K * f.value;
            e.loop || (fe = Math.min(fe, -g.value));
            let xe = ie - fe;
            return e.loop || (xe = kr(xe, g.value, 0)),
            xe
        }
          , k = ({pace: K=0, offset: ie=0, emitChange: fe}) => {
            if (a.value <= 1)
                return;
            const {active: xe} = o
              , H = I(K)
              , W = O(H, ie);
            if (e.loop) {
                if (c[0] && W !== g.value) {
                    const p = W < g.value;
                    c[0].setOffset(p ? b.value : 0)
                }
                if (c[a.value - 1] && W !== 0) {
                    const p = W > 0;
                    c[a.value - 1].setOffset(p ? -b.value : 0)
                }
            }
            o.active = H,
            o.offset = W,
            fe && H !== xe && t("change", w.value)
        }
          , F = () => {
            o.swiping = !0,
            o.active <= -1 ? k({
                pace: a.value
            }) : o.active >= a.value && k({
                pace: -a.value
            })
        }
          , V = () => {
            F(),
            l.reset(),
            Mn( () => {
                o.swiping = !1,
                k({
                    pace: -1,
                    emitChange: !0
                })
            }
            )
        }
          , U = () => {
            F(),
            l.reset(),
            Mn( () => {
                o.swiping = !1,
                k({
                    pace: 1,
                    emitChange: !0
                })
            }
            )
        }
        ;
        let B;
        const z = () => clearTimeout(B)
          , X = () => {
            z(),
            +e.autoplay > 0 && a.value > 1 && (B = setTimeout( () => {
                U(),
                X()
            }
            , +e.autoplay))
        }
          , $ = (K=+e.initialSwipe) => {
            if (!r.value)
                return;
            const ie = () => {
                var fe, xe;
                if (!lr(r)) {
                    const H = {
                        width: r.value.offsetWidth,
                        height: r.value.offsetHeight
                    };
                    o.rect = H,
                    o.width = +((fe = e.width) != null ? fe : H.width),
                    o.height = +((xe = e.height) != null ? xe : H.height)
                }
                a.value && (K = Math.min(a.value - 1, K),
                K === -1 && (K = a.value - 1)),
                o.active = K,
                o.swiping = !0,
                o.offset = O(K),
                c.forEach(H => {
                    H.setOffset(0)
                }
                ),
                X()
            }
            ;
            lr(r) ? Pe().then(ie) : ie()
        }
          , ne = () => $(o.active);
        let me;
        const Te = K => {
            !e.touchable || K.touches.length > 1 || (l.start(K),
            i = !1,
            me = Date.now(),
            z(),
            F())
        }
          , ae = K => {
            e.touchable && o.swiping && (l.move(K),
            _.value && (!e.loop && (o.active === 0 && d.value > 0 || o.active === a.value - 1 && d.value < 0) || (un(K, e.stopPropagation),
            k({
                offset: d.value
            }),
            i || (t("dragStart", {
                index: w.value
            }),
            i = !0))))
        }
          , Q = () => {
            if (!e.touchable || !o.swiping)
                return;
            const K = Date.now() - me
              , ie = d.value / K;
            if ((Math.abs(ie) > .25 || Math.abs(d.value) > f.value / 2) && _.value) {
                const xe = e.vertical ? l.offsetY.value : l.offsetX.value;
                let H = 0;
                e.loop ? H = xe > 0 ? d.value > 0 ? -1 : 1 : 0 : H = -Math[d.value > 0 ? "ceil" : "floor"](d.value / f.value),
                k({
                    pace: H,
                    emitChange: !0
                })
            } else
                d.value && k({
                    pace: 0
                });
            i = !1,
            o.swiping = !1,
            t("dragEnd", {
                index: w.value
            }),
            X()
        }
          , le = (K, ie={}) => {
            F(),
            l.reset(),
            Mn( () => {
                let fe;
                e.loop && K === a.value ? fe = o.active === 0 ? 0 : K : fe = K % a.value,
                ie.immediate ? Mn( () => {
                    o.swiping = !1
                }
                ) : o.swiping = !1,
                k({
                    pace: fe - o.active,
                    emitChange: !0
                })
            }
            )
        }
          , je = (K, ie) => {
            const fe = ie === w.value
              , xe = fe ? {
                backgroundColor: e.indicatorColor
            } : void 0;
            return P("i", {
                style: xe,
                class: Rr("indicator", {
                    active: fe
                })
            }, null)
        }
          , Je = () => {
            if (n.indicator)
                return n.indicator({
                    active: w.value,
                    total: a.value
                });
            if (e.showIndicators && a.value > 1)
                return P("div", {
                    class: Rr("indicators", {
                        vertical: e.vertical
                    })
                }, [Array(a.value).fill("").map(je)])
        }
        ;
        return $t({
            prev: V,
            next: U,
            state: o,
            resize: ne,
            swipeTo: le
        }),
        u({
            size: f,
            props: e,
            count: a,
            activeIndicator: w
        }),
        we( () => e.initialSwipe, K => $(+K)),
        we(a, () => $(o.active)),
        we( () => e.autoplay, X),
        we([yr, hs, () => e.width, () => e.height], ne),
        we(wh(), K => {
            K === "visible" ? X() : z()
        }
        ),
        Ct($),
        hr( () => $(o.active)),
        Zo( () => $(o.active)),
        Tn(z),
        pr(z),
        An("touchmove", ae, {
            target: s
        }),
        () => {
            var K;
            return P("div", {
                ref: r,
                class: Rr()
            }, [P("div", {
                ref: s,
                style: S.value,
                class: Rr("track", {
                    vertical: e.vertical
                }),
                onTouchstartPassive: Te,
                onTouchend: Q,
                onTouchcancel: Q
            }, [(K = n.default) == null ? void 0 : K.call(n)]), Je()])
        }
    }
});
const Up = ke(Hp)
  , [Vp,al] = Oe("tabs");
var zp = Ce({
    name: Vp,
    props: {
        count: Rs(Number),
        inited: Boolean,
        animated: Boolean,
        duration: Rs(pe),
        swipeable: Boolean,
        lazyRender: Boolean,
        currentIndex: Rs(Number)
    },
    emits: ["change"],
    setup(e, {emit: t, slots: n}) {
        const r = se()
          , s = l => t("change", l)
          , o = () => {
            var l;
            const c = (l = n.default) == null ? void 0 : l.call(n);
            return e.animated || e.swipeable ? P(Up, {
                ref: r,
                loop: !1,
                class: al("track"),
                duration: +e.duration * 1e3,
                touchable: e.swipeable,
                lazyRender: e.lazyRender,
                showIndicators: !1,
                onChange: s
            }, {
                default: () => [c]
            }) : c
        }
          , i = l => {
            const c = r.value;
            c && c.state.active !== l && c.swipeTo(l, {
                immediate: !e.inited
            })
        }
        ;
        return we( () => e.currentIndex, i),
        Ct( () => {
            i(e.currentIndex)
        }
        ),
        $t({
            swipeRef: r
        }),
        () => P("div", {
            class: al("content", {
                animated: e.animated || e.swipeable
            })
        }, [o()])
    }
});
const [Ma,Ar] = Oe("tabs")
  , qp = {
    type: _e("line"),
    color: String,
    border: Boolean,
    sticky: Boolean,
    shrink: Boolean,
    active: ut(0),
    duration: ut(.3),
    animated: Boolean,
    ellipsis: Re,
    swipeable: Boolean,
    scrollspy: Boolean,
    offsetTop: ut(0),
    background: String,
    lazyRender: Re,
    showHeader: Re,
    lineWidth: pe,
    lineHeight: pe,
    beforeChange: Function,
    swipeThreshold: ut(5),
    titleActiveColor: String,
    titleInactiveColor: String
}
  , ja = Symbol(Ma);
var Kp = Ce({
    name: Ma,
    props: qp,
    emits: ["change", "scroll", "rendered", "clickTab", "update:active"],
    setup(e, {emit: t, slots: n}) {
        let r, s, o, i, l;
        const c = se()
          , u = se()
          , a = se()
          , f = se()
          , d = ti()
          , g = xa(c)
          , [y,b] = Np()
          , {children: w, linkChildren: _} = fs(ja)
          , S = Ke({
            inited: !1,
            position: "",
            lineStyle: {},
            currentIndex: -1
        })
          , I = G( () => w.length > +e.swipeThreshold || !e.ellipsis || e.shrink)
          , O = G( () => ({
            borderColor: e.color,
            background: e.background
        }))
          , k = (H, W) => {
            var p;
            return (p = H.name) != null ? p : W
        }
          , F = G( () => {
            const H = w[S.currentIndex];
            if (H)
                return k(H, S.currentIndex)
        }
        )
          , V = G( () => Ta(e.offsetTop))
          , U = G( () => e.sticky ? V.value + r : 0)
          , B = H => {
            const W = u.value
              , p = y.value;
            if (!I.value || !W || !p || !p[S.currentIndex])
                return;
            const T = p[S.currentIndex].$el
              , x = T.offsetLeft - (W.offsetWidth - T.offsetWidth) / 2;
            i && i(),
            i = Ip(W, x, H ? 0 : +e.duration)
        }
          , z = () => {
            const H = S.inited;
            Pe( () => {
                const W = y.value;
                if (!W || !W[S.currentIndex] || e.type !== "line" || lr(c.value))
                    return;
                const p = W[S.currentIndex].$el
                  , {lineWidth: T, lineHeight: x} = e
                  , L = p.offsetLeft + p.offsetWidth / 2
                  , J = {
                    width: De(T),
                    backgroundColor: e.color,
                    transform: `translateX(${L}px) translateX(-50%)`
                };
                if (H && (J.transitionDuration = `${e.duration}s`),
                Be(x)) {
                    const h = De(x);
                    J.height = h,
                    J.borderRadius = h
                }
                S.lineStyle = J
            }
            )
        }
          , X = H => {
            const W = H < S.currentIndex ? -1 : 1;
            for (; H >= 0 && H < w.length; ) {
                if (!w[H].disabled)
                    return H;
                H += W
            }
        }
          , $ = (H, W) => {
            const p = X(H);
            if (!Be(p))
                return;
            const T = w[p]
              , x = k(T, p)
              , L = S.currentIndex !== null;
            S.currentIndex !== p && (S.currentIndex = p,
            W || B(),
            z()),
            x !== e.active && (t("update:active", x),
            L && t("change", x, T.title)),
            o && !e.scrollspy && Go(Math.ceil(Xi(c.value) - V.value))
        }
          , ne = (H, W) => {
            const p = w.find( (x, L) => k(x, L) === H)
              , T = p ? w.indexOf(p) : 0;
            $(T, W)
        }
          , me = (H=!1) => {
            if (e.scrollspy) {
                const W = w[S.currentIndex].$el;
                if (W && g.value) {
                    const p = Xi(W, g.value) - U.value;
                    s = !0,
                    l && l(),
                    l = Bp(g.value, p, H ? 0 : +e.duration, () => {
                        s = !1
                    }
                    )
                }
            }
        }
          , Te = (H, W, p) => {
            const {title: T, disabled: x} = w[W]
              , L = k(w[W], W);
            x || (Xo(e.beforeChange, {
                args: [L],
                done: () => {
                    $(W),
                    me()
                }
            }),
            Ba(H)),
            t("clickTab", {
                name: L,
                title: T,
                event: p,
                disabled: x
            })
        }
          , ae = H => {
            o = H.isFixed,
            t("scroll", H)
        }
          , Q = H => {
            Pe( () => {
                ne(H),
                me(!0)
            }
            )
        }
          , le = () => {
            for (let H = 0; H < w.length; H++) {
                const {top: W} = zt(w[H].$el);
                if (W > U.value)
                    return H === 0 ? 0 : H - 1
            }
            return w.length - 1
        }
          , je = () => {
            if (e.scrollspy && !s) {
                const H = le();
                $(H)
            }
        }
          , Je = () => {
            if (e.type === "line" && w.length)
                return P("div", {
                    class: Ar("line"),
                    style: S.lineStyle
                }, null)
        }
          , K = () => {
            var H, W, p;
            const {type: T, border: x, sticky: L} = e
              , J = [P("div", {
                ref: L ? void 0 : a,
                class: [Ar("wrap"), {
                    [Uh]: T === "line" && x
                }]
            }, [P("div", {
                ref: u,
                role: "tablist",
                class: Ar("nav", [T, {
                    shrink: e.shrink,
                    complete: I.value
                }]),
                style: O.value,
                "aria-orientation": "horizontal"
            }, [(H = n["nav-left"]) == null ? void 0 : H.call(n), w.map(h => h.renderTitle(Te)), Je(), (W = n["nav-right"]) == null ? void 0 : W.call(n)])]), (p = n["nav-bottom"]) == null ? void 0 : p.call(n)];
            return L ? P("div", {
                ref: a
            }, [J]) : J
        }
          , ie = () => {
            z(),
            Pe( () => {
                var H, W;
                B(!0),
                (W = (H = f.value) == null ? void 0 : H.swipeRef.value) == null || W.resize()
            }
            )
        }
        ;
        we( () => [e.color, e.duration, e.lineWidth, e.lineHeight], z),
        we(yr, ie),
        we( () => e.active, H => {
            H !== F.value && ne(H)
        }
        ),
        we( () => w.length, () => {
            S.inited && (ne(e.active),
            z(),
            Pe( () => {
                B(!0)
            }
            ))
        }
        );
        const fe = () => {
            ne(e.active, !0),
            Pe( () => {
                S.inited = !0,
                a.value && (r = zt(a.value).height),
                B(!0)
            }
            )
        }
          , xe = (H, W) => t("rendered", H, W);
        return $t({
            resize: ie,
            scrollTo: Q
        }),
        hr(z),
        Zo(z),
        ds(fe),
        $a(c, z),
        An("scroll", je, {
            target: g,
            passive: !0
        }),
        _({
            id: d,
            props: e,
            setLine: z,
            scrollable: I,
            onRendered: xe,
            currentName: F,
            setTitleRefs: b,
            scrollIntoView: B
        }),
        () => P("div", {
            ref: c,
            class: Ar([e.type])
        }, [e.showHeader ? e.sticky ? P(Mp, {
            container: c.value,
            offsetTop: V.value,
            onScroll: ae
        }, {
            default: () => [K()]
        }) : K() : null, P(zp, {
            ref: f,
            count: w.length,
            inited: S.inited,
            animated: e.animated,
            duration: e.duration,
            swipeable: e.swipeable,
            lazyRender: e.lazyRender,
            currentIndex: S.currentIndex,
            onChange: $
        }, {
            default: () => {
                var H;
                return [(H = n.default) == null ? void 0 : H.call(n)]
            }
        })])
    }
});
const Wp = Symbol()
  , [Jp,ul] = Oe("tab")
  , Yp = Ce({
    name: Jp,
    props: {
        id: String,
        dot: Boolean,
        type: String,
        color: String,
        title: String,
        badge: pe,
        shrink: Boolean,
        isActive: Boolean,
        disabled: Boolean,
        controls: String,
        scrollable: Boolean,
        activeColor: String,
        inactiveColor: String,
        showZeroBadge: Re
    },
    setup(e, {slots: t}) {
        const n = G( () => {
            const s = {}
              , {type: o, color: i, disabled: l, isActive: c, activeColor: u, inactiveColor: a} = e;
            i && o === "card" && (s.borderColor = i,
            l || (c ? s.backgroundColor = i : s.color = i));
            const d = c ? u : a;
            return d && (s.color = d),
            s
        }
        )
          , r = () => {
            const s = P("span", {
                class: ul("text", {
                    ellipsis: !e.scrollable
                })
            }, [t.title ? t.title() : e.title]);
            return e.dot || Be(e.badge) && e.badge !== "" ? P(ka, {
                dot: e.dot,
                content: e.badge,
                showZero: e.showZeroBadge
            }, {
                default: () => [s]
            }) : s
        }
        ;
        return () => P("div", {
            id: e.id,
            role: "tab",
            class: [ul([e.type, {
                grow: e.scrollable && !e.shrink,
                shrink: e.shrink,
                active: e.isActive,
                disabled: e.disabled
            }])],
            style: n.value,
            tabindex: e.disabled ? void 0 : e.isActive ? 0 : -1,
            "aria-selected": e.isActive,
            "aria-disabled": e.disabled || void 0,
            "aria-controls": e.controls,
            "data-allow-mismatch": "attribute"
        }, [r()])
    }
})
  , [Gp,Xp] = Oe("swipe-item");
var Zp = Ce({
    name: Gp,
    setup(e, {slots: t}) {
        let n;
        const r = Ke({
            offset: 0,
            inited: !1,
            mounted: !1
        })
          , {parent: s, index: o} = mr(Da);
        if (!s)
            return;
        const i = G( () => {
            const u = {}
              , {vertical: a} = s.props;
            return s.size.value && (u[a ? "height" : "width"] = `${s.size.value}px`),
            r.offset && (u.transform = `translate${a ? "Y" : "X"}(${r.offset}px)`),
            u
        }
        )
          , l = G( () => {
            const {loop: u, lazyRender: a} = s.props;
            if (!a || n)
                return !0;
            if (!r.mounted)
                return !1;
            const f = s.activeIndicator.value
              , d = s.count.value - 1
              , g = f === 0 && u ? d : f - 1
              , y = f === d && u ? 0 : f + 1;
            return n = o.value === f || o.value === g || o.value === y,
            n
        }
        )
          , c = u => {
            r.offset = u
        }
        ;
        return Ct( () => {
            Pe( () => {
                r.mounted = !0
            }
            )
        }
        ),
        $t({
            setOffset: c
        }),
        () => {
            var u;
            return P("div", {
                class: Xp(),
                style: i.value
            }, [l.value ? (u = t.default) == null ? void 0 : u.call(t) : null])
        }
    }
});
const Qp = ke(Zp)
  , [eg,Vs] = Oe("tab")
  , tg = We({}, gs, {
    dot: Boolean,
    name: pe,
    badge: pe,
    title: String,
    disabled: Boolean,
    titleClass: kt,
    titleStyle: [String, Object],
    showZeroBadge: Re
});
var ng = Ce({
    name: eg,
    props: tg,
    setup(e, {slots: t}) {
        const n = ti()
          , r = se(!1)
          , s = Qt()
          , {parent: o, index: i} = mr(ja);
        if (!o)
            return;
        const l = () => {
            var y;
            return (y = e.name) != null ? y : i.value
        }
          , c = () => {
            r.value = !0,
            o.props.lazyRender && Pe( () => {
                o.onRendered(l(), e.title)
            }
            )
        }
          , u = G( () => {
            const y = l() === o.currentName.value;
            return y && !r.value && c(),
            y
        }
        )
          , a = se("")
          , f = se("");
        md( () => {
            const {titleClass: y, titleStyle: b} = e;
            a.value = y ? ns(y) : "",
            f.value = b && typeof b != "string" ? Yu(ts(b)) : b
        }
        );
        const d = y => P(Yp, xt({
            key: n,
            id: `${o.id}-${i.value}`,
            ref: o.setTitleRefs(i.value),
            style: f.value,
            class: a.value,
            isActive: u.value,
            controls: n,
            scrollable: o.scrollable.value,
            activeColor: o.props.titleActiveColor,
            inactiveColor: o.props.titleInactiveColor,
            onClick: b => y(s.proxy, i.value, b)
        }, Fr(o.props, ["type", "color", "shrink"]), Fr(e, ["dot", "badge", "title", "disabled", "showZeroBadge"])), {
            title: t.title
        })
          , g = se(!u.value);
        return we(u, y => {
            y ? g.value = !1 : Mn( () => {
                g.value = !0
            }
            )
        }
        ),
        we( () => e.title, () => {
            o.setLine(),
            o.scrollIntoView()
        }
        ),
        Wt(Wp, u),
        $t({
            id: n,
            renderTitle: d
        }),
        () => {
            var y;
            const b = `${o.id}-${i.value}`
              , {animated: w, swipeable: _, scrollspy: S, lazyRender: I} = o.props;
            if (!t.default && !w)
                return;
            const O = S || u.value;
            if (w || _)
                return P(Qp, {
                    id: n,
                    role: "tabpanel",
                    class: Vs("panel-wrapper", {
                        inactive: g.value
                    }),
                    tabindex: u.value ? 0 : -1,
                    "aria-hidden": !u.value,
                    "aria-labelledby": b,
                    "data-allow-mismatch": "attribute"
                }, {
                    default: () => {
                        var V;
                        return [P("div", {
                            class: Vs("panel")
                        }, [(V = t.default) == null ? void 0 : V.call(t)])]
                    }
                });
            const F = r.value || S || !I ? (y = t.default) == null ? void 0 : y.call(t) : null;
            return Ho(P("div", {
                id: n,
                role: "tabpanel",
                class: Vs("panel"),
                tabindex: O ? 0 : -1,
                "aria-labelledby": b,
                "data-allow-mismatch": "attribute"
            }, [F]), [[Wo, O]])
        }
    }
});
const rg = ke(ng)
  , sg = ke(Kp)
  , [og,mn] = Oe("cell")
  , Ha = {
    tag: _e("div"),
    icon: String,
    size: String,
    title: pe,
    value: pe,
    label: pe,
    center: Boolean,
    isLink: Boolean,
    border: Re,
    iconPrefix: String,
    valueClass: kt,
    labelClass: kt,
    titleClass: kt,
    titleStyle: null,
    arrowDirection: String,
    required: {
        type: [Boolean, String],
        default: null
    },
    clickable: {
        type: Boolean,
        default: null
    }
}
  , ig = We({}, Ha, gs);
var lg = Ce({
    name: og,
    props: ig,
    setup(e, {slots: t}) {
        const n = Qo()
          , r = () => {
            if (t.label || Be(e.label))
                return P("div", {
                    class: [mn("label"), e.labelClass]
                }, [t.label ? t.label() : e.label])
        }
          , s = () => {
            var c;
            if (t.title || Be(e.title)) {
                const u = (c = t.title) == null ? void 0 : c.call(t);
                return Array.isArray(u) && u.length === 0 ? void 0 : P("div", {
                    class: [mn("title"), e.titleClass],
                    style: e.titleStyle
                }, [u || P("span", null, [e.title]), r()])
            }
        }
          , o = () => {
            const c = t.value || t.default;
            if (c || Be(e.value))
                return P("div", {
                    class: [mn("value"), e.valueClass]
                }, [c ? c() : P("span", null, [e.value])])
        }
          , i = () => {
            if (t.icon)
                return t.icon();
            if (e.icon)
                return P(Jt, {
                    name: e.icon,
                    class: mn("left-icon"),
                    classPrefix: e.iconPrefix
                }, null)
        }
          , l = () => {
            if (t["right-icon"])
                return t["right-icon"]();
            if (e.isLink) {
                const c = e.arrowDirection && e.arrowDirection !== "right" ? `arrow-${e.arrowDirection}` : "arrow";
                return P(Jt, {
                    name: c,
                    class: mn("right-icon")
                }, null)
            }
        }
        ;
        return () => {
            var c;
            const {tag: u, size: a, center: f, border: d, isLink: g, required: y} = e
              , b = (c = e.clickable) != null ? c : g
              , w = {
                center: f,
                required: !!y,
                clickable: b,
                borderless: !d
            };
            return a && (w[a] = !!a),
            P(u, {
                class: mn(w),
                role: b ? "button" : void 0,
                tabindex: b ? 0 : void 0,
                onClick: n
            }, {
                default: () => {
                    var _;
                    return [i(), s(), o(), l(), (_ = t.extra) == null ? void 0 : _.call(t)]
                }
            })
        }
    }
});
const cg = ke(lg);
function Ua(e) {
    return Array.isArray(e) ? !e.length : e === 0 ? !1 : !e
}
function ag(e, t) {
    if (Ua(e)) {
        if (t.required)
            return !1;
        if (t.validateEmpty === !1)
            return !0
    }
    return !(t.pattern && !t.pattern.test(String(e)))
}
function ug(e, t) {
    return new Promise(n => {
        const r = t.validator(e, t);
        if (Xl(r)) {
            r.then(n);
            return
        }
        n(r)
    }
    )
}
function fl(e, t) {
    const {message: n} = t;
    return Xn(n) ? n(e, t) : n || ""
}
function fg({target: e}) {
    e.composing = !0
}
function dl({target: e}) {
    e.composing && (e.composing = !1,
    e.dispatchEvent(new Event("input")))
}
function dg(e, t) {
    const n = Yo();
    e.style.height = "auto";
    let r = e.scrollHeight;
    if (fr(t)) {
        const {maxHeight: s, minHeight: o} = t;
        s !== void 0 && (r = Math.min(r, s)),
        o !== void 0 && (r = Math.max(r, o))
    }
    r && (e.style.height = `${r}px`,
    Go(n))
}
function hg(e, t) {
    return e === "number" && (e = "text",
    t ?? (t = "decimal")),
    e === "digit" && (e = "tel",
    t ?? (t = "numeric")),
    {
        type: e,
        inputmode: t
    }
}
function At(e) {
    return [...e].length
}
function zs(e, t) {
    return [...e].slice(0, t).join("")
}
const [pg,st] = Oe("field")
  , gg = {
    id: String,
    name: String,
    leftIcon: String,
    rightIcon: String,
    autofocus: Boolean,
    clearable: Boolean,
    maxlength: pe,
    max: Number,
    min: Number,
    formatter: Function,
    clearIcon: _e("clear"),
    modelValue: ut(""),
    inputAlign: String,
    placeholder: String,
    autocomplete: String,
    autocapitalize: String,
    autocorrect: String,
    errorMessage: String,
    enterkeyhint: String,
    clearTrigger: _e("focus"),
    formatTrigger: _e("onChange"),
    spellcheck: {
        type: Boolean,
        default: null
    },
    error: {
        type: Boolean,
        default: null
    },
    disabled: {
        type: Boolean,
        default: null
    },
    readonly: {
        type: Boolean,
        default: null
    },
    inputmode: String
}
  , mg = We({}, Ha, gg, {
    rows: pe,
    type: _e("text"),
    rules: Array,
    autosize: [Boolean, Object],
    labelWidth: pe,
    labelClass: kt,
    labelAlign: String,
    showWordLimit: Boolean,
    errorMessageAlign: String,
    colon: {
        type: Boolean,
        default: null
    }
});
var yg = Ce({
    name: pg,
    props: mg,
    emits: ["blur", "focus", "clear", "keypress", "clickInput", "endValidate", "startValidate", "clickLeftIcon", "clickRightIcon", "update:modelValue"],
    setup(e, {emit: t, slots: n}) {
        const r = ti()
          , s = Ke({
            status: "unvalidated",
            focused: !1,
            validateMessage: ""
        })
          , o = se()
          , i = se()
          , l = se()
          , {parent: c} = mr(zh)
          , u = () => {
            var p;
            return String((p = e.modelValue) != null ? p : "")
        }
          , a = p => {
            if (Be(e[p]))
                return e[p];
            if (c && Be(c.props[p]))
                return c.props[p]
        }
          , f = G( () => {
            const p = a("readonly");
            if (e.clearable && !p) {
                const T = u() !== ""
                  , x = e.clearTrigger === "always" || e.clearTrigger === "focus" && s.focused;
                return T && x
            }
            return !1
        }
        )
          , d = G( () => l.value && n.input ? l.value() : e.modelValue)
          , g = G( () => {
            var p;
            const T = a("required");
            return T === "auto" ? (p = e.rules) == null ? void 0 : p.some(x => x.required) : T
        }
        )
          , y = p => p.reduce( (T, x) => T.then( () => {
            if (s.status === "failed")
                return;
            let {value: L} = d;
            if (x.formatter && (L = x.formatter(L, x)),
            !ag(L, x)) {
                s.status = "failed",
                s.validateMessage = fl(L, x);
                return
            }
            if (x.validator)
                return Ua(L) && x.validateEmpty === !1 ? void 0 : ug(L, x).then(J => {
                    J && typeof J == "string" ? (s.status = "failed",
                    s.validateMessage = J) : J === !1 && (s.status = "failed",
                    s.validateMessage = fl(L, x))
                }
                )
        }
        ), Promise.resolve())
          , b = () => {
            s.status = "unvalidated",
            s.validateMessage = ""
        }
          , w = () => t("endValidate", {
            status: s.status,
            message: s.validateMessage
        })
          , _ = (p=e.rules) => new Promise(T => {
            b(),
            p ? (t("startValidate"),
            y(p).then( () => {
                s.status === "failed" ? (T({
                    name: e.name,
                    message: s.validateMessage
                }),
                w()) : (s.status = "passed",
                T(),
                w())
            }
            )) : T()
        }
        )
          , S = p => {
            if (c && e.rules) {
                const {validateTrigger: T} = c.props
                  , x = fi(T).includes(p)
                  , L = e.rules.filter(J => J.trigger ? fi(J.trigger).includes(p) : x);
                L.length && _(L)
            }
        }
          , I = p => {
            var T;
            const {maxlength: x} = e;
            if (Be(x) && At(p) > +x) {
                const L = u();
                if (L && At(L) === +x)
                    return L;
                const J = (T = o.value) == null ? void 0 : T.selectionEnd;
                if (s.focused && J) {
                    const h = [...p]
                      , m = h.length - +x;
                    return h.splice(J - m, m),
                    h.join("")
                }
                return zs(p, +x)
            }
            return p
        }
          , O = (p, T="onChange") => {
            var x, L;
            const J = p;
            p = I(p);
            const h = At(J) - At(p);
            if (e.type === "number" || e.type === "digit") {
                const v = e.type === "number";
                p = Ih(p, v, v),
                T === "onBlur" && p !== "" && (e.min !== void 0 || e.max !== void 0) && (p = kr(+p, (x = e.min) != null ? x : -1 / 0, (L = e.max) != null ? L : 1 / 0).toString())
            }
            let m = 0;
            if (e.formatter && T === e.formatTrigger) {
                const {formatter: v, maxlength: C} = e;
                if (p = v(p),
                Be(C) && At(p) > +C && (p = zs(p, +C)),
                o.value && s.focused) {
                    const {selectionEnd: A} = o.value
                      , R = zs(J, A);
                    m = At(v(R)) - At(R)
                }
            }
            if (o.value && o.value.value !== p)
                if (s.focused) {
                    let {selectionStart: v, selectionEnd: C} = o.value;
                    if (o.value.value = p,
                    Be(v) && Be(C)) {
                        const A = At(p);
                        h ? (v -= h,
                        C -= h) : m && (v += m,
                        C += m),
                        o.value.setSelectionRange(Math.min(v, A), Math.min(C, A))
                    }
                } else
                    o.value.value = p;
            p !== e.modelValue && t("update:modelValue", p)
        }
          , k = p => {
            p.target.composing || O(p.target.value)
        }
          , F = () => {
            var p;
            return (p = o.value) == null ? void 0 : p.blur()
        }
          , V = () => {
            var p;
            return (p = o.value) == null ? void 0 : p.focus()
        }
          , U = () => {
            const p = o.value;
            e.type === "textarea" && e.autosize && p && dg(p, e.autosize)
        }
          , B = p => {
            s.focused = !0,
            t("focus", p),
            Pe(U),
            a("readonly") && F()
        }
          , z = p => {
            s.focused = !1,
            O(u(), "onBlur"),
            t("blur", p),
            !a("readonly") && (S("onBlur"),
            Pe(U),
            Eh())
        }
          , X = p => t("clickInput", p)
          , $ = p => t("clickLeftIcon", p)
          , ne = p => t("clickRightIcon", p)
          , me = p => {
            un(p),
            t("update:modelValue", ""),
            t("clear", p)
        }
          , Te = G( () => {
            if (typeof e.error == "boolean")
                return e.error;
            if (c && c.props.showError && s.status === "failed")
                return !0
        }
        )
          , ae = G( () => {
            const p = a("labelWidth")
              , T = a("labelAlign");
            if (p && T !== "top")
                return {
                    width: De(p)
                }
        }
        )
          , Q = p => {
            p.keyCode === 13 && (!(c && c.props.submitOnEnter) && e.type !== "textarea" && un(p),
            e.type === "search" && F()),
            t("keypress", p)
        }
          , le = () => e.id || `${r}-input`
          , je = () => s.status
          , Je = () => {
            const p = st("control", [a("inputAlign"), {
                error: Te.value,
                custom: !!n.input,
                "min-height": e.type === "textarea" && !e.autosize
            }]);
            if (n.input)
                return P("div", {
                    class: p,
                    onClick: X
                }, [n.input()]);
            const T = {
                id: le(),
                ref: o,
                name: e.name,
                rows: e.rows !== void 0 ? +e.rows : void 0,
                class: p,
                disabled: a("disabled"),
                readonly: a("readonly"),
                autofocus: e.autofocus,
                placeholder: e.placeholder,
                autocomplete: e.autocomplete,
                autocapitalize: e.autocapitalize,
                autocorrect: e.autocorrect,
                enterkeyhint: e.enterkeyhint,
                spellcheck: e.spellcheck,
                "aria-labelledby": e.label ? `${r}-label` : void 0,
                "data-allow-mismatch": "attribute",
                onBlur: z,
                onFocus: B,
                onInput: k,
                onClick: X,
                onChange: dl,
                onKeypress: Q,
                onCompositionend: dl,
                onCompositionstart: fg
            };
            return e.type === "textarea" ? P("textarea", xt(T, {
                inputmode: e.inputmode
            }), null) : P("input", xt(hg(e.type, e.inputmode), T), null)
        }
          , K = () => {
            const p = n["left-icon"];
            if (e.leftIcon || p)
                return P("div", {
                    class: st("left-icon"),
                    onClick: $
                }, [p ? p() : P(Jt, {
                    name: e.leftIcon,
                    classPrefix: e.iconPrefix
                }, null)])
        }
          , ie = () => {
            const p = n["right-icon"];
            if (e.rightIcon || p)
                return P("div", {
                    class: st("right-icon"),
                    onClick: ne
                }, [p ? p() : P(Jt, {
                    name: e.rightIcon,
                    classPrefix: e.iconPrefix
                }, null)])
        }
          , fe = () => {
            if (e.showWordLimit && e.maxlength) {
                const p = At(u());
                return P("div", {
                    class: st("word-limit")
                }, [P("span", {
                    class: st("word-num")
                }, [p]), ha("/"), e.maxlength])
            }
        }
          , xe = () => {
            if (c && c.props.showErrorMessage === !1)
                return;
            const p = e.errorMessage || s.validateMessage;
            if (p) {
                const T = n["error-message"]
                  , x = a("errorMessageAlign");
                return P("div", {
                    class: st("error-message", x)
                }, [T ? T({
                    message: p
                }) : p])
            }
        }
          , H = () => {
            const p = a("labelWidth")
              , T = a("labelAlign")
              , x = a("colon") ? ":" : "";
            if (n.label)
                return [n.label(), x];
            if (e.label)
                return P("label", {
                    id: `${r}-label`,
                    for: n.input ? void 0 : le(),
                    "data-allow-mismatch": "attribute",
                    onClick: L => {
                        un(L),
                        V()
                    }
                    ,
                    style: T === "top" && p ? {
                        width: De(p)
                    } : void 0
                }, [e.label + x])
        }
          , W = () => [P("div", {
            class: st("body")
        }, [Je(), f.value && P(Jt, {
            ref: i,
            name: e.clearIcon,
            class: st("clear")
        }, null), ie(), n.button && P("div", {
            class: st("button")
        }, [n.button()])]), fe(), xe()];
        return $t({
            blur: F,
            focus: V,
            validate: _,
            formValue: d,
            resetValidation: b,
            getValidationStatus: je
        }),
        Wt(Sh, {
            customValue: l,
            resetValidation: b,
            validateWithTrigger: S
        }),
        we( () => e.modelValue, () => {
            O(u()),
            b(),
            S("onChange"),
            Pe(U)
        }
        ),
        Ct( () => {
            O(u(), e.formatTrigger),
            Pe(U)
        }
        ),
        An("touchstart", me, {
            target: G( () => {
                var p;
                return (p = i.value) == null ? void 0 : p.$el
            }
            )
        }),
        () => {
            const p = a("disabled")
              , T = a("labelAlign")
              , x = K()
              , L = () => {
                const J = H();
                return T === "top" ? [x, J].filter(Boolean) : J || []
            }
            ;
            return P(cg, {
                size: e.size,
                class: st({
                    error: Te.value,
                    disabled: p,
                    [`label-${T}`]: T
                }),
                center: e.center,
                border: e.border,
                isLink: e.isLink,
                clickable: e.clickable,
                titleStyle: ae.value,
                valueClass: st("value"),
                titleClass: [st("label", [T, {
                    required: g.value
                }]), e.labelClass],
                arrowDirection: e.arrowDirection
            }, {
                icon: x && T !== "top" ? () => x : null,
                title: L,
                value: W,
                extra: n.extra
            })
        }
    }
});
const bg = ke(yg);
function vg() {
    const e = Ke({
        show: !1
    })
      , t = s => {
        e.show = s
    }
      , n = s => {
        We(e, s, {
            transitionAppear: !0
        }),
        t(!0)
    }
      , r = () => t(!1);
    return $t({
        open: n,
        close: r,
        toggle: t
    }),
    {
        open: n,
        close: r,
        state: e,
        toggle: t
    }
}
function wg(e) {
    const t = wa(e)
      , n = document.createElement("div");
    return document.body.appendChild(n),
    {
        instance: t.mount(n),
        unmount() {
            t.unmount(),
            document.body.removeChild(n)
        }
    }
}
const [Va,Sg] = Oe("row")
  , za = Symbol(Va)
  , _g = {
    tag: _e("div"),
    wrap: Re,
    align: String,
    gutter: {
        type: [String, Number, Array],
        default: 0
    },
    justify: String
};
var Eg = Ce({
    name: Va,
    props: _g,
    setup(e, {slots: t}) {
        const {children: n, linkChildren: r} = fs(za)
          , s = G( () => {
            const l = [[]];
            let c = 0;
            return n.forEach( (u, a) => {
                c += Number(u.span),
                c > 24 ? (l.push([a]),
                c -= 24) : l[l.length - 1].push(a)
            }
            ),
            l
        }
        )
          , o = G( () => {
            let l = 0;
            Array.isArray(e.gutter) ? l = Number(e.gutter[0]) || 0 : l = Number(e.gutter);
            const c = [];
            return l && s.value.forEach(u => {
                const a = l * (u.length - 1) / u.length;
                u.forEach( (f, d) => {
                    if (d === 0)
                        c.push({
                            right: a
                        });
                    else {
                        const g = l - c[f - 1].right
                          , y = a - g;
                        c.push({
                            left: g,
                            right: y
                        })
                    }
                }
                )
            }
            ),
            c
        }
        )
          , i = G( () => {
            const {gutter: l} = e
              , c = [];
            if (Array.isArray(l) && l.length > 1) {
                const u = Number(l[1]) || 0;
                if (u <= 0)
                    return c;
                s.value.forEach( (a, f) => {
                    f !== s.value.length - 1 && a.forEach( () => {
                        c.push({
                            bottom: u
                        })
                    }
                    )
                }
                )
            }
            return c
        }
        );
        return r({
            spaces: o,
            verticalSpaces: i
        }),
        () => {
            const {tag: l, wrap: c, align: u, justify: a} = e;
            return P(l, {
                class: Sg({
                    [`align-${u}`]: u,
                    [`justify-${a}`]: a,
                    nowrap: !c
                })
            }, {
                default: () => {
                    var f;
                    return [(f = t.default) == null ? void 0 : f.call(t)]
                }
            })
        }
    }
});
const [xg,Cg] = Oe("col")
  , Tg = {
    tag: _e("div"),
    span: ut(0),
    offset: pe
};
var Rg = Ce({
    name: xg,
    props: Tg,
    setup(e, {slots: t}) {
        const {parent: n, index: r} = mr(za)
          , s = G( () => {
            if (!n)
                return;
            const {spaces: o, verticalSpaces: i} = n;
            let l = {};
            if (o && o.value && o.value[r.value]) {
                const {left: u, right: a} = o.value[r.value];
                l = {
                    paddingLeft: u ? `${u}px` : null,
                    paddingRight: a ? `${a}px` : null
                }
            }
            const {bottom: c} = i.value[r.value] || {};
            return We(l, {
                marginBottom: c ? `${c}px` : null
            })
        }
        );
        return () => {
            const {tag: o, span: i, offset: l} = e;
            return P(o, {
                style: s.value,
                class: Cg({
                    [i]: i,
                    [`offset-${l}`]: l
                })
            }, {
                default: () => {
                    var c;
                    return [(c = t.default) == null ? void 0 : c.call(t)]
                }
            })
        }
    }
});
const Ag = ke(Rg)
  , [Og,ct,Or] = Oe("dialog")
  , Pg = We({}, ms, {
    title: String,
    theme: String,
    width: pe,
    message: [String, Function],
    callback: Function,
    allowHtml: Boolean,
    className: kt,
    transition: _e("van-dialog-bounce"),
    messageAlign: String,
    closeOnPopstate: Re,
    showCancelButton: Boolean,
    cancelButtonText: String,
    cancelButtonColor: String,
    cancelButtonDisabled: Boolean,
    confirmButtonText: String,
    confirmButtonColor: String,
    confirmButtonDisabled: Boolean,
    showConfirmButton: Re,
    closeOnClickOverlay: Boolean,
    keyboardEnabled: Re
})
  , Ig = [...wp, "transition", "closeOnPopstate"];
var Bg = Ce({
    name: Og,
    props: Pg,
    emits: ["confirm", "cancel", "keydown", "update:show"],
    setup(e, {emit: t, slots: n}) {
        const r = se()
          , s = Ke({
            confirm: !1,
            cancel: !1
        })
          , o = _ => t("update:show", _)
          , i = _ => {
            var S;
            o(!1),
            (S = e.callback) == null || S.call(e, _)
        }
          , l = _ => () => {
            e.show && (t(_),
            e.beforeClose ? (s[_] = !0,
            Xo(e.beforeClose, {
                args: [_],
                done() {
                    i(_),
                    s[_] = !1
                },
                canceled() {
                    s[_] = !1
                }
            })) : i(_))
        }
          , c = l("cancel")
          , u = l("confirm")
          , a = ah(_ => {
            var S, I;
            if (!e.keyboardEnabled || _.target !== ((I = (S = r.value) == null ? void 0 : S.popupRef) == null ? void 0 : I.value))
                return;
            ({
                Enter: e.showConfirmButton ? u : eo,
                Escape: e.showCancelButton ? c : eo
            })[_.key](),
            t("keydown", _)
        }
        , ["enter", "esc"])
          , f = () => {
            const _ = n.title ? n.title() : e.title;
            if (_)
                return P("div", {
                    class: ct("header", {
                        isolated: !e.message && !n.default
                    })
                }, [_])
        }
          , d = _ => {
            const {message: S, allowHtml: I, messageAlign: O} = e
              , k = ct("message", {
                "has-title": _,
                [O]: O
            })
              , F = Xn(S) ? S() : S;
            return I && typeof F == "string" ? P("div", {
                class: k,
                innerHTML: F
            }, null) : P("div", {
                class: k
            }, [F])
        }
          , g = () => {
            if (n.default)
                return P("div", {
                    class: ct("content")
                }, [n.default()]);
            const {title: _, message: S, allowHtml: I} = e;
            if (S) {
                const O = !!(_ || n.title);
                return P("div", {
                    key: I ? 1 : 0,
                    class: ct("content", {
                        isolated: !O
                    })
                }, [d(O)])
            }
        }
          , y = () => P("div", {
            class: [Mh, ct("footer")]
        }, [e.showCancelButton && P(Kr, {
            size: "large",
            text: e.cancelButtonText || Or("cancel"),
            class: ct("cancel"),
            style: {
                color: e.cancelButtonColor
            },
            loading: s.cancel,
            disabled: e.cancelButtonDisabled,
            onClick: c
        }, null), e.showConfirmButton && P(Kr, {
            size: "large",
            text: e.confirmButtonText || Or("confirm"),
            class: [ct("confirm"), {
                [jh]: e.showCancelButton
            }],
            style: {
                color: e.confirmButtonColor
            },
            loading: s.confirm,
            disabled: e.confirmButtonDisabled,
            onClick: u
        }, null)])
          , b = () => P(Yh, {
            class: ct("footer")
        }, {
            default: () => [e.showCancelButton && P(ol, {
                type: "warning",
                text: e.cancelButtonText || Or("cancel"),
                class: ct("cancel"),
                color: e.cancelButtonColor,
                loading: s.cancel,
                disabled: e.cancelButtonDisabled,
                onClick: c
            }, null), e.showConfirmButton && P(ol, {
                type: "danger",
                text: e.confirmButtonText || Or("confirm"),
                class: ct("confirm"),
                color: e.confirmButtonColor,
                loading: s.confirm,
                disabled: e.confirmButtonDisabled,
                onClick: u
            }, null)]
        })
          , w = () => n.footer ? n.footer() : e.theme === "round-button" ? b() : y();
        return () => {
            const {width: _, title: S, theme: I, message: O, className: k} = e;
            return P(ei, xt({
                ref: r,
                role: "dialog",
                class: [ct([I]), k],
                style: {
                    width: De(_)
                },
                tabindex: 0,
                "aria-labelledby": S || O,
                onKeydown: a,
                "onUpdate:show": o
            }, Fr(e, Ig)), {
                default: () => [f(), g(), w()]
            })
        }
    }
});
const kg = ke(Bg)
  , [Ng,Lg] = Oe("notify")
  , $g = ["lockScroll", "position", "show", "teleport", "zIndex"]
  , Fg = We({}, ms, {
    type: _e("danger"),
    color: String,
    message: pe,
    position: _e("top"),
    className: kt,
    background: String,
    lockScroll: Boolean
});
var qa = Ce({
    name: Ng,
    props: Fg,
    emits: ["update:show"],
    setup(e, {emit: t, slots: n}) {
        const r = s => t("update:show", s);
        return () => P(ei, xt({
            class: [Lg([e.type]), e.className],
            style: {
                color: e.color,
                background: e.background
            },
            overlay: !1,
            duration: .2,
            "onUpdate:show": r
        }, Fr(e, $g)), {
            default: () => [n.default ? n.default() : e.message]
        })
    }
});
let hl, En;
const Dg = e => fr(e) ? e : {
    message: e
};
function Mg() {
    ({instance: En} = wg({
        setup() {
            const {state: e, toggle: t} = vg();
            return () => P(qa, xt(e, {
                "onUpdate:show": t
            }), null)
        }
    }))
}
const jg = () => ({
    type: "danger",
    color: void 0,
    message: "",
    onClose: void 0,
    onClick: void 0,
    onOpened: void 0,
    duration: 3e3,
    position: void 0,
    className: "",
    lockScroll: !1,
    background: void 0
});
let Hg = jg();
const Ug = () => {
    En && En.toggle(!1)
}
;
function pl(e) {
    if (Yr)
        return En || Mg(),
        e = We({}, Hg, Dg(e)),
        En.open(e),
        clearTimeout(hl),
        e.duration > 0 && (hl = setTimeout(Ug, e.duration)),
        En
}
ke(qa);
const Vg = ke(Eg)
  , zg = (e, t) => {
    const n = e.__vccOpts || e;
    for (const [r,s] of t)
        n[r] = s;
    return n
}
  , qg = {};
function Kg(e, t) {
    const n = Kf("router-view");
    return ca(),
    ua(n)
}
const Wg = zg(qg, [["render", Kg]])
  , Jg = "modulepreload"
  , Yg = function(e) {
    return "/apps/klotski/" + e
}
  , gl = {}
  , qs = function(t, n, r) {
    let s = Promise.resolve();
    if (n && n.length > 0) {
        document.getElementsByTagName("link");
        const i = document.querySelector("meta[property=csp-nonce]")
          , l = (i == null ? void 0 : i.nonce) || (i == null ? void 0 : i.getAttribute("nonce"));
        s = Promise.allSettled(n.map(c => {
            if (c = Yg(c),
            c in gl)
                return;
            gl[c] = !0;
            const u = c.endsWith(".css")
              , a = u ? '[rel="stylesheet"]' : "";
            if (document.querySelector(`link[href="${c}"]${a}`))
                return;
            const f = document.createElement("link");
            if (f.rel = u ? "stylesheet" : Jg,
            u || (f.as = "script"),
            f.crossOrigin = "",
            f.href = c,
            l && f.setAttribute("nonce", l),
            document.head.appendChild(f),
            u)
                return new Promise( (d, g) => {
                    f.addEventListener("load", d),
                    f.addEventListener("error", () => g(new Error(`Unable to preload CSS for ${c}`)))
                }
                )
        }
        ))
    }
    function o(i) {
        const l = new Event("vite:preloadError",{
            cancelable: !0
        });
        if (l.payload = i,
        window.dispatchEvent(l),
        !l.defaultPrevented)
            throw i
    }
    return s.then(i => {
        for (const l of i || [])
            l.status === "rejected" && o(l.reason);
        return t().catch(o)
    }
    )
};
/*!
  * vue-router v4.5.0
  * (c) 2024 Eduardo San Martin Morote
  * @license MIT
  */
const vn = typeof document < "u";
function Ka(e) {
    return typeof e == "object" || "displayName"in e || "props"in e || "__vccOpts"in e
}
function Gg(e) {
    return e.__esModule || e[Symbol.toStringTag] === "Module" || e.default && Ka(e.default)
}
const de = Object.assign;
function Ks(e, t) {
    const n = {};
    for (const r in t) {
        const s = t[r];
        n[r] = ht(s) ? s.map(e) : e(s)
    }
    return n
}
const Jn = () => {}
  , ht = Array.isArray
  , Wa = /#/g
  , Xg = /&/g
  , Zg = /\//g
  , Qg = /=/g
  , em = /\?/g
  , Ja = /\+/g
  , tm = /%5B/g
  , nm = /%5D/g
  , Ya = /%5E/g
  , rm = /%60/g
  , Ga = /%7B/g
  , sm = /%7C/g
  , Xa = /%7D/g
  , om = /%20/g;
function ni(e) {
    return encodeURI("" + e).replace(sm, "|").replace(tm, "[").replace(nm, "]")
}
function im(e) {
    return ni(e).replace(Ga, "{").replace(Xa, "}").replace(Ya, "^")
}
function mo(e) {
    return ni(e).replace(Ja, "%2B").replace(om, "+").replace(Wa, "%23").replace(Xg, "%26").replace(rm, "`").replace(Ga, "{").replace(Xa, "}").replace(Ya, "^")
}
function lm(e) {
    return mo(e).replace(Qg, "%3D")
}
function cm(e) {
    return ni(e).replace(Wa, "%23").replace(em, "%3F")
}
function am(e) {
    return e == null ? "" : cm(e).replace(Zg, "%2F")
}
function cr(e) {
    try {
        return decodeURIComponent("" + e)
    } catch {}
    return "" + e
}
const um = /\/$/
  , fm = e => e.replace(um, "");
function Ws(e, t, n="/") {
    let r, s = {}, o = "", i = "";
    const l = t.indexOf("#");
    let c = t.indexOf("?");
    return l < c && l >= 0 && (c = -1),
    c > -1 && (r = t.slice(0, c),
    o = t.slice(c + 1, l > -1 ? l : t.length),
    s = e(o)),
    l > -1 && (r = r || t.slice(0, l),
    i = t.slice(l, t.length)),
    r = gm(r ?? t, n),
    {
        fullPath: r + (o && "?") + o + i,
        path: r,
        query: s,
        hash: cr(i)
    }
}
function dm(e, t) {
    const n = t.query ? e(t.query) : "";
    return t.path + (n && "?") + n + (t.hash || "")
}
function ml(e, t) {
    return !t || !e.toLowerCase().startsWith(t.toLowerCase()) ? e : e.slice(t.length) || "/"
}
function hm(e, t, n) {
    const r = t.matched.length - 1
      , s = n.matched.length - 1;
    return r > -1 && r === s && xn(t.matched[r], n.matched[s]) && Za(t.params, n.params) && e(t.query) === e(n.query) && t.hash === n.hash
}
function xn(e, t) {
    return (e.aliasOf || e) === (t.aliasOf || t)
}
function Za(e, t) {
    if (Object.keys(e).length !== Object.keys(t).length)
        return !1;
    for (const n in e)
        if (!pm(e[n], t[n]))
            return !1;
    return !0
}
function pm(e, t) {
    return ht(e) ? yl(e, t) : ht(t) ? yl(t, e) : e === t
}
function yl(e, t) {
    return ht(t) ? e.length === t.length && e.every( (n, r) => n === t[r]) : e.length === 1 && e[0] === t
}
function gm(e, t) {
    if (e.startsWith("/"))
        return e;
    if (!e)
        return t;
    const n = t.split("/")
      , r = e.split("/")
      , s = r[r.length - 1];
    (s === ".." || s === ".") && r.push("");
    let o = n.length - 1, i, l;
    for (i = 0; i < r.length; i++)
        if (l = r[i],
        l !== ".")
            if (l === "..")
                o > 1 && o--;
            else
                break;
    return n.slice(0, o).join("/") + "/" + r.slice(i).join("/")
}
const Dt = {
    path: "/",
    name: void 0,
    params: {},
    query: {},
    hash: "",
    fullPath: "/",
    matched: [],
    meta: {},
    redirectedFrom: void 0
};
var ar;
(function(e) {
    e.pop = "pop",
    e.push = "push"
}
)(ar || (ar = {}));
var Yn;
(function(e) {
    e.back = "back",
    e.forward = "forward",
    e.unknown = ""
}
)(Yn || (Yn = {}));
function mm(e) {
    if (!e)
        if (vn) {
            const t = document.querySelector("base");
            e = t && t.getAttribute("href") || "/",
            e = e.replace(/^\w+:\/\/[^\/]+/, "")
        } else
            e = "/";
    return e[0] !== "/" && e[0] !== "#" && (e = "/" + e),
    fm(e)
}
const ym = /^[^#]+#/;
function bm(e, t) {
    return e.replace(ym, "#") + t
}
function vm(e, t) {
    const n = document.documentElement.getBoundingClientRect()
      , r = e.getBoundingClientRect();
    return {
        behavior: t.behavior,
        left: r.left - n.left - (t.left || 0),
        top: r.top - n.top - (t.top || 0)
    }
}
const ys = () => ({
    left: window.scrollX,
    top: window.scrollY
});
function wm(e) {
    let t;
    if ("el"in e) {
        const n = e.el
          , r = typeof n == "string" && n.startsWith("#")
          , s = typeof n == "string" ? r ? document.getElementById(n.slice(1)) : document.querySelector(n) : n;
        if (!s)
            return;
        t = vm(s, e)
    } else
        t = e;
    "scrollBehavior"in document.documentElement.style ? window.scrollTo(t) : window.scrollTo(t.left != null ? t.left : window.scrollX, t.top != null ? t.top : window.scrollY)
}
function bl(e, t) {
    return (history.state ? history.state.position - t : -1) + e
}
const yo = new Map;
function Sm(e, t) {
    yo.set(e, t)
}
function _m(e) {
    const t = yo.get(e);
    return yo.delete(e),
    t
}
let Em = () => location.protocol + "//" + location.host;
function Qa(e, t) {
    const {pathname: n, search: r, hash: s} = t
      , o = e.indexOf("#");
    if (o > -1) {
        let l = s.includes(e.slice(o)) ? e.slice(o).length : 1
          , c = s.slice(l);
        return c[0] !== "/" && (c = "/" + c),
        ml(c, "")
    }
    return ml(n, e) + r + s
}
function xm(e, t, n, r) {
    let s = []
      , o = []
      , i = null;
    const l = ({state: d}) => {
        const g = Qa(e, location)
          , y = n.value
          , b = t.value;
        let w = 0;
        if (d) {
            if (n.value = g,
            t.value = d,
            i && i === y) {
                i = null;
                return
            }
            w = b ? d.position - b.position : 0
        } else
            r(g);
        s.forEach(_ => {
            _(n.value, y, {
                delta: w,
                type: ar.pop,
                direction: w ? w > 0 ? Yn.forward : Yn.back : Yn.unknown
            })
        }
        )
    }
    ;
    function c() {
        i = n.value
    }
    function u(d) {
        s.push(d);
        const g = () => {
            const y = s.indexOf(d);
            y > -1 && s.splice(y, 1)
        }
        ;
        return o.push(g),
        g
    }
    function a() {
        const {history: d} = window;
        d.state && d.replaceState(de({}, d.state, {
            scroll: ys()
        }), "")
    }
    function f() {
        for (const d of o)
            d();
        o = [],
        window.removeEventListener("popstate", l),
        window.removeEventListener("beforeunload", a)
    }
    return window.addEventListener("popstate", l),
    window.addEventListener("beforeunload", a, {
        passive: !0
    }),
    {
        pauseListeners: c,
        listen: u,
        destroy: f
    }
}
function vl(e, t, n, r=!1, s=!1) {
    return {
        back: e,
        current: t,
        forward: n,
        replaced: r,
        position: window.history.length,
        scroll: s ? ys() : null
    }
}
function Cm(e) {
    const {history: t, location: n} = window
      , r = {
        value: Qa(e, n)
    }
      , s = {
        value: t.state
    };
    s.value || o(r.value, {
        back: null,
        current: r.value,
        forward: null,
        position: t.length - 1,
        replaced: !0,
        scroll: null
    }, !0);
    function o(c, u, a) {
        const f = e.indexOf("#")
          , d = f > -1 ? (n.host && document.querySelector("base") ? e : e.slice(f)) + c : Em() + e + c;
        try {
            t[a ? "replaceState" : "pushState"](u, "", d),
            s.value = u
        } catch (g) {
            console.error(g),
            n[a ? "replace" : "assign"](d)
        }
    }
    function i(c, u) {
        const a = de({}, t.state, vl(s.value.back, c, s.value.forward, !0), u, {
            position: s.value.position
        });
        o(c, a, !0),
        r.value = c
    }
    function l(c, u) {
        const a = de({}, s.value, t.state, {
            forward: c,
            scroll: ys()
        });
        o(a.current, a, !0);
        const f = de({}, vl(r.value, c, null), {
            position: a.position + 1
        }, u);
        o(c, f, !1),
        r.value = c
    }
    return {
        location: r,
        state: s,
        push: l,
        replace: i
    }
}
function Tm(e) {
    e = mm(e);
    const t = Cm(e)
      , n = xm(e, t.state, t.location, t.replace);
    function r(o, i=!0) {
        i || n.pauseListeners(),
        history.go(o)
    }
    const s = de({
        location: "",
        base: e,
        go: r,
        createHref: bm.bind(null, e)
    }, t, n);
    return Object.defineProperty(s, "location", {
        enumerable: !0,
        get: () => t.location.value
    }),
    Object.defineProperty(s, "state", {
        enumerable: !0,
        get: () => t.state.value
    }),
    s
}
function Rm(e) {
    return typeof e == "string" || e && typeof e == "object"
}
function eu(e) {
    return typeof e == "string" || typeof e == "symbol"
}
const tu = Symbol("");
var wl;
(function(e) {
    e[e.aborted = 4] = "aborted",
    e[e.cancelled = 8] = "cancelled",
    e[e.duplicated = 16] = "duplicated"
}
)(wl || (wl = {}));
function Cn(e, t) {
    return de(new Error, {
        type: e,
        [tu]: !0
    }, t)
}
function Ot(e, t) {
    return e instanceof Error && tu in e && (t == null || !!(e.type & t))
}
const Sl = "[^/]+?"
  , Am = {
    sensitive: !1,
    strict: !1,
    start: !0,
    end: !0
}
  , Om = /[.+*?^${}()[\]/\\]/g;
function Pm(e, t) {
    const n = de({}, Am, t)
      , r = [];
    let s = n.start ? "^" : "";
    const o = [];
    for (const u of e) {
        const a = u.length ? [] : [90];
        n.strict && !u.length && (s += "/");
        for (let f = 0; f < u.length; f++) {
            const d = u[f];
            let g = 40 + (n.sensitive ? .25 : 0);
            if (d.type === 0)
                f || (s += "/"),
                s += d.value.replace(Om, "\\$&"),
                g += 40;
            else if (d.type === 1) {
                const {value: y, repeatable: b, optional: w, regexp: _} = d;
                o.push({
                    name: y,
                    repeatable: b,
                    optional: w
                });
                const S = _ || Sl;
                if (S !== Sl) {
                    g += 10;
                    try {
                        new RegExp(`(${S})`)
                    } catch (O) {
                        throw new Error(`Invalid custom RegExp for param "${y}" (${S}): ` + O.message)
                    }
                }
                let I = b ? `((?:${S})(?:/(?:${S}))*)` : `(${S})`;
                f || (I = w && u.length < 2 ? `(?:/${I})` : "/" + I),
                w && (I += "?"),
                s += I,
                g += 20,
                w && (g += -8),
                b && (g += -20),
                S === ".*" && (g += -50)
            }
            a.push(g)
        }
        r.push(a)
    }
    if (n.strict && n.end) {
        const u = r.length - 1;
        r[u][r[u].length - 1] += .7000000000000001
    }
    n.strict || (s += "/?"),
    n.end ? s += "$" : n.strict && !s.endsWith("/") && (s += "(?:/|$)");
    const i = new RegExp(s,n.sensitive ? "" : "i");
    function l(u) {
        const a = u.match(i)
          , f = {};
        if (!a)
            return null;
        for (let d = 1; d < a.length; d++) {
            const g = a[d] || ""
              , y = o[d - 1];
            f[y.name] = g && y.repeatable ? g.split("/") : g
        }
        return f
    }
    function c(u) {
        let a = ""
          , f = !1;
        for (const d of e) {
            (!f || !a.endsWith("/")) && (a += "/"),
            f = !1;
            for (const g of d)
                if (g.type === 0)
                    a += g.value;
                else if (g.type === 1) {
                    const {value: y, repeatable: b, optional: w} = g
                      , _ = y in u ? u[y] : "";
                    if (ht(_) && !b)
                        throw new Error(`Provided param "${y}" is an array but it is not repeatable (* or + modifiers)`);
                    const S = ht(_) ? _.join("/") : _;
                    if (!S)
                        if (w)
                            d.length < 2 && (a.endsWith("/") ? a = a.slice(0, -1) : f = !0);
                        else
                            throw new Error(`Missing required param "${y}"`);
                    a += S
                }
        }
        return a || "/"
    }
    return {
        re: i,
        score: r,
        keys: o,
        parse: l,
        stringify: c
    }
}
function Im(e, t) {
    let n = 0;
    for (; n < e.length && n < t.length; ) {
        const r = t[n] - e[n];
        if (r)
            return r;
        n++
    }
    return e.length < t.length ? e.length === 1 && e[0] === 80 ? -1 : 1 : e.length > t.length ? t.length === 1 && t[0] === 80 ? 1 : -1 : 0
}
function nu(e, t) {
    let n = 0;
    const r = e.score
      , s = t.score;
    for (; n < r.length && n < s.length; ) {
        const o = Im(r[n], s[n]);
        if (o)
            return o;
        n++
    }
    if (Math.abs(s.length - r.length) === 1) {
        if (_l(r))
            return 1;
        if (_l(s))
            return -1
    }
    return s.length - r.length
}
function _l(e) {
    const t = e[e.length - 1];
    return e.length > 0 && t[t.length - 1] < 0
}
const Bm = {
    type: 0,
    value: ""
}
  , km = /[a-zA-Z0-9_]/;
function Nm(e) {
    if (!e)
        return [[]];
    if (e === "/")
        return [[Bm]];
    if (!e.startsWith("/"))
        throw new Error(`Invalid path "${e}"`);
    function t(g) {
        throw new Error(`ERR (${n})/"${u}": ${g}`)
    }
    let n = 0
      , r = n;
    const s = [];
    let o;
    function i() {
        o && s.push(o),
        o = []
    }
    let l = 0, c, u = "", a = "";
    function f() {
        u && (n === 0 ? o.push({
            type: 0,
            value: u
        }) : n === 1 || n === 2 || n === 3 ? (o.length > 1 && (c === "*" || c === "+") && t(`A repeatable param (${u}) must be alone in its segment. eg: '/:ids+.`),
        o.push({
            type: 1,
            value: u,
            regexp: a,
            repeatable: c === "*" || c === "+",
            optional: c === "*" || c === "?"
        })) : t("Invalid state to consume buffer"),
        u = "")
    }
    function d() {
        u += c
    }
    for (; l < e.length; ) {
        if (c = e[l++],
        c === "\\" && n !== 2) {
            r = n,
            n = 4;
            continue
        }
        switch (n) {
        case 0:
            c === "/" ? (u && f(),
            i()) : c === ":" ? (f(),
            n = 1) : d();
            break;
        case 4:
            d(),
            n = r;
            break;
        case 1:
            c === "(" ? n = 2 : km.test(c) ? d() : (f(),
            n = 0,
            c !== "*" && c !== "?" && c !== "+" && l--);
            break;
        case 2:
            c === ")" ? a[a.length - 1] == "\\" ? a = a.slice(0, -1) + c : n = 3 : a += c;
            break;
        case 3:
            f(),
            n = 0,
            c !== "*" && c !== "?" && c !== "+" && l--,
            a = "";
            break;
        default:
            t("Unknown state");
            break
        }
    }
    return n === 2 && t(`Unfinished custom RegExp for param "${u}"`),
    f(),
    i(),
    s
}
function Lm(e, t, n) {
    const r = Pm(Nm(e.path), n)
      , s = de(r, {
        record: e,
        parent: t,
        children: [],
        alias: []
    });
    return t && !s.record.aliasOf == !t.record.aliasOf && t.children.push(s),
    s
}
function $m(e, t) {
    const n = []
      , r = new Map;
    t = Tl({
        strict: !1,
        end: !0,
        sensitive: !1
    }, t);
    function s(f) {
        return r.get(f)
    }
    function o(f, d, g) {
        const y = !g
          , b = xl(f);
        b.aliasOf = g && g.record;
        const w = Tl(t, f)
          , _ = [b];
        if ("alias"in f) {
            const O = typeof f.alias == "string" ? [f.alias] : f.alias;
            for (const k of O)
                _.push(xl(de({}, b, {
                    components: g ? g.record.components : b.components,
                    path: k,
                    aliasOf: g ? g.record : b
                })))
        }
        let S, I;
        for (const O of _) {
            const {path: k} = O;
            if (d && k[0] !== "/") {
                const F = d.record.path
                  , V = F[F.length - 1] === "/" ? "" : "/";
                O.path = d.record.path + (k && V + k)
            }
            if (S = Lm(O, d, w),
            g ? g.alias.push(S) : (I = I || S,
            I !== S && I.alias.push(S),
            y && f.name && !Cl(S) && i(f.name)),
            ru(S) && c(S),
            b.children) {
                const F = b.children;
                for (let V = 0; V < F.length; V++)
                    o(F[V], S, g && g.children[V])
            }
            g = g || S
        }
        return I ? () => {
            i(I)
        }
        : Jn
    }
    function i(f) {
        if (eu(f)) {
            const d = r.get(f);
            d && (r.delete(f),
            n.splice(n.indexOf(d), 1),
            d.children.forEach(i),
            d.alias.forEach(i))
        } else {
            const d = n.indexOf(f);
            d > -1 && (n.splice(d, 1),
            f.record.name && r.delete(f.record.name),
            f.children.forEach(i),
            f.alias.forEach(i))
        }
    }
    function l() {
        return n
    }
    function c(f) {
        const d = Mm(f, n);
        n.splice(d, 0, f),
        f.record.name && !Cl(f) && r.set(f.record.name, f)
    }
    function u(f, d) {
        let g, y = {}, b, w;
        if ("name"in f && f.name) {
            if (g = r.get(f.name),
            !g)
                throw Cn(1, {
                    location: f
                });
            w = g.record.name,
            y = de(El(d.params, g.keys.filter(I => !I.optional).concat(g.parent ? g.parent.keys.filter(I => I.optional) : []).map(I => I.name)), f.params && El(f.params, g.keys.map(I => I.name))),
            b = g.stringify(y)
        } else if (f.path != null)
            b = f.path,
            g = n.find(I => I.re.test(b)),
            g && (y = g.parse(b),
            w = g.record.name);
        else {
            if (g = d.name ? r.get(d.name) : n.find(I => I.re.test(d.path)),
            !g)
                throw Cn(1, {
                    location: f,
                    currentLocation: d
                });
            w = g.record.name,
            y = de({}, d.params, f.params),
            b = g.stringify(y)
        }
        const _ = [];
        let S = g;
        for (; S; )
            _.unshift(S.record),
            S = S.parent;
        return {
            name: w,
            path: b,
            params: y,
            matched: _,
            meta: Dm(_)
        }
    }
    e.forEach(f => o(f));
    function a() {
        n.length = 0,
        r.clear()
    }
    return {
        addRoute: o,
        resolve: u,
        removeRoute: i,
        clearRoutes: a,
        getRoutes: l,
        getRecordMatcher: s
    }
}
function El(e, t) {
    const n = {};
    for (const r of t)
        r in e && (n[r] = e[r]);
    return n
}
function xl(e) {
    const t = {
        path: e.path,
        redirect: e.redirect,
        name: e.name,
        meta: e.meta || {},
        aliasOf: e.aliasOf,
        beforeEnter: e.beforeEnter,
        props: Fm(e),
        children: e.children || [],
        instances: {},
        leaveGuards: new Set,
        updateGuards: new Set,
        enterCallbacks: {},
        components: "components"in e ? e.components || null : e.component && {
            default: e.component
        }
    };
    return Object.defineProperty(t, "mods", {
        value: {}
    }),
    t
}
function Fm(e) {
    const t = {}
      , n = e.props || !1;
    if ("component"in e)
        t.default = n;
    else
        for (const r in e.components)
            t[r] = typeof n == "object" ? n[r] : n;
    return t
}
function Cl(e) {
    for (; e; ) {
        if (e.record.aliasOf)
            return !0;
        e = e.parent
    }
    return !1
}
function Dm(e) {
    return e.reduce( (t, n) => de(t, n.meta), {})
}
function Tl(e, t) {
    const n = {};
    for (const r in e)
        n[r] = r in t ? t[r] : e[r];
    return n
}
function Mm(e, t) {
    let n = 0
      , r = t.length;
    for (; n !== r; ) {
        const o = n + r >> 1;
        nu(e, t[o]) < 0 ? r = o : n = o + 1
    }
    const s = jm(e);
    return s && (r = t.lastIndexOf(s, r - 1)),
    r
}
function jm(e) {
    let t = e;
    for (; t = t.parent; )
        if (ru(t) && nu(e, t) === 0)
            return t
}
function ru({record: e}) {
    return !!(e.name || e.components && Object.keys(e.components).length || e.redirect)
}
function Hm(e) {
    const t = {};
    if (e === "" || e === "?")
        return t;
    const r = (e[0] === "?" ? e.slice(1) : e).split("&");
    for (let s = 0; s < r.length; ++s) {
        const o = r[s].replace(Ja, " ")
          , i = o.indexOf("=")
          , l = cr(i < 0 ? o : o.slice(0, i))
          , c = i < 0 ? null : cr(o.slice(i + 1));
        if (l in t) {
            let u = t[l];
            ht(u) || (u = t[l] = [u]),
            u.push(c)
        } else
            t[l] = c
    }
    return t
}
function Rl(e) {
    let t = "";
    for (let n in e) {
        const r = e[n];
        if (n = lm(n),
        r == null) {
            r !== void 0 && (t += (t.length ? "&" : "") + n);
            continue
        }
        (ht(r) ? r.map(o => o && mo(o)) : [r && mo(r)]).forEach(o => {
            o !== void 0 && (t += (t.length ? "&" : "") + n,
            o != null && (t += "=" + o))
        }
        )
    }
    return t
}
function Um(e) {
    const t = {};
    for (const n in e) {
        const r = e[n];
        r !== void 0 && (t[n] = ht(r) ? r.map(s => s == null ? null : "" + s) : r == null ? r : "" + r)
    }
    return t
}
const Vm = Symbol("")
  , Al = Symbol("")
  , bs = Symbol("")
  , su = Symbol("")
  , bo = Symbol("");
function $n() {
    let e = [];
    function t(r) {
        return e.push(r),
        () => {
            const s = e.indexOf(r);
            s > -1 && e.splice(s, 1)
        }
    }
    function n() {
        e = []
    }
    return {
        add: t,
        list: () => e.slice(),
        reset: n
    }
}
function Vt(e, t, n, r, s, o=i => i()) {
    const i = r && (r.enterCallbacks[s] = r.enterCallbacks[s] || []);
    return () => new Promise( (l, c) => {
        const u = d => {
            d === !1 ? c(Cn(4, {
                from: n,
                to: t
            })) : d instanceof Error ? c(d) : Rm(d) ? c(Cn(2, {
                from: t,
                to: d
            })) : (i && r.enterCallbacks[s] === i && typeof d == "function" && i.push(d),
            l())
        }
          , a = o( () => e.call(r && r.instances[s], t, n, u));
        let f = Promise.resolve(a);
        e.length < 3 && (f = f.then(u)),
        f.catch(d => c(d))
    }
    )
}
function Js(e, t, n, r, s=o => o()) {
    const o = [];
    for (const i of e)
        for (const l in i.components) {
            let c = i.components[l];
            if (!(t !== "beforeRouteEnter" && !i.instances[l]))
                if (Ka(c)) {
                    const a = (c.__vccOpts || c)[t];
                    a && o.push(Vt(a, n, r, i, l, s))
                } else {
                    let u = c();
                    o.push( () => u.then(a => {
                        if (!a)
                            throw new Error(`Couldn't resolve component "${l}" at "${i.path}"`);
                        const f = Gg(a) ? a.default : a;
                        i.mods[l] = a,
                        i.components[l] = f;
                        const g = (f.__vccOpts || f)[t];
                        return g && Vt(g, n, r, i, l, s)()
                    }
                    ))
                }
        }
    return o
}
function Ol(e) {
    const t = Xe(bs)
      , n = Xe(su)
      , r = G( () => {
        const c = Et(e.to);
        return t.resolve(c)
    }
    )
      , s = G( () => {
        const {matched: c} = r.value
          , {length: u} = c
          , a = c[u - 1]
          , f = n.matched;
        if (!a || !f.length)
            return -1;
        const d = f.findIndex(xn.bind(null, a));
        if (d > -1)
            return d;
        const g = Pl(c[u - 2]);
        return u > 1 && Pl(a) === g && f[f.length - 1].path !== g ? f.findIndex(xn.bind(null, c[u - 2])) : d
    }
    )
      , o = G( () => s.value > -1 && Jm(n.params, r.value.params))
      , i = G( () => s.value > -1 && s.value === n.matched.length - 1 && Za(n.params, r.value.params));
    function l(c={}) {
        if (Wm(c)) {
            const u = t[Et(e.replace) ? "replace" : "push"](Et(e.to)).catch(Jn);
            return e.viewTransition && typeof document < "u" && "startViewTransition"in document && document.startViewTransition( () => u),
            u
        }
        return Promise.resolve()
    }
    return {
        route: r,
        href: G( () => r.value.href),
        isActive: o,
        isExactActive: i,
        navigate: l
    }
}
function zm(e) {
    return e.length === 1 ? e[0] : e
}
const qm = Ce({
    name: "RouterLink",
    compatConfig: {
        MODE: 3
    },
    props: {
        to: {
            type: [String, Object],
            required: !0
        },
        replace: Boolean,
        activeClass: String,
        exactActiveClass: String,
        custom: Boolean,
        ariaCurrentValue: {
            type: String,
            default: "page"
        }
    },
    useLink: Ol,
    setup(e, {slots: t}) {
        const n = Ke(Ol(e))
          , {options: r} = Xe(bs)
          , s = G( () => ({
            [Il(e.activeClass, r.linkActiveClass, "router-link-active")]: n.isActive,
            [Il(e.exactActiveClass, r.linkExactActiveClass, "router-link-exact-active")]: n.isExactActive
        }));
        return () => {
            const o = t.default && zm(t.default(n));
            return e.custom ? o : Ko("a", {
                "aria-current": n.isExactActive ? e.ariaCurrentValue : null,
                href: n.href,
                onClick: n.navigate,
                class: s.value
            }, o)
        }
    }
})
  , Km = qm;
function Wm(e) {
    if (!(e.metaKey || e.altKey || e.ctrlKey || e.shiftKey) && !e.defaultPrevented && !(e.button !== void 0 && e.button !== 0)) {
        if (e.currentTarget && e.currentTarget.getAttribute) {
            const t = e.currentTarget.getAttribute("target");
            if (/\b_blank\b/i.test(t))
                return
        }
        return e.preventDefault && e.preventDefault(),
        !0
    }
}
function Jm(e, t) {
    for (const n in t) {
        const r = t[n]
          , s = e[n];
        if (typeof r == "string") {
            if (r !== s)
                return !1
        } else if (!ht(s) || s.length !== r.length || r.some( (o, i) => o !== s[i]))
            return !1
    }
    return !0
}
function Pl(e) {
    return e ? e.aliasOf ? e.aliasOf.path : e.path : ""
}
const Il = (e, t, n) => e ?? t ?? n
  , Ym = Ce({
    name: "RouterView",
    inheritAttrs: !1,
    props: {
        name: {
            type: String,
            default: "default"
        },
        route: Object
    },
    compatConfig: {
        MODE: 3
    },
    setup(e, {attrs: t, slots: n}) {
        const r = Xe(bo)
          , s = G( () => e.route || r.value)
          , o = Xe(Al, 0)
          , i = G( () => {
            let u = Et(o);
            const {matched: a} = s.value;
            let f;
            for (; (f = a[u]) && !f.components; )
                u++;
            return u
        }
        )
          , l = G( () => s.value.matched[i.value]);
        Wt(Al, G( () => i.value + 1)),
        Wt(Vm, l),
        Wt(bo, s);
        const c = se();
        return we( () => [c.value, l.value, e.name], ([u,a,f], [d,g,y]) => {
            a && (a.instances[f] = u,
            g && g !== a && u && u === d && (a.leaveGuards.size || (a.leaveGuards = g.leaveGuards),
            a.updateGuards.size || (a.updateGuards = g.updateGuards))),
            u && a && (!g || !xn(a, g) || !d) && (a.enterCallbacks[f] || []).forEach(b => b(u))
        }
        , {
            flush: "post"
        }),
        () => {
            const u = s.value
              , a = e.name
              , f = l.value
              , d = f && f.components[a];
            if (!d)
                return Bl(n.default, {
                    Component: d,
                    route: u
                });
            const g = f.props[a]
              , y = g ? g === !0 ? u.params : typeof g == "function" ? g(u) : g : null
              , w = Ko(d, de({}, y, t, {
                onVnodeUnmounted: _ => {
                    _.component.isUnmounted && (f.instances[a] = null)
                }
                ,
                ref: c
            }));
            return Bl(n.default, {
                Component: w,
                route: u
            }) || w
        }
    }
});
function Bl(e, t) {
    if (!e)
        return null;
    const n = e(t);
    return n.length === 1 ? n[0] : n
}
const Gm = Ym;
function Xm(e) {
    const t = $m(e.routes, e)
      , n = e.parseQuery || Hm
      , r = e.stringifyQuery || Rl
      , s = e.history
      , o = $n()
      , i = $n()
      , l = $n()
      , c = wf(Dt);
    let u = Dt;
    vn && e.scrollBehavior && "scrollRestoration"in history && (history.scrollRestoration = "manual");
    const a = Ks.bind(null, p => "" + p)
      , f = Ks.bind(null, am)
      , d = Ks.bind(null, cr);
    function g(p, T) {
        let x, L;
        return eu(p) ? (x = t.getRecordMatcher(p),
        L = T) : L = p,
        t.addRoute(L, x)
    }
    function y(p) {
        const T = t.getRecordMatcher(p);
        T && t.removeRoute(T)
    }
    function b() {
        return t.getRoutes().map(p => p.record)
    }
    function w(p) {
        return !!t.getRecordMatcher(p)
    }
    function _(p, T) {
        if (T = de({}, T || c.value),
        typeof p == "string") {
            const v = Ws(n, p, T.path)
              , C = t.resolve({
                path: v.path
            }, T)
              , A = s.createHref(v.fullPath);
            return de(v, C, {
                params: d(C.params),
                hash: cr(v.hash),
                redirectedFrom: void 0,
                href: A
            })
        }
        let x;
        if (p.path != null)
            x = de({}, p, {
                path: Ws(n, p.path, T.path).path
            });
        else {
            const v = de({}, p.params);
            for (const C in v)
                v[C] == null && delete v[C];
            x = de({}, p, {
                params: f(v)
            }),
            T.params = f(T.params)
        }
        const L = t.resolve(x, T)
          , J = p.hash || "";
        L.params = a(d(L.params));
        const h = dm(r, de({}, p, {
            hash: im(J),
            path: L.path
        }))
          , m = s.createHref(h);
        return de({
            fullPath: h,
            hash: J,
            query: r === Rl ? Um(p.query) : p.query || {}
        }, L, {
            redirectedFrom: void 0,
            href: m
        })
    }
    function S(p) {
        return typeof p == "string" ? Ws(n, p, c.value.path) : de({}, p)
    }
    function I(p, T) {
        if (u !== p)
            return Cn(8, {
                from: T,
                to: p
            })
    }
    function O(p) {
        return V(p)
    }
    function k(p) {
        return O(de(S(p), {
            replace: !0
        }))
    }
    function F(p) {
        const T = p.matched[p.matched.length - 1];
        if (T && T.redirect) {
            const {redirect: x} = T;
            let L = typeof x == "function" ? x(p) : x;
            return typeof L == "string" && (L = L.includes("?") || L.includes("#") ? L = S(L) : {
                path: L
            },
            L.params = {}),
            de({
                query: p.query,
                hash: p.hash,
                params: L.path != null ? {} : p.params
            }, L)
        }
    }
    function V(p, T) {
        const x = u = _(p)
          , L = c.value
          , J = p.state
          , h = p.force
          , m = p.replace === !0
          , v = F(x);
        if (v)
            return V(de(S(v), {
                state: typeof v == "object" ? de({}, J, v.state) : J,
                force: h,
                replace: m
            }), T || x);
        const C = x;
        C.redirectedFrom = T;
        let A;
        return !h && hm(r, L, x) && (A = Cn(16, {
            to: C,
            from: L
        }),
        K(L, L, !0, !1)),
        (A ? Promise.resolve(A) : z(C, L)).catch(R => Ot(R) ? Ot(R, 2) ? R : Je(R) : le(R, C, L)).then(R => {
            if (R) {
                if (Ot(R, 2))
                    return V(de({
                        replace: m
                    }, S(R.to), {
                        state: typeof R.to == "object" ? de({}, J, R.to.state) : J,
                        force: h
                    }), T || C)
            } else
                R = $(C, L, !0, m, J);
            return X(C, L, R),
            R
        }
        )
    }
    function U(p, T) {
        const x = I(p, T);
        return x ? Promise.reject(x) : Promise.resolve()
    }
    function B(p) {
        const T = xe.values().next().value;
        return T && typeof T.runWithContext == "function" ? T.runWithContext(p) : p()
    }
    function z(p, T) {
        let x;
        const [L,J,h] = Zm(p, T);
        x = Js(L.reverse(), "beforeRouteLeave", p, T);
        for (const v of L)
            v.leaveGuards.forEach(C => {
                x.push(Vt(C, p, T))
            }
            );
        const m = U.bind(null, p, T);
        return x.push(m),
        W(x).then( () => {
            x = [];
            for (const v of o.list())
                x.push(Vt(v, p, T));
            return x.push(m),
            W(x)
        }
        ).then( () => {
            x = Js(J, "beforeRouteUpdate", p, T);
            for (const v of J)
                v.updateGuards.forEach(C => {
                    x.push(Vt(C, p, T))
                }
                );
            return x.push(m),
            W(x)
        }
        ).then( () => {
            x = [];
            for (const v of h)
                if (v.beforeEnter)
                    if (ht(v.beforeEnter))
                        for (const C of v.beforeEnter)
                            x.push(Vt(C, p, T));
                    else
                        x.push(Vt(v.beforeEnter, p, T));
            return x.push(m),
            W(x)
        }
        ).then( () => (p.matched.forEach(v => v.enterCallbacks = {}),
        x = Js(h, "beforeRouteEnter", p, T, B),
        x.push(m),
        W(x))).then( () => {
            x = [];
            for (const v of i.list())
                x.push(Vt(v, p, T));
            return x.push(m),
            W(x)
        }
        ).catch(v => Ot(v, 8) ? v : Promise.reject(v))
    }
    function X(p, T, x) {
        l.list().forEach(L => B( () => L(p, T, x)))
    }
    function $(p, T, x, L, J) {
        const h = I(p, T);
        if (h)
            return h;
        const m = T === Dt
          , v = vn ? history.state : {};
        x && (L || m ? s.replace(p.fullPath, de({
            scroll: m && v && v.scroll
        }, J)) : s.push(p.fullPath, J)),
        c.value = p,
        K(p, T, x, m),
        Je()
    }
    let ne;
    function me() {
        ne || (ne = s.listen( (p, T, x) => {
            if (!H.listening)
                return;
            const L = _(p)
              , J = F(L);
            if (J) {
                V(de(J, {
                    replace: !0,
                    force: !0
                }), L).catch(Jn);
                return
            }
            u = L;
            const h = c.value;
            vn && Sm(bl(h.fullPath, x.delta), ys()),
            z(L, h).catch(m => Ot(m, 12) ? m : Ot(m, 2) ? (V(de(S(m.to), {
                force: !0
            }), L).then(v => {
                Ot(v, 20) && !x.delta && x.type === ar.pop && s.go(-1, !1)
            }
            ).catch(Jn),
            Promise.reject()) : (x.delta && s.go(-x.delta, !1),
            le(m, L, h))).then(m => {
                m = m || $(L, h, !1),
                m && (x.delta && !Ot(m, 8) ? s.go(-x.delta, !1) : x.type === ar.pop && Ot(m, 20) && s.go(-1, !1)),
                X(L, h, m)
            }
            ).catch(Jn)
        }
        ))
    }
    let Te = $n(), ae = $n(), Q;
    function le(p, T, x) {
        Je(p);
        const L = ae.list();
        return L.length ? L.forEach(J => J(p, T, x)) : console.error(p),
        Promise.reject(p)
    }
    function je() {
        return Q && c.value !== Dt ? Promise.resolve() : new Promise( (p, T) => {
            Te.add([p, T])
        }
        )
    }
    function Je(p) {
        return Q || (Q = !p,
        me(),
        Te.list().forEach( ([T,x]) => p ? x(p) : T()),
        Te.reset()),
        p
    }
    function K(p, T, x, L) {
        const {scrollBehavior: J} = e;
        if (!vn || !J)
            return Promise.resolve();
        const h = !x && _m(bl(p.fullPath, 0)) || (L || !x) && history.state && history.state.scroll || null;
        return Pe().then( () => J(p, T, h)).then(m => m && wm(m)).catch(m => le(m, p, T))
    }
    const ie = p => s.go(p);
    let fe;
    const xe = new Set
      , H = {
        currentRoute: c,
        listening: !0,
        addRoute: g,
        removeRoute: y,
        clearRoutes: t.clearRoutes,
        hasRoute: w,
        getRoutes: b,
        resolve: _,
        options: e,
        push: O,
        replace: k,
        go: ie,
        back: () => ie(-1),
        forward: () => ie(1),
        beforeEach: o.add,
        beforeResolve: i.add,
        afterEach: l.add,
        onError: ae.add,
        isReady: je,
        install(p) {
            const T = this;
            p.component("RouterLink", Km),
            p.component("RouterView", Gm),
            p.config.globalProperties.$router = T,
            Object.defineProperty(p.config.globalProperties, "$route", {
                enumerable: !0,
                get: () => Et(c)
            }),
            vn && !fe && c.value === Dt && (fe = !0,
            O(s.location).catch(J => {}
            ));
            const x = {};
            for (const J in Dt)
                Object.defineProperty(x, J, {
                    get: () => c.value[J],
                    enumerable: !0
                });
            p.provide(bs, T),
            p.provide(su, xc(x)),
            p.provide(bo, c);
            const L = p.unmount;
            xe.add(p),
            p.unmount = function() {
                xe.delete(p),
                xe.size < 1 && (u = Dt,
                ne && ne(),
                ne = null,
                c.value = Dt,
                fe = !1,
                Q = !1),
                L()
            }
        }
    };
    function W(p) {
        return p.reduce( (T, x) => T.then( () => B(x)), Promise.resolve())
    }
    return H
}
function Zm(e, t) {
    const n = []
      , r = []
      , s = []
      , o = Math.max(t.matched.length, e.matched.length);
    for (let i = 0; i < o; i++) {
        const l = t.matched[i];
        l && (e.matched.find(u => xn(u, l)) ? r.push(l) : n.push(l));
        const c = e.matched[i];
        c && (t.matched.find(u => xn(u, c)) || s.push(c))
    }
    return [n, r, s]
}
function Qb() {
    return Xe(bs)
}
const vo = Xm({
    history: Tm("/apps/klotski/"),
    routes: [{
        path: "/login",
        component: () => qs( () => import("./index-BeKCQTLS.js"), [])
    }, {
        path: "/",
        component: () => qs( () => import("./index-BeKCQTLS.js"), [])
    }, {
        path: "/game",
        component: () => qs( () => import("./index-BJ1R8LeI.js"), __vite__mapDeps([0, 1]))
    }]
});
/*!
 * pinia v2.3.1
 * (c) 2025 Eduardo San Martin Morote
 * @license MIT
 */
let ou;
const vs = e => ou = e
  , iu = Symbol();
function wo(e) {
    return e && typeof e == "object" && Object.prototype.toString.call(e) === "[object Object]" && typeof e.toJSON != "function"
}
var Gn;
(function(e) {
    e.direct = "direct",
    e.patchObject = "patch object",
    e.patchFunction = "patch function"
}
)(Gn || (Gn = {}));
function Qm() {
    const e = cc(!0)
      , t = e.run( () => se({}));
    let n = []
      , r = [];
    const s = Mo({
        install(o) {
            vs(s),
            s._a = o,
            o.provide(iu, s),
            o.config.globalProperties.$pinia = s,
            r.forEach(i => n.push(i)),
            r = []
        },
        use(o) {
            return this._a ? n.push(o) : r.push(o),
            this
        },
        _p: n,
        _a: null,
        _e: e,
        _s: new Map,
        state: t
    });
    return s
}
const lu = () => {}
;
function kl(e, t, n, r=lu) {
    e.push(t);
    const s = () => {
        const o = e.indexOf(t);
        o > -1 && (e.splice(o, 1),
        r())
    }
    ;
    return !n && ac() && Qu(s),
    s
}
function yn(e, ...t) {
    e.slice().forEach(n => {
        n(...t)
    }
    )
}
const ey = e => e()
  , Nl = Symbol()
  , Ys = Symbol();
function So(e, t) {
    e instanceof Map && t instanceof Map ? t.forEach( (n, r) => e.set(r, n)) : e instanceof Set && t instanceof Set && t.forEach(e.add, e);
    for (const n in t) {
        if (!t.hasOwnProperty(n))
            continue;
        const r = t[n]
          , s = e[n];
        wo(s) && wo(r) && e.hasOwnProperty(n) && !Ae(r) && !Kt(r) ? e[n] = So(s, r) : e[n] = r
    }
    return e
}
const ty = Symbol();
function ny(e) {
    return !wo(e) || !e.hasOwnProperty(ty)
}
const {assign: Mt} = Object;
function ry(e) {
    return !!(Ae(e) && e.effect)
}
function sy(e, t, n, r) {
    const {state: s, actions: o, getters: i} = t
      , l = n.state.value[e];
    let c;
    function u() {
        l || (n.state.value[e] = s ? s() : {});
        const a = Ef(n.state.value[e]);
        return Mt(a, o, Object.keys(i || {}).reduce( (f, d) => (f[d] = Mo(G( () => {
            vs(n);
            const g = n._s.get(e);
            return i[d].call(g, g)
        }
        )),
        f), {}))
    }
    return c = cu(e, u, t, n, r, !0),
    c
}
function cu(e, t, n={}, r, s, o) {
    let i;
    const l = Mt({
        actions: {}
    }, n)
      , c = {
        deep: !0
    };
    let u, a, f = [], d = [], g;
    const y = r.state.value[e];
    !o && !y && (r.state.value[e] = {}),
    se({});
    let b;
    function w(U) {
        let B;
        u = a = !1,
        typeof U == "function" ? (U(r.state.value[e]),
        B = {
            type: Gn.patchFunction,
            storeId: e,
            events: g
        }) : (So(r.state.value[e], U),
        B = {
            type: Gn.patchObject,
            payload: U,
            storeId: e,
            events: g
        });
        const z = b = Symbol();
        Pe().then( () => {
            b === z && (u = !0)
        }
        ),
        a = !0,
        yn(f, B, r.state.value[e])
    }
    const _ = o ? function() {
        const {state: B} = n
          , z = B ? B() : {};
        this.$patch(X => {
            Mt(X, z)
        }
        )
    }
    : lu;
    function S() {
        i.stop(),
        f = [],
        d = [],
        r._s.delete(e)
    }
    const I = (U, B="") => {
        if (Nl in U)
            return U[Ys] = B,
            U;
        const z = function() {
            vs(r);
            const X = Array.from(arguments)
              , $ = []
              , ne = [];
            function me(Q) {
                $.push(Q)
            }
            function Te(Q) {
                ne.push(Q)
            }
            yn(d, {
                args: X,
                name: z[Ys],
                store: k,
                after: me,
                onError: Te
            });
            let ae;
            try {
                ae = U.apply(this && this.$id === e ? this : k, X)
            } catch (Q) {
                throw yn(ne, Q),
                Q
            }
            return ae instanceof Promise ? ae.then(Q => (yn($, Q),
            Q)).catch(Q => (yn(ne, Q),
            Promise.reject(Q))) : (yn($, ae),
            ae)
        };
        return z[Nl] = !0,
        z[Ys] = B,
        z
    }
      , O = {
        _p: r,
        $id: e,
        $onAction: kl.bind(null, d),
        $patch: w,
        $reset: _,
        $subscribe(U, B={}) {
            const z = kl(f, U, B.detached, () => X())
              , X = i.run( () => we( () => r.state.value[e], $ => {
                (B.flush === "sync" ? a : u) && U({
                    storeId: e,
                    type: Gn.direct,
                    events: g
                }, $)
            }
            , Mt({}, c, B)));
            return z
        },
        $dispose: S
    }
      , k = Ke(O);
    r._s.set(e, k);
    const V = (r._a && r._a.runWithContext || ey)( () => r._e.run( () => (i = cc()).run( () => t({
        action: I
    }))));
    for (const U in V) {
        const B = V[U];
        if (Ae(B) && !ry(B) || Kt(B))
            o || (y && ny(B) && (Ae(B) ? B.value = y[U] : So(B, y[U])),
            r.state.value[e][U] = B);
        else if (typeof B == "function") {
            const z = I(B, U);
            V[U] = z,
            l.actions[U] = B
        }
    }
    return Mt(k, V),
    Mt(ue(k), V),
    Object.defineProperty(k, "$state", {
        get: () => r.state.value[e],
        set: U => {
            w(B => {
                Mt(B, U)
            }
            )
        }
    }),
    r._p.forEach(U => {
        Mt(k, i.run( () => U({
            store: k,
            app: r._a,
            pinia: r,
            options: l
        })))
    }
    ),
    y && o && n.hydrate && n.hydrate(k.$state, y),
    u = !0,
    a = !0,
    k
}
/*! #__NO_SIDE_EFFECTS__ */
function oy(e, t, n) {
    let r, s;
    const o = typeof t == "function";
    r = e,
    s = o ? n : t;
    function i(l, c) {
        const u = rd();
        return l = l || (u ? Xe(iu, null) : null),
        l && vs(l),
        l = ou,
        l._s.has(r) || (o ? cu(r, t, s, l) : sy(r, s, l)),
        l._s.get(r)
    }
    return i.$id = r,
    i
}
const iy = /"(?:_|\\u0{2}5[Ff]){2}(?:p|\\u0{2}70)(?:r|\\u0{2}72)(?:o|\\u0{2}6[Ff])(?:t|\\u0{2}74)(?:o|\\u0{2}6[Ff])(?:_|\\u0{2}5[Ff]){2}"\s*:/
  , ly = /"(?:c|\\u0063)(?:o|\\u006[Ff])(?:n|\\u006[Ee])(?:s|\\u0073)(?:t|\\u0074)(?:r|\\u0072)(?:u|\\u0075)(?:c|\\u0063)(?:t|\\u0074)(?:o|\\u006[Ff])(?:r|\\u0072)"\s*:/
  , cy = /^\s*["[{]|^\s*-?\d{1,16}(\.\d{1,17})?([Ee][+-]?\d+)?\s*$/;
function ay(e, t) {
    if (e === "__proto__" || e === "constructor" && t && typeof t == "object" && "prototype"in t) {
        uy(e);
        return
    }
    return t
}
function uy(e) {
    console.warn(`[destr] Dropping "${e}" key to prevent prototype pollution.`)
}
function fy(e, t={}) {
    if (typeof e != "string")
        return e;
    const n = e.trim();
    if (e[0] === '"' && e.endsWith('"') && !e.includes("\\"))
        return n.slice(1, -1);
    if (n.length <= 9) {
        const r = n.toLowerCase();
        if (r === "true")
            return !0;
        if (r === "false")
            return !1;
        if (r === "undefined")
            return;
        if (r === "null")
            return null;
        if (r === "nan")
            return Number.NaN;
        if (r === "infinity")
            return Number.POSITIVE_INFINITY;
        if (r === "-infinity")
            return Number.NEGATIVE_INFINITY
    }
    if (!cy.test(e)) {
        if (t.strict)
            throw new SyntaxError("[destr] Invalid JSON");
        return e
    }
    try {
        if (iy.test(e) || ly.test(e)) {
            if (t.strict)
                throw new Error("[destr] Possible prototype pollution");
            return JSON.parse(e, ay)
        }
        return JSON.parse(e)
    } catch (r) {
        if (t.strict)
            throw r;
        return e
    }
}
function dy(e, t) {
    if (e == null)
        return;
    let n = e;
    for (let r = 0; r < t.length; r++) {
        if (n == null || n[t[r]] == null)
            return;
        n = n[t[r]]
    }
    return n
}
function ri(e, t, n) {
    if (n.length === 0)
        return t;
    const r = n[0];
    return n.length > 1 && (t = ri(typeof e != "object" || e === null || !Object.prototype.hasOwnProperty.call(e, r) ? Number.isInteger(Number(n[1])) ? [] : {} : e[r], t, Array.prototype.slice.call(n, 1))),
    Number.isInteger(Number(r)) && Array.isArray(e) ? e.slice()[r] : Object.assign({}, e, {
        [r]: t
    })
}
function au(e, t) {
    if (e == null || t.length === 0)
        return e;
    if (t.length === 1) {
        if (e == null)
            return e;
        if (Number.isInteger(t[0]) && Array.isArray(e))
            return Array.prototype.slice.call(e, 0).splice(t[0], 1);
        const n = {};
        for (const r in e)
            n[r] = e[r];
        return delete n[t[0]],
        n
    }
    if (e[t[0]] == null) {
        if (Number.isInteger(t[0]) && Array.isArray(e))
            return Array.prototype.concat.call([], e);
        const n = {};
        for (const r in e)
            n[r] = e[r];
        return n
    }
    return ri(e, au(e[t[0]], Array.prototype.slice.call(t, 1)), [t[0]])
}
function uu(e, t) {
    return t.map(n => n.split(".")).map(n => [n, dy(e, n)]).filter(n => n[1] !== void 0).reduce( (n, r) => ri(n, r[1], r[0]), {})
}
function fu(e, t) {
    return t.map(n => n.split(".")).reduce( (n, r) => au(n, r), e)
}
function Ll(e, {storage: t, serializer: n, key: r, debug: s, pick: o, omit: i, beforeHydrate: l, afterHydrate: c}, u, a=!0) {
    try {
        a && (l == null || l(u));
        const f = t.getItem(r);
        if (f) {
            const d = n.deserialize(f)
              , g = o ? uu(d, o) : d
              , y = i ? fu(g, i) : g;
            e.$patch(y)
        }
        a && (c == null || c(u))
    } catch (f) {
        s && console.error("[pinia-plugin-persistedstate]", f)
    }
}
function $l(e, {storage: t, serializer: n, key: r, debug: s, pick: o, omit: i}) {
    try {
        const l = o ? uu(e, o) : e
          , c = i ? fu(l, i) : l
          , u = n.serialize(c);
        t.setItem(r, u)
    } catch (l) {
        s && console.error("[pinia-plugin-persistedstate]", l)
    }
}
function hy(e, t, n) {
    const {pinia: r, store: s, options: {persist: o=n}} = e;
    if (!o)
        return;
    if (!(s.$id in r.state.value)) {
        const c = r._s.get(s.$id.replace("__hot:", ""));
        c && Promise.resolve().then( () => c.$persist());
        return
    }
    const l = (Array.isArray(o) ? o : o === !0 ? [{}] : [o]).map(t);
    s.$hydrate = ({runHooks: c=!0}={}) => {
        l.forEach(u => {
            Ll(s, u, e, c)
        }
        )
    }
    ,
    s.$persist = () => {
        l.forEach(c => {
            $l(s.$state, c)
        }
        )
    }
    ,
    l.forEach(c => {
        Ll(s, c, e),
        s.$subscribe( (u, a) => $l(a, c), {
            detached: !0
        })
    }
    )
}
function py(e={}) {
    return function(t) {
        hy(t, n => ({
            key: (e.key ? e.key : r => r)(n.key ?? t.store.$id),
            debug: n.debug ?? e.debug ?? !1,
            serializer: n.serializer ?? e.serializer ?? {
                serialize: r => JSON.stringify(r),
                deserialize: r => fy(r)
            },
            storage: n.storage ?? e.storage ?? window.localStorage,
            beforeHydrate: n.beforeHydrate,
            afterHydrate: n.afterHydrate,
            pick: n.pick,
            omit: n.omit
        }), e.auto ?? !1)
    }
}
var gy = py();
function du(e, t) {
    return function() {
        return e.apply(t, arguments)
    }
}
const {toString: my} = Object.prototype
  , {getPrototypeOf: si} = Object
  , ws = (e => t => {
    const n = my.call(t);
    return e[n] || (e[n] = n.slice(8, -1).toLowerCase())
}
)(Object.create(null))
  , pt = e => (e = e.toLowerCase(),
t => ws(t) === e)
  , Ss = e => t => typeof t === e
  , {isArray: On} = Array
  , ur = Ss("undefined");
function yy(e) {
    return e !== null && !ur(e) && e.constructor !== null && !ur(e.constructor) && nt(e.constructor.isBuffer) && e.constructor.isBuffer(e)
}
const hu = pt("ArrayBuffer");
function by(e) {
    let t;
    return typeof ArrayBuffer < "u" && ArrayBuffer.isView ? t = ArrayBuffer.isView(e) : t = e && e.buffer && hu(e.buffer),
    t
}
const vy = Ss("string")
  , nt = Ss("function")
  , pu = Ss("number")
  , _s = e => e !== null && typeof e == "object"
  , wy = e => e === !0 || e === !1
  , Nr = e => {
    if (ws(e) !== "object")
        return !1;
    const t = si(e);
    return (t === null || t === Object.prototype || Object.getPrototypeOf(t) === null) && !(Symbol.toStringTag in e) && !(Symbol.iterator in e)
}
  , Sy = pt("Date")
  , _y = pt("File")
  , Ey = pt("Blob")
  , xy = pt("FileList")
  , Cy = e => _s(e) && nt(e.pipe)
  , Ty = e => {
    let t;
    return e && (typeof FormData == "function" && e instanceof FormData || nt(e.append) && ((t = ws(e)) === "formdata" || t === "object" && nt(e.toString) && e.toString() === "[object FormData]"))
}
  , Ry = pt("URLSearchParams")
  , [Ay,Oy,Py,Iy] = ["ReadableStream", "Request", "Response", "Headers"].map(pt)
  , By = e => e.trim ? e.trim() : e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g, "");
function br(e, t, {allOwnKeys: n=!1}={}) {
    if (e === null || typeof e > "u")
        return;
    let r, s;
    if (typeof e != "object" && (e = [e]),
    On(e))
        for (r = 0,
        s = e.length; r < s; r++)
            t.call(null, e[r], r, e);
    else {
        const o = n ? Object.getOwnPropertyNames(e) : Object.keys(e)
          , i = o.length;
        let l;
        for (r = 0; r < i; r++)
            l = o[r],
            t.call(null, e[l], l, e)
    }
}
function gu(e, t) {
    t = t.toLowerCase();
    const n = Object.keys(e);
    let r = n.length, s;
    for (; r-- > 0; )
        if (s = n[r],
        t === s.toLowerCase())
            return s;
    return null
}
const ln = typeof globalThis < "u" ? globalThis : typeof self < "u" ? self : typeof window < "u" ? window : global
  , mu = e => !ur(e) && e !== ln;
function _o() {
    const {caseless: e} = mu(this) && this || {}
      , t = {}
      , n = (r, s) => {
        const o = e && gu(t, s) || s;
        Nr(t[o]) && Nr(r) ? t[o] = _o(t[o], r) : Nr(r) ? t[o] = _o({}, r) : On(r) ? t[o] = r.slice() : t[o] = r
    }
    ;
    for (let r = 0, s = arguments.length; r < s; r++)
        arguments[r] && br(arguments[r], n);
    return t
}
const ky = (e, t, n, {allOwnKeys: r}={}) => (br(t, (s, o) => {
    n && nt(s) ? e[o] = du(s, n) : e[o] = s
}
, {
    allOwnKeys: r
}),
e)
  , Ny = e => (e.charCodeAt(0) === 65279 && (e = e.slice(1)),
e)
  , Ly = (e, t, n, r) => {
    e.prototype = Object.create(t.prototype, r),
    e.prototype.constructor = e,
    Object.defineProperty(e, "super", {
        value: t.prototype
    }),
    n && Object.assign(e.prototype, n)
}
  , $y = (e, t, n, r) => {
    let s, o, i;
    const l = {};
    if (t = t || {},
    e == null)
        return t;
    do {
        for (s = Object.getOwnPropertyNames(e),
        o = s.length; o-- > 0; )
            i = s[o],
            (!r || r(i, e, t)) && !l[i] && (t[i] = e[i],
            l[i] = !0);
        e = n !== !1 && si(e)
    } while (e && (!n || n(e, t)) && e !== Object.prototype);
    return t
}
  , Fy = (e, t, n) => {
    e = String(e),
    (n === void 0 || n > e.length) && (n = e.length),
    n -= t.length;
    const r = e.indexOf(t, n);
    return r !== -1 && r === n
}
  , Dy = e => {
    if (!e)
        return null;
    if (On(e))
        return e;
    let t = e.length;
    if (!pu(t))
        return null;
    const n = new Array(t);
    for (; t-- > 0; )
        n[t] = e[t];
    return n
}
  , My = (e => t => e && t instanceof e)(typeof Uint8Array < "u" && si(Uint8Array))
  , jy = (e, t) => {
    const r = (e && e[Symbol.iterator]).call(e);
    let s;
    for (; (s = r.next()) && !s.done; ) {
        const o = s.value;
        t.call(e, o[0], o[1])
    }
}
  , Hy = (e, t) => {
    let n;
    const r = [];
    for (; (n = e.exec(t)) !== null; )
        r.push(n);
    return r
}
  , Uy = pt("HTMLFormElement")
  , Vy = e => e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g, function(n, r, s) {
    return r.toUpperCase() + s
})
  , Fl = ( ({hasOwnProperty: e}) => (t, n) => e.call(t, n))(Object.prototype)
  , zy = pt("RegExp")
  , yu = (e, t) => {
    const n = Object.getOwnPropertyDescriptors(e)
      , r = {};
    br(n, (s, o) => {
        let i;
        (i = t(s, o, e)) !== !1 && (r[o] = i || s)
    }
    ),
    Object.defineProperties(e, r)
}
  , qy = e => {
    yu(e, (t, n) => {
        if (nt(e) && ["arguments", "caller", "callee"].indexOf(n) !== -1)
            return !1;
        const r = e[n];
        if (nt(r)) {
            if (t.enumerable = !1,
            "writable"in t) {
                t.writable = !1;
                return
            }
            t.set || (t.set = () => {
                throw Error("Can not rewrite read-only method '" + n + "'")
            }
            )
        }
    }
    )
}
  , Ky = (e, t) => {
    const n = {}
      , r = s => {
        s.forEach(o => {
            n[o] = !0
        }
        )
    }
    ;
    return On(e) ? r(e) : r(String(e).split(t)),
    n
}
  , Wy = () => {}
  , Jy = (e, t) => e != null && Number.isFinite(e = +e) ? e : t
  , Gs = "abcdefghijklmnopqrstuvwxyz"
  , Dl = "0123456789"
  , bu = {
    DIGIT: Dl,
    ALPHA: Gs,
    ALPHA_DIGIT: Gs + Gs.toUpperCase() + Dl
}
  , Yy = (e=16, t=bu.ALPHA_DIGIT) => {
    let n = "";
    const {length: r} = t;
    for (; e--; )
        n += t[Math.random() * r | 0];
    return n
}
;
function Gy(e) {
    return !!(e && nt(e.append) && e[Symbol.toStringTag] === "FormData" && e[Symbol.iterator])
}
const Xy = e => {
    const t = new Array(10)
      , n = (r, s) => {
        if (_s(r)) {
            if (t.indexOf(r) >= 0)
                return;
            if (!("toJSON"in r)) {
                t[s] = r;
                const o = On(r) ? [] : {};
                return br(r, (i, l) => {
                    const c = n(i, s + 1);
                    !ur(c) && (o[l] = c)
                }
                ),
                t[s] = void 0,
                o
            }
        }
        return r
    }
    ;
    return n(e, 0)
}
  , Zy = pt("AsyncFunction")
  , Qy = e => e && (_s(e) || nt(e)) && nt(e.then) && nt(e.catch)
  , vu = ( (e, t) => e ? setImmediate : t ? ( (n, r) => (ln.addEventListener("message", ({source: s, data: o}) => {
    s === ln && o === n && r.length && r.shift()()
}
, !1),
s => {
    r.push(s),
    ln.postMessage(n, "*")
}
))(`axios@${Math.random()}`, []) : n => setTimeout(n))(typeof setImmediate == "function", nt(ln.postMessage))
  , eb = typeof queueMicrotask < "u" ? queueMicrotask.bind(ln) : typeof process < "u" && process.nextTick || vu
  , E = {
    isArray: On,
    isArrayBuffer: hu,
    isBuffer: yy,
    isFormData: Ty,
    isArrayBufferView: by,
    isString: vy,
    isNumber: pu,
    isBoolean: wy,
    isObject: _s,
    isPlainObject: Nr,
    isReadableStream: Ay,
    isRequest: Oy,
    isResponse: Py,
    isHeaders: Iy,
    isUndefined: ur,
    isDate: Sy,
    isFile: _y,
    isBlob: Ey,
    isRegExp: zy,
    isFunction: nt,
    isStream: Cy,
    isURLSearchParams: Ry,
    isTypedArray: My,
    isFileList: xy,
    forEach: br,
    merge: _o,
    extend: ky,
    trim: By,
    stripBOM: Ny,
    inherits: Ly,
    toFlatObject: $y,
    kindOf: ws,
    kindOfTest: pt,
    endsWith: Fy,
    toArray: Dy,
    forEachEntry: jy,
    matchAll: Hy,
    isHTMLForm: Uy,
    hasOwnProperty: Fl,
    hasOwnProp: Fl,
    reduceDescriptors: yu,
    freezeMethods: qy,
    toObjectSet: Ky,
    toCamelCase: Vy,
    noop: Wy,
    toFiniteNumber: Jy,
    findKey: gu,
    global: ln,
    isContextDefined: mu,
    ALPHABET: bu,
    generateString: Yy,
    isSpecCompliantForm: Gy,
    toJSONObject: Xy,
    isAsyncFn: Zy,
    isThenable: Qy,
    setImmediate: vu,
    asap: eb
};
function oe(e, t, n, r, s) {
    Error.call(this),
    Error.captureStackTrace ? Error.captureStackTrace(this, this.constructor) : this.stack = new Error().stack,
    this.message = e,
    this.name = "AxiosError",
    t && (this.code = t),
    n && (this.config = n),
    r && (this.request = r),
    s && (this.response = s,
    this.status = s.status ? s.status : null)
}
E.inherits(oe, Error, {
    toJSON: function() {
        return {
            message: this.message,
            name: this.name,
            description: this.description,
            number: this.number,
            fileName: this.fileName,
            lineNumber: this.lineNumber,
            columnNumber: this.columnNumber,
            stack: this.stack,
            config: E.toJSONObject(this.config),
            code: this.code,
            status: this.status
        }
    }
});
const wu = oe.prototype
  , Su = {};
["ERR_BAD_OPTION_VALUE", "ERR_BAD_OPTION", "ECONNABORTED", "ETIMEDOUT", "ERR_NETWORK", "ERR_FR_TOO_MANY_REDIRECTS", "ERR_DEPRECATED", "ERR_BAD_RESPONSE", "ERR_BAD_REQUEST", "ERR_CANCELED", "ERR_NOT_SUPPORT", "ERR_INVALID_URL"].forEach(e => {
    Su[e] = {
        value: e
    }
}
);
Object.defineProperties(oe, Su);
Object.defineProperty(wu, "isAxiosError", {
    value: !0
});
oe.from = (e, t, n, r, s, o) => {
    const i = Object.create(wu);
    return E.toFlatObject(e, i, function(c) {
        return c !== Error.prototype
    }, l => l !== "isAxiosError"),
    oe.call(i, e.message, t, n, r, s),
    i.cause = e,
    i.name = e.name,
    o && Object.assign(i, o),
    i
}
;
const tb = null;
function Eo(e) {
    return E.isPlainObject(e) || E.isArray(e)
}
function _u(e) {
    return E.endsWith(e, "[]") ? e.slice(0, -2) : e
}
function Ml(e, t, n) {
    return e ? e.concat(t).map(function(s, o) {
        return s = _u(s),
        !n && o ? "[" + s + "]" : s
    }).join(n ? "." : "") : t
}
function nb(e) {
    return E.isArray(e) && !e.some(Eo)
}
const rb = E.toFlatObject(E, {}, null, function(t) {
    return /^is[A-Z]/.test(t)
});
function Es(e, t, n) {
    if (!E.isObject(e))
        throw new TypeError("target must be an object");
    t = t || new FormData,
    n = E.toFlatObject(n, {
        metaTokens: !0,
        dots: !1,
        indexes: !1
    }, !1, function(b, w) {
        return !E.isUndefined(w[b])
    });
    const r = n.metaTokens
      , s = n.visitor || a
      , o = n.dots
      , i = n.indexes
      , c = (n.Blob || typeof Blob < "u" && Blob) && E.isSpecCompliantForm(t);
    if (!E.isFunction(s))
        throw new TypeError("visitor must be a function");
    function u(y) {
        if (y === null)
            return "";
        if (E.isDate(y))
            return y.toISOString();
        if (!c && E.isBlob(y))
            throw new oe("Blob is not supported. Use a Buffer instead.");
        return E.isArrayBuffer(y) || E.isTypedArray(y) ? c && typeof Blob == "function" ? new Blob([y]) : Buffer.from(y) : y
    }
    function a(y, b, w) {
        let _ = y;
        if (y && !w && typeof y == "object") {
            if (E.endsWith(b, "{}"))
                b = r ? b : b.slice(0, -2),
                y = JSON.stringify(y);
            else if (E.isArray(y) && nb(y) || (E.isFileList(y) || E.endsWith(b, "[]")) && (_ = E.toArray(y)))
                return b = _u(b),
                _.forEach(function(I, O) {
                    !(E.isUndefined(I) || I === null) && t.append(i === !0 ? Ml([b], O, o) : i === null ? b : b + "[]", u(I))
                }),
                !1
        }
        return Eo(y) ? !0 : (t.append(Ml(w, b, o), u(y)),
        !1)
    }
    const f = []
      , d = Object.assign(rb, {
        defaultVisitor: a,
        convertValue: u,
        isVisitable: Eo
    });
    function g(y, b) {
        if (!E.isUndefined(y)) {
            if (f.indexOf(y) !== -1)
                throw Error("Circular reference detected in " + b.join("."));
            f.push(y),
            E.forEach(y, function(_, S) {
                (!(E.isUndefined(_) || _ === null) && s.call(t, _, E.isString(S) ? S.trim() : S, b, d)) === !0 && g(_, b ? b.concat(S) : [S])
            }),
            f.pop()
        }
    }
    if (!E.isObject(e))
        throw new TypeError("data must be an object");
    return g(e),
    t
}
function jl(e) {
    const t = {
        "!": "%21",
        "'": "%27",
        "(": "%28",
        ")": "%29",
        "~": "%7E",
        "%20": "+",
        "%00": "\0"
    };
    return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g, function(r) {
        return t[r]
    })
}
function oi(e, t) {
    this._pairs = [],
    e && Es(e, this, t)
}
const Eu = oi.prototype;
Eu.append = function(t, n) {
    this._pairs.push([t, n])
}
;
Eu.toString = function(t) {
    const n = t ? function(r) {
        return t.call(this, r, jl)
    }
    : jl;
    return this._pairs.map(function(s) {
        return n(s[0]) + "=" + n(s[1])
    }, "").join("&")
}
;
function sb(e) {
    return encodeURIComponent(e).replace(/%3A/gi, ":").replace(/%24/g, "$").replace(/%2C/gi, ",").replace(/%20/g, "+").replace(/%5B/gi, "[").replace(/%5D/gi, "]")
}
function xu(e, t, n) {
    if (!t)
        return e;
    const r = n && n.encode || sb;
    E.isFunction(n) && (n = {
        serialize: n
    });
    const s = n && n.serialize;
    let o;
    if (s ? o = s(t, n) : o = E.isURLSearchParams(t) ? t.toString() : new oi(t,n).toString(r),
    o) {
        const i = e.indexOf("#");
        i !== -1 && (e = e.slice(0, i)),
        e += (e.indexOf("?") === -1 ? "?" : "&") + o
    }
    return e
}
class Hl {
    constructor() {
        this.handlers = []
    }
    use(t, n, r) {
        return this.handlers.push({
            fulfilled: t,
            rejected: n,
            synchronous: r ? r.synchronous : !1,
            runWhen: r ? r.runWhen : null
        }),
        this.handlers.length - 1
    }
    eject(t) {
        this.handlers[t] && (this.handlers[t] = null)
    }
    clear() {
        this.handlers && (this.handlers = [])
    }
    forEach(t) {
        E.forEach(this.handlers, function(r) {
            r !== null && t(r)
        })
    }
}
const Cu = {
    silentJSONParsing: !0,
    forcedJSONParsing: !0,
    clarifyTimeoutError: !1
}
  , ob = typeof URLSearchParams < "u" ? URLSearchParams : oi
  , ib = typeof FormData < "u" ? FormData : null
  , lb = typeof Blob < "u" ? Blob : null
  , cb = {
    isBrowser: !0,
    classes: {
        URLSearchParams: ob,
        FormData: ib,
        Blob: lb
    },
    protocols: ["http", "https", "file", "blob", "url", "data"]
}
  , ii = typeof window < "u" && typeof document < "u"
  , xo = typeof navigator == "object" && navigator || void 0
  , ab = ii && (!xo || ["ReactNative", "NativeScript", "NS"].indexOf(xo.product) < 0)
  , ub = typeof WorkerGlobalScope < "u" && self instanceof WorkerGlobalScope && typeof self.importScripts == "function"
  , fb = ii && window.location.href || "http://localhost"
  , db = Object.freeze(Object.defineProperty({
    __proto__: null,
    hasBrowserEnv: ii,
    hasStandardBrowserEnv: ab,
    hasStandardBrowserWebWorkerEnv: ub,
    navigator: xo,
    origin: fb
}, Symbol.toStringTag, {
    value: "Module"
}))
  , Me = {
    ...db,
    ...cb
};
function hb(e, t) {
    return Es(e, new Me.classes.URLSearchParams, Object.assign({
        visitor: function(n, r, s, o) {
            return Me.isNode && E.isBuffer(n) ? (this.append(r, n.toString("base64")),
            !1) : o.defaultVisitor.apply(this, arguments)
        }
    }, t))
}
function pb(e) {
    return E.matchAll(/\w+|\[(\w*)]/g, e).map(t => t[0] === "[]" ? "" : t[1] || t[0])
}
function gb(e) {
    const t = {}
      , n = Object.keys(e);
    let r;
    const s = n.length;
    let o;
    for (r = 0; r < s; r++)
        o = n[r],
        t[o] = e[o];
    return t
}
function Tu(e) {
    function t(n, r, s, o) {
        let i = n[o++];
        if (i === "__proto__")
            return !0;
        const l = Number.isFinite(+i)
          , c = o >= n.length;
        return i = !i && E.isArray(s) ? s.length : i,
        c ? (E.hasOwnProp(s, i) ? s[i] = [s[i], r] : s[i] = r,
        !l) : ((!s[i] || !E.isObject(s[i])) && (s[i] = []),
        t(n, r, s[i], o) && E.isArray(s[i]) && (s[i] = gb(s[i])),
        !l)
    }
    if (E.isFormData(e) && E.isFunction(e.entries)) {
        const n = {};
        return E.forEachEntry(e, (r, s) => {
            t(pb(r), s, n, 0)
        }
        ),
        n
    }
    return null
}
function mb(e, t, n) {
    if (E.isString(e))
        try {
            return (t || JSON.parse)(e),
            E.trim(e)
        } catch (r) {
            if (r.name !== "SyntaxError")
                throw r
        }
    return (0,
    JSON.stringify)(e)
}
const vr = {
    transitional: Cu,
    adapter: ["xhr", "http", "fetch"],
    transformRequest: [function(t, n) {
        const r = n.getContentType() || ""
          , s = r.indexOf("application/json") > -1
          , o = E.isObject(t);
        if (o && E.isHTMLForm(t) && (t = new FormData(t)),
        E.isFormData(t))
            return s ? JSON.stringify(Tu(t)) : t;
        if (E.isArrayBuffer(t) || E.isBuffer(t) || E.isStream(t) || E.isFile(t) || E.isBlob(t) || E.isReadableStream(t))
            return t;
        if (E.isArrayBufferView(t))
            return t.buffer;
        if (E.isURLSearchParams(t))
            return n.setContentType("application/x-www-form-urlencoded;charset=utf-8", !1),
            t.toString();
        let l;
        if (o) {
            if (r.indexOf("application/x-www-form-urlencoded") > -1)
                return hb(t, this.formSerializer).toString();
            if ((l = E.isFileList(t)) || r.indexOf("multipart/form-data") > -1) {
                const c = this.env && this.env.FormData;
                return Es(l ? {
                    "files[]": t
                } : t, c && new c, this.formSerializer)
            }
        }
        return o || s ? (n.setContentType("application/json", !1),
        mb(t)) : t
    }
    ],
    transformResponse: [function(t) {
        const n = this.transitional || vr.transitional
          , r = n && n.forcedJSONParsing
          , s = this.responseType === "json";
        if (E.isResponse(t) || E.isReadableStream(t))
            return t;
        if (t && E.isString(t) && (r && !this.responseType || s)) {
            const i = !(n && n.silentJSONParsing) && s;
            try {
                return JSON.parse(t)
            } catch (l) {
                if (i)
                    throw l.name === "SyntaxError" ? oe.from(l, oe.ERR_BAD_RESPONSE, this, null, this.response) : l
            }
        }
        return t
    }
    ],
    timeout: 0,
    xsrfCookieName: "XSRF-TOKEN",
    xsrfHeaderName: "X-XSRF-TOKEN",
    maxContentLength: -1,
    maxBodyLength: -1,
    env: {
        FormData: Me.classes.FormData,
        Blob: Me.classes.Blob
    },
    validateStatus: function(t) {
        return t >= 200 && t < 300
    },
    headers: {
        common: {
            Accept: "application/json, text/plain, */*",
            "Content-Type": void 0
        }
    }
};
E.forEach(["delete", "get", "head", "post", "put", "patch"], e => {
    vr.headers[e] = {}
}
);
const yb = E.toObjectSet(["age", "authorization", "content-length", "content-type", "etag", "expires", "from", "host", "if-modified-since", "if-unmodified-since", "last-modified", "location", "max-forwards", "proxy-authorization", "referer", "retry-after", "user-agent"])
  , bb = e => {
    const t = {};
    let n, r, s;
    return e && e.split(`
`).forEach(function(i) {
        s = i.indexOf(":"),
        n = i.substring(0, s).trim().toLowerCase(),
        r = i.substring(s + 1).trim(),
        !(!n || t[n] && yb[n]) && (n === "set-cookie" ? t[n] ? t[n].push(r) : t[n] = [r] : t[n] = t[n] ? t[n] + ", " + r : r)
    }),
    t
}
  , Ul = Symbol("internals");
function Fn(e) {
    return e && String(e).trim().toLowerCase()
}
function Lr(e) {
    return e === !1 || e == null ? e : E.isArray(e) ? e.map(Lr) : String(e)
}
function vb(e) {
    const t = Object.create(null)
      , n = /([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;
    let r;
    for (; r = n.exec(e); )
        t[r[1]] = r[2];
    return t
}
const wb = e => /^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());
function Xs(e, t, n, r, s) {
    if (E.isFunction(r))
        return r.call(this, t, n);
    if (s && (t = n),
    !!E.isString(t)) {
        if (E.isString(r))
            return t.indexOf(r) !== -1;
        if (E.isRegExp(r))
            return r.test(t)
    }
}
function Sb(e) {
    return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g, (t, n, r) => n.toUpperCase() + r)
}
function _b(e, t) {
    const n = E.toCamelCase(" " + t);
    ["get", "set", "has"].forEach(r => {
        Object.defineProperty(e, r + n, {
            value: function(s, o, i) {
                return this[r].call(this, t, s, o, i)
            },
            configurable: !0
        })
    }
    )
}
class Ze {
    constructor(t) {
        t && this.set(t)
    }
    set(t, n, r) {
        const s = this;
        function o(l, c, u) {
            const a = Fn(c);
            if (!a)
                throw new Error("header name must be a non-empty string");
            const f = E.findKey(s, a);
            (!f || s[f] === void 0 || u === !0 || u === void 0 && s[f] !== !1) && (s[f || c] = Lr(l))
        }
        const i = (l, c) => E.forEach(l, (u, a) => o(u, a, c));
        if (E.isPlainObject(t) || t instanceof this.constructor)
            i(t, n);
        else if (E.isString(t) && (t = t.trim()) && !wb(t))
            i(bb(t), n);
        else if (E.isHeaders(t))
            for (const [l,c] of t.entries())
                o(c, l, r);
        else
            t != null && o(n, t, r);
        return this
    }
    get(t, n) {
        if (t = Fn(t),
        t) {
            const r = E.findKey(this, t);
            if (r) {
                const s = this[r];
                if (!n)
                    return s;
                if (n === !0)
                    return vb(s);
                if (E.isFunction(n))
                    return n.call(this, s, r);
                if (E.isRegExp(n))
                    return n.exec(s);
                throw new TypeError("parser must be boolean|regexp|function")
            }
        }
    }
    has(t, n) {
        if (t = Fn(t),
        t) {
            const r = E.findKey(this, t);
            return !!(r && this[r] !== void 0 && (!n || Xs(this, this[r], r, n)))
        }
        return !1
    }
    delete(t, n) {
        const r = this;
        let s = !1;
        function o(i) {
            if (i = Fn(i),
            i) {
                const l = E.findKey(r, i);
                l && (!n || Xs(r, r[l], l, n)) && (delete r[l],
                s = !0)
            }
        }
        return E.isArray(t) ? t.forEach(o) : o(t),
        s
    }
    clear(t) {
        const n = Object.keys(this);
        let r = n.length
          , s = !1;
        for (; r--; ) {
            const o = n[r];
            (!t || Xs(this, this[o], o, t, !0)) && (delete this[o],
            s = !0)
        }
        return s
    }
    normalize(t) {
        const n = this
          , r = {};
        return E.forEach(this, (s, o) => {
            const i = E.findKey(r, o);
            if (i) {
                n[i] = Lr(s),
                delete n[o];
                return
            }
            const l = t ? Sb(o) : String(o).trim();
            l !== o && delete n[o],
            n[l] = Lr(s),
            r[l] = !0
        }
        ),
        this
    }
    concat(...t) {
        return this.constructor.concat(this, ...t)
    }
    toJSON(t) {
        const n = Object.create(null);
        return E.forEach(this, (r, s) => {
            r != null && r !== !1 && (n[s] = t && E.isArray(r) ? r.join(", ") : r)
        }
        ),
        n
    }
    [Symbol.iterator]() {
        return Object.entries(this.toJSON())[Symbol.iterator]()
    }
    toString() {
        return Object.entries(this.toJSON()).map( ([t,n]) => t + ": " + n).join(`
`)
    }
    get[Symbol.toStringTag]() {
        return "AxiosHeaders"
    }
    static from(t) {
        return t instanceof this ? t : new this(t)
    }
    static concat(t, ...n) {
        const r = new this(t);
        return n.forEach(s => r.set(s)),
        r
    }
    static accessor(t) {
        const r = (this[Ul] = this[Ul] = {
            accessors: {}
        }).accessors
          , s = this.prototype;
        function o(i) {
            const l = Fn(i);
            r[l] || (_b(s, i),
            r[l] = !0)
        }
        return E.isArray(t) ? t.forEach(o) : o(t),
        this
    }
}
Ze.accessor(["Content-Type", "Content-Length", "Accept", "Accept-Encoding", "User-Agent", "Authorization"]);
E.reduceDescriptors(Ze.prototype, ({value: e}, t) => {
    let n = t[0].toUpperCase() + t.slice(1);
    return {
        get: () => e,
        set(r) {
            this[n] = r
        }
    }
}
);
E.freezeMethods(Ze);
function Zs(e, t) {
    const n = this || vr
      , r = t || n
      , s = Ze.from(r.headers);
    let o = r.data;
    return E.forEach(e, function(l) {
        o = l.call(n, o, s.normalize(), t ? t.status : void 0)
    }),
    s.normalize(),
    o
}
function Ru(e) {
    return !!(e && e.__CANCEL__)
}
function Pn(e, t, n) {
    oe.call(this, e ?? "canceled", oe.ERR_CANCELED, t, n),
    this.name = "CanceledError"
}
E.inherits(Pn, oe, {
    __CANCEL__: !0
});
function Au(e, t, n) {
    const r = n.config.validateStatus;
    !n.status || !r || r(n.status) ? e(n) : t(new oe("Request failed with status code " + n.status,[oe.ERR_BAD_REQUEST, oe.ERR_BAD_RESPONSE][Math.floor(n.status / 100) - 4],n.config,n.request,n))
}
function Eb(e) {
    const t = /^([-+\w]{1,25})(:?\/\/|:)/.exec(e);
    return t && t[1] || ""
}
function xb(e, t) {
    e = e || 10;
    const n = new Array(e)
      , r = new Array(e);
    let s = 0, o = 0, i;
    return t = t !== void 0 ? t : 1e3,
    function(c) {
        const u = Date.now()
          , a = r[o];
        i || (i = u),
        n[s] = c,
        r[s] = u;
        let f = o
          , d = 0;
        for (; f !== s; )
            d += n[f++],
            f = f % e;
        if (s = (s + 1) % e,
        s === o && (o = (o + 1) % e),
        u - i < t)
            return;
        const g = a && u - a;
        return g ? Math.round(d * 1e3 / g) : void 0
    }
}
function Cb(e, t) {
    let n = 0, r = 1e3 / t, s, o;
    const i = (u, a=Date.now()) => {
        n = a,
        s = null,
        o && (clearTimeout(o),
        o = null),
        e.apply(null, u)
    }
    ;
    return [ (...u) => {
        const a = Date.now()
          , f = a - n;
        f >= r ? i(u, a) : (s = u,
        o || (o = setTimeout( () => {
            o = null,
            i(s)
        }
        , r - f)))
    }
    , () => s && i(s)]
}
const Wr = (e, t, n=3) => {
    let r = 0;
    const s = xb(50, 250);
    return Cb(o => {
        const i = o.loaded
          , l = o.lengthComputable ? o.total : void 0
          , c = i - r
          , u = s(c)
          , a = i <= l;
        r = i;
        const f = {
            loaded: i,
            total: l,
            progress: l ? i / l : void 0,
            bytes: c,
            rate: u || void 0,
            estimated: u && l && a ? (l - i) / u : void 0,
            event: o,
            lengthComputable: l != null,
            [t ? "download" : "upload"]: !0
        };
        e(f)
    }
    , n)
}
  , Vl = (e, t) => {
    const n = e != null;
    return [r => t[0]({
        lengthComputable: n,
        total: e,
        loaded: r
    }), t[1]]
}
  , zl = e => (...t) => E.asap( () => e(...t))
  , Tb = Me.hasStandardBrowserEnv ? ( (e, t) => n => (n = new URL(n,Me.origin),
e.protocol === n.protocol && e.host === n.host && (t || e.port === n.port)))(new URL(Me.origin), Me.navigator && /(msie|trident)/i.test(Me.navigator.userAgent)) : () => !0
  , Rb = Me.hasStandardBrowserEnv ? {
    write(e, t, n, r, s, o) {
        const i = [e + "=" + encodeURIComponent(t)];
        E.isNumber(n) && i.push("expires=" + new Date(n).toGMTString()),
        E.isString(r) && i.push("path=" + r),
        E.isString(s) && i.push("domain=" + s),
        o === !0 && i.push("secure"),
        document.cookie = i.join("; ")
    },
    read(e) {
        const t = document.cookie.match(new RegExp("(^|;\\s*)(" + e + ")=([^;]*)"));
        return t ? decodeURIComponent(t[3]) : null
    },
    remove(e) {
        this.write(e, "", Date.now() - 864e5)
    }
} : {
    write() {},
    read() {
        return null
    },
    remove() {}
};
function Ab(e) {
    return /^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)
}
function Ob(e, t) {
    return t ? e.replace(/\/?\/$/, "") + "/" + t.replace(/^\/+/, "") : e
}
function Ou(e, t) {
    return e && !Ab(t) ? Ob(e, t) : t
}
const ql = e => e instanceof Ze ? {
    ...e
} : e;
function hn(e, t) {
    t = t || {};
    const n = {};
    function r(u, a, f, d) {
        return E.isPlainObject(u) && E.isPlainObject(a) ? E.merge.call({
            caseless: d
        }, u, a) : E.isPlainObject(a) ? E.merge({}, a) : E.isArray(a) ? a.slice() : a
    }
    function s(u, a, f, d) {
        if (E.isUndefined(a)) {
            if (!E.isUndefined(u))
                return r(void 0, u, f, d)
        } else
            return r(u, a, f, d)
    }
    function o(u, a) {
        if (!E.isUndefined(a))
            return r(void 0, a)
    }
    function i(u, a) {
        if (E.isUndefined(a)) {
            if (!E.isUndefined(u))
                return r(void 0, u)
        } else
            return r(void 0, a)
    }
    function l(u, a, f) {
        if (f in t)
            return r(u, a);
        if (f in e)
            return r(void 0, u)
    }
    const c = {
        url: o,
        method: o,
        data: o,
        baseURL: i,
        transformRequest: i,
        transformResponse: i,
        paramsSerializer: i,
        timeout: i,
        timeoutMessage: i,
        withCredentials: i,
        withXSRFToken: i,
        adapter: i,
        responseType: i,
        xsrfCookieName: i,
        xsrfHeaderName: i,
        onUploadProgress: i,
        onDownloadProgress: i,
        decompress: i,
        maxContentLength: i,
        maxBodyLength: i,
        beforeRedirect: i,
        transport: i,
        httpAgent: i,
        httpsAgent: i,
        cancelToken: i,
        socketPath: i,
        responseEncoding: i,
        validateStatus: l,
        headers: (u, a, f) => s(ql(u), ql(a), f, !0)
    };
    return E.forEach(Object.keys(Object.assign({}, e, t)), function(a) {
        const f = c[a] || s
          , d = f(e[a], t[a], a);
        E.isUndefined(d) && f !== l || (n[a] = d)
    }),
    n
}
const Pu = e => {
    const t = hn({}, e);
    let {data: n, withXSRFToken: r, xsrfHeaderName: s, xsrfCookieName: o, headers: i, auth: l} = t;
    t.headers = i = Ze.from(i),
    t.url = xu(Ou(t.baseURL, t.url), e.params, e.paramsSerializer),
    l && i.set("Authorization", "Basic " + btoa((l.username || "") + ":" + (l.password ? unescape(encodeURIComponent(l.password)) : "")));
    let c;
    if (E.isFormData(n)) {
        if (Me.hasStandardBrowserEnv || Me.hasStandardBrowserWebWorkerEnv)
            i.setContentType(void 0);
        else if ((c = i.getContentType()) !== !1) {
            const [u,...a] = c ? c.split(";").map(f => f.trim()).filter(Boolean) : [];
            i.setContentType([u || "multipart/form-data", ...a].join("; "))
        }
    }
    if (Me.hasStandardBrowserEnv && (r && E.isFunction(r) && (r = r(t)),
    r || r !== !1 && Tb(t.url))) {
        const u = s && o && Rb.read(o);
        u && i.set(s, u)
    }
    return t
}
  , Pb = typeof XMLHttpRequest < "u"
  , Ib = Pb && function(e) {
    return new Promise(function(n, r) {
        const s = Pu(e);
        let o = s.data;
        const i = Ze.from(s.headers).normalize();
        let {responseType: l, onUploadProgress: c, onDownloadProgress: u} = s, a, f, d, g, y;
        function b() {
            g && g(),
            y && y(),
            s.cancelToken && s.cancelToken.unsubscribe(a),
            s.signal && s.signal.removeEventListener("abort", a)
        }
        let w = new XMLHttpRequest;
        w.open(s.method.toUpperCase(), s.url, !0),
        w.timeout = s.timeout;
        function _() {
            if (!w)
                return;
            const I = Ze.from("getAllResponseHeaders"in w && w.getAllResponseHeaders())
              , k = {
                data: !l || l === "text" || l === "json" ? w.responseText : w.response,
                status: w.status,
                statusText: w.statusText,
                headers: I,
                config: e,
                request: w
            };
            Au(function(V) {
                n(V),
                b()
            }, function(V) {
                r(V),
                b()
            }, k),
            w = null
        }
        "onloadend"in w ? w.onloadend = _ : w.onreadystatechange = function() {
            !w || w.readyState !== 4 || w.status === 0 && !(w.responseURL && w.responseURL.indexOf("file:") === 0) || setTimeout(_)
        }
        ,
        w.onabort = function() {
            w && (r(new oe("Request aborted",oe.ECONNABORTED,e,w)),
            w = null)
        }
        ,
        w.onerror = function() {
            r(new oe("Network Error",oe.ERR_NETWORK,e,w)),
            w = null
        }
        ,
        w.ontimeout = function() {
            let O = s.timeout ? "timeout of " + s.timeout + "ms exceeded" : "timeout exceeded";
            const k = s.transitional || Cu;
            s.timeoutErrorMessage && (O = s.timeoutErrorMessage),
            r(new oe(O,k.clarifyTimeoutError ? oe.ETIMEDOUT : oe.ECONNABORTED,e,w)),
            w = null
        }
        ,
        o === void 0 && i.setContentType(null),
        "setRequestHeader"in w && E.forEach(i.toJSON(), function(O, k) {
            w.setRequestHeader(k, O)
        }),
        E.isUndefined(s.withCredentials) || (w.withCredentials = !!s.withCredentials),
        l && l !== "json" && (w.responseType = s.responseType),
        u && ([d,y] = Wr(u, !0),
        w.addEventListener("progress", d)),
        c && w.upload && ([f,g] = Wr(c),
        w.upload.addEventListener("progress", f),
        w.upload.addEventListener("loadend", g)),
        (s.cancelToken || s.signal) && (a = I => {
            w && (r(!I || I.type ? new Pn(null,e,w) : I),
            w.abort(),
            w = null)
        }
        ,
        s.cancelToken && s.cancelToken.subscribe(a),
        s.signal && (s.signal.aborted ? a() : s.signal.addEventListener("abort", a)));
        const S = Eb(s.url);
        if (S && Me.protocols.indexOf(S) === -1) {
            r(new oe("Unsupported protocol " + S + ":",oe.ERR_BAD_REQUEST,e));
            return
        }
        w.send(o || null)
    }
    )
}
  , Bb = (e, t) => {
    const {length: n} = e = e ? e.filter(Boolean) : [];
    if (t || n) {
        let r = new AbortController, s;
        const o = function(u) {
            if (!s) {
                s = !0,
                l();
                const a = u instanceof Error ? u : this.reason;
                r.abort(a instanceof oe ? a : new Pn(a instanceof Error ? a.message : a))
            }
        };
        let i = t && setTimeout( () => {
            i = null,
            o(new oe(`timeout ${t} of ms exceeded`,oe.ETIMEDOUT))
        }
        , t);
        const l = () => {
            e && (i && clearTimeout(i),
            i = null,
            e.forEach(u => {
                u.unsubscribe ? u.unsubscribe(o) : u.removeEventListener("abort", o)
            }
            ),
            e = null)
        }
        ;
        e.forEach(u => u.addEventListener("abort", o));
        const {signal: c} = r;
        return c.unsubscribe = () => E.asap(l),
        c
    }
}
  , kb = function*(e, t) {
    let n = e.byteLength;
    if (n < t) {
        yield e;
        return
    }
    let r = 0, s;
    for (; r < n; )
        s = r + t,
        yield e.slice(r, s),
        r = s
}
  , Nb = async function*(e, t) {
    for await(const n of Lb(e))
        yield*kb(n, t)
}
  , Lb = async function*(e) {
    if (e[Symbol.asyncIterator]) {
        yield*e;
        return
    }
    const t = e.getReader();
    try {
        for (; ; ) {
            const {done: n, value: r} = await t.read();
            if (n)
                break;
            yield r
        }
    } finally {
        await t.cancel()
    }
}
  , Kl = (e, t, n, r) => {
    const s = Nb(e, t);
    let o = 0, i, l = c => {
        i || (i = !0,
        r && r(c))
    }
    ;
    return new ReadableStream({
        async pull(c) {
            try {
                const {done: u, value: a} = await s.next();
                if (u) {
                    l(),
                    c.close();
                    return
                }
                let f = a.byteLength;
                if (n) {
                    let d = o += f;
                    n(d)
                }
                c.enqueue(new Uint8Array(a))
            } catch (u) {
                throw l(u),
                u
            }
        },
        cancel(c) {
            return l(c),
            s.return()
        }
    },{
        highWaterMark: 2
    })
}
  , xs = typeof fetch == "function" && typeof Request == "function" && typeof Response == "function"
  , Iu = xs && typeof ReadableStream == "function"
  , $b = xs && (typeof TextEncoder == "function" ? (e => t => e.encode(t))(new TextEncoder) : async e => new Uint8Array(await new Response(e).arrayBuffer()))
  , Bu = (e, ...t) => {
    try {
        return !!e(...t)
    } catch {
        return !1
    }
}
  , Fb = Iu && Bu( () => {
    let e = !1;
    const t = new Request(Me.origin,{
        body: new ReadableStream,
        method: "POST",
        get duplex() {
            return e = !0,
            "half"
        }
    }).headers.has("Content-Type");
    return e && !t
}
)
  , Wl = 64 * 1024
  , Co = Iu && Bu( () => E.isReadableStream(new Response("").body))
  , Jr = {
    stream: Co && (e => e.body)
};
xs && (e => {
    ["text", "arrayBuffer", "blob", "formData", "stream"].forEach(t => {
        !Jr[t] && (Jr[t] = E.isFunction(e[t]) ? n => n[t]() : (n, r) => {
            throw new oe(`Response type '${t}' is not supported`,oe.ERR_NOT_SUPPORT,r)
        }
        )
    }
    )
}
)(new Response);
const Db = async e => {
    if (e == null)
        return 0;
    if (E.isBlob(e))
        return e.size;
    if (E.isSpecCompliantForm(e))
        return (await new Request(Me.origin,{
            method: "POST",
            body: e
        }).arrayBuffer()).byteLength;
    if (E.isArrayBufferView(e) || E.isArrayBuffer(e))
        return e.byteLength;
    if (E.isURLSearchParams(e) && (e = e + ""),
    E.isString(e))
        return (await $b(e)).byteLength
}
  , Mb = async (e, t) => {
    const n = E.toFiniteNumber(e.getContentLength());
    return n ?? Db(t)
}
  , jb = xs && (async e => {
    let {url: t, method: n, data: r, signal: s, cancelToken: o, timeout: i, onDownloadProgress: l, onUploadProgress: c, responseType: u, headers: a, withCredentials: f="same-origin", fetchOptions: d} = Pu(e);
    u = u ? (u + "").toLowerCase() : "text";
    let g = Bb([s, o && o.toAbortSignal()], i), y;
    const b = g && g.unsubscribe && ( () => {
        g.unsubscribe()
    }
    );
    let w;
    try {
        if (c && Fb && n !== "get" && n !== "head" && (w = await Mb(a, r)) !== 0) {
            let k = new Request(t,{
                method: "POST",
                body: r,
                duplex: "half"
            }), F;
            if (E.isFormData(r) && (F = k.headers.get("content-type")) && a.setContentType(F),
            k.body) {
                const [V,U] = Vl(w, Wr(zl(c)));
                r = Kl(k.body, Wl, V, U)
            }
        }
        E.isString(f) || (f = f ? "include" : "omit");
        const _ = "credentials"in Request.prototype;
        y = new Request(t,{
            ...d,
            signal: g,
            method: n.toUpperCase(),
            headers: a.normalize().toJSON(),
            body: r,
            duplex: "half",
            credentials: _ ? f : void 0
        });
        let S = await fetch(y);
        const I = Co && (u === "stream" || u === "response");
        if (Co && (l || I && b)) {
            const k = {};
            ["status", "statusText", "headers"].forEach(B => {
                k[B] = S[B]
            }
            );
            const F = E.toFiniteNumber(S.headers.get("content-length"))
              , [V,U] = l && Vl(F, Wr(zl(l), !0)) || [];
            S = new Response(Kl(S.body, Wl, V, () => {
                U && U(),
                b && b()
            }
            ),k)
        }
        u = u || "text";
        let O = await Jr[E.findKey(Jr, u) || "text"](S, e);
        return !I && b && b(),
        await new Promise( (k, F) => {
            Au(k, F, {
                data: O,
                headers: Ze.from(S.headers),
                status: S.status,
                statusText: S.statusText,
                config: e,
                request: y
            })
        }
        )
    } catch (_) {
        throw b && b(),
        _ && _.name === "TypeError" && /fetch/i.test(_.message) ? Object.assign(new oe("Network Error",oe.ERR_NETWORK,e,y), {
            cause: _.cause || _
        }) : oe.from(_, _ && _.code, e, y)
    }
}
)
  , To = {
    http: tb,
    xhr: Ib,
    fetch: jb
};
E.forEach(To, (e, t) => {
    if (e) {
        try {
            Object.defineProperty(e, "name", {
                value: t
            })
        } catch {}
        Object.defineProperty(e, "adapterName", {
            value: t
        })
    }
}
);
const Jl = e => `- ${e}`
  , Hb = e => E.isFunction(e) || e === null || e === !1
  , ku = {
    getAdapter: e => {
        e = E.isArray(e) ? e : [e];
        const {length: t} = e;
        let n, r;
        const s = {};
        for (let o = 0; o < t; o++) {
            n = e[o];
            let i;
            if (r = n,
            !Hb(n) && (r = To[(i = String(n)).toLowerCase()],
            r === void 0))
                throw new oe(`Unknown adapter '${i}'`);
            if (r)
                break;
            s[i || "#" + o] = r
        }
        if (!r) {
            const o = Object.entries(s).map( ([l,c]) => `adapter ${l} ` + (c === !1 ? "is not supported by the environment" : "is not available in the build"));
            let i = t ? o.length > 1 ? `since :
` + o.map(Jl).join(`
`) : " " + Jl(o[0]) : "as no adapter specified";
            throw new oe("There is no suitable adapter to dispatch the request " + i,"ERR_NOT_SUPPORT")
        }
        return r
    }
    ,
    adapters: To
};
function Qs(e) {
    if (e.cancelToken && e.cancelToken.throwIfRequested(),
    e.signal && e.signal.aborted)
        throw new Pn(null,e)
}
function Yl(e) {
    return Qs(e),
    e.headers = Ze.from(e.headers),
    e.data = Zs.call(e, e.transformRequest),
    ["post", "put", "patch"].indexOf(e.method) !== -1 && e.headers.setContentType("application/x-www-form-urlencoded", !1),
    ku.getAdapter(e.adapter || vr.adapter)(e).then(function(r) {
        return Qs(e),
        r.data = Zs.call(e, e.transformResponse, r),
        r.headers = Ze.from(r.headers),
        r
    }, function(r) {
        return Ru(r) || (Qs(e),
        r && r.response && (r.response.data = Zs.call(e, e.transformResponse, r.response),
        r.response.headers = Ze.from(r.response.headers))),
        Promise.reject(r)
    })
}
const Nu = "1.7.9"
  , Cs = {};
["object", "boolean", "number", "function", "string", "symbol"].forEach( (e, t) => {
    Cs[e] = function(r) {
        return typeof r === e || "a" + (t < 1 ? "n " : " ") + e
    }
}
);
const Gl = {};
Cs.transitional = function(t, n, r) {
    function s(o, i) {
        return "[Axios v" + Nu + "] Transitional option '" + o + "'" + i + (r ? ". " + r : "")
    }
    return (o, i, l) => {
        if (t === !1)
            throw new oe(s(i, " has been removed" + (n ? " in " + n : "")),oe.ERR_DEPRECATED);
        return n && !Gl[i] && (Gl[i] = !0,
        console.warn(s(i, " has been deprecated since v" + n + " and will be removed in the near future"))),
        t ? t(o, i, l) : !0
    }
}
;
Cs.spelling = function(t) {
    return (n, r) => (console.warn(`${r} is likely a misspelling of ${t}`),
    !0)
}
;
function Ub(e, t, n) {
    if (typeof e != "object")
        throw new oe("options must be an object",oe.ERR_BAD_OPTION_VALUE);
    const r = Object.keys(e);
    let s = r.length;
    for (; s-- > 0; ) {
        const o = r[s]
          , i = t[o];
        if (i) {
            const l = e[o]
              , c = l === void 0 || i(l, o, e);
            if (c !== !0)
                throw new oe("option " + o + " must be " + c,oe.ERR_BAD_OPTION_VALUE);
            continue
        }
        if (n !== !0)
            throw new oe("Unknown option " + o,oe.ERR_BAD_OPTION)
    }
}
const $r = {
    assertOptions: Ub,
    validators: Cs
}
  , vt = $r.validators;
class fn {
    constructor(t) {
        this.defaults = t,
        this.interceptors = {
            request: new Hl,
            response: new Hl
        }
    }
    async request(t, n) {
        try {
            return await this._request(t, n)
        } catch (r) {
            if (r instanceof Error) {
                let s = {};
                Error.captureStackTrace ? Error.captureStackTrace(s) : s = new Error;
                const o = s.stack ? s.stack.replace(/^.+\n/, "") : "";
                try {
                    r.stack ? o && !String(r.stack).endsWith(o.replace(/^.+\n.+\n/, "")) && (r.stack += `
` + o) : r.stack = o
                } catch {}
            }
            throw r
        }
    }
    _request(t, n) {
        typeof t == "string" ? (n = n || {},
        n.url = t) : n = t || {},
        n = hn(this.defaults, n);
        const {transitional: r, paramsSerializer: s, headers: o} = n;
        r !== void 0 && $r.assertOptions(r, {
            silentJSONParsing: vt.transitional(vt.boolean),
            forcedJSONParsing: vt.transitional(vt.boolean),
            clarifyTimeoutError: vt.transitional(vt.boolean)
        }, !1),
        s != null && (E.isFunction(s) ? n.paramsSerializer = {
            serialize: s
        } : $r.assertOptions(s, {
            encode: vt.function,
            serialize: vt.function
        }, !0)),
        $r.assertOptions(n, {
            baseUrl: vt.spelling("baseURL"),
            withXsrfToken: vt.spelling("withXSRFToken")
        }, !0),
        n.method = (n.method || this.defaults.method || "get").toLowerCase();
        let i = o && E.merge(o.common, o[n.method]);
        o && E.forEach(["delete", "get", "head", "post", "put", "patch", "common"], y => {
            delete o[y]
        }
        ),
        n.headers = Ze.concat(i, o);
        const l = [];
        let c = !0;
        this.interceptors.request.forEach(function(b) {
            typeof b.runWhen == "function" && b.runWhen(n) === !1 || (c = c && b.synchronous,
            l.unshift(b.fulfilled, b.rejected))
        });
        const u = [];
        this.interceptors.response.forEach(function(b) {
            u.push(b.fulfilled, b.rejected)
        });
        let a, f = 0, d;
        if (!c) {
            const y = [Yl.bind(this), void 0];
            for (y.unshift.apply(y, l),
            y.push.apply(y, u),
            d = y.length,
            a = Promise.resolve(n); f < d; )
                a = a.then(y[f++], y[f++]);
            return a
        }
        d = l.length;
        let g = n;
        for (f = 0; f < d; ) {
            const y = l[f++]
              , b = l[f++];
            try {
                g = y(g)
            } catch (w) {
                b.call(this, w);
                break
            }
        }
        try {
            a = Yl.call(this, g)
        } catch (y) {
            return Promise.reject(y)
        }
        for (f = 0,
        d = u.length; f < d; )
            a = a.then(u[f++], u[f++]);
        return a
    }
    getUri(t) {
        t = hn(this.defaults, t);
        const n = Ou(t.baseURL, t.url);
        return xu(n, t.params, t.paramsSerializer)
    }
}
E.forEach(["delete", "get", "head", "options"], function(t) {
    fn.prototype[t] = function(n, r) {
        return this.request(hn(r || {}, {
            method: t,
            url: n,
            data: (r || {}).data
        }))
    }
});
E.forEach(["post", "put", "patch"], function(t) {
    function n(r) {
        return function(o, i, l) {
            return this.request(hn(l || {}, {
                method: t,
                headers: r ? {
                    "Content-Type": "multipart/form-data"
                } : {},
                url: o,
                data: i
            }))
        }
    }
    fn.prototype[t] = n(),
    fn.prototype[t + "Form"] = n(!0)
});
class li {
    constructor(t) {
        if (typeof t != "function")
            throw new TypeError("executor must be a function.");
        let n;
        this.promise = new Promise(function(o) {
            n = o
        }
        );
        const r = this;
        this.promise.then(s => {
            if (!r._listeners)
                return;
            let o = r._listeners.length;
            for (; o-- > 0; )
                r._listeners[o](s);
            r._listeners = null
        }
        ),
        this.promise.then = s => {
            let o;
            const i = new Promise(l => {
                r.subscribe(l),
                o = l
            }
            ).then(s);
            return i.cancel = function() {
                r.unsubscribe(o)
            }
            ,
            i
        }
        ,
        t(function(o, i, l) {
            r.reason || (r.reason = new Pn(o,i,l),
            n(r.reason))
        })
    }
    throwIfRequested() {
        if (this.reason)
            throw this.reason
    }
    subscribe(t) {
        if (this.reason) {
            t(this.reason);
            return
        }
        this._listeners ? this._listeners.push(t) : this._listeners = [t]
    }
    unsubscribe(t) {
        if (!this._listeners)
            return;
        const n = this._listeners.indexOf(t);
        n !== -1 && this._listeners.splice(n, 1)
    }
    toAbortSignal() {
        const t = new AbortController
          , n = r => {
            t.abort(r)
        }
        ;
        return this.subscribe(n),
        t.signal.unsubscribe = () => this.unsubscribe(n),
        t.signal
    }
    static source() {
        let t;
        return {
            token: new li(function(s) {
                t = s
            }
            ),
            cancel: t
        }
    }
}
function Vb(e) {
    return function(n) {
        return e.apply(null, n)
    }
}
function zb(e) {
    return E.isObject(e) && e.isAxiosError === !0
}
const Ro = {
    Continue: 100,
    SwitchingProtocols: 101,
    Processing: 102,
    EarlyHints: 103,
    Ok: 200,
    Created: 201,
    Accepted: 202,
    NonAuthoritativeInformation: 203,
    NoContent: 204,
    ResetContent: 205,
    PartialContent: 206,
    MultiStatus: 207,
    AlreadyReported: 208,
    ImUsed: 226,
    MultipleChoices: 300,
    MovedPermanently: 301,
    Found: 302,
    SeeOther: 303,
    NotModified: 304,
    UseProxy: 305,
    Unused: 306,
    TemporaryRedirect: 307,
    PermanentRedirect: 308,
    BadRequest: 400,
    Unauthorized: 401,
    PaymentRequired: 402,
    Forbidden: 403,
    NotFound: 404,
    MethodNotAllowed: 405,
    NotAcceptable: 406,
    ProxyAuthenticationRequired: 407,
    RequestTimeout: 408,
    Conflict: 409,
    Gone: 410,
    LengthRequired: 411,
    PreconditionFailed: 412,
    PayloadTooLarge: 413,
    UriTooLong: 414,
    UnsupportedMediaType: 415,
    RangeNotSatisfiable: 416,
    ExpectationFailed: 417,
    ImATeapot: 418,
    MisdirectedRequest: 421,
    UnprocessableEntity: 422,
    Locked: 423,
    FailedDependency: 424,
    TooEarly: 425,
    UpgradeRequired: 426,
    PreconditionRequired: 428,
    TooManyRequests: 429,
    RequestHeaderFieldsTooLarge: 431,
    UnavailableForLegalReasons: 451,
    InternalServerError: 500,
    NotImplemented: 501,
    BadGateway: 502,
    ServiceUnavailable: 503,
    GatewayTimeout: 504,
    HttpVersionNotSupported: 505,
    VariantAlsoNegotiates: 506,
    InsufficientStorage: 507,
    LoopDetected: 508,
    NotExtended: 510,
    NetworkAuthenticationRequired: 511
};
Object.entries(Ro).forEach( ([e,t]) => {
    Ro[t] = e
}
);
function Lu(e) {
    const t = new fn(e)
      , n = du(fn.prototype.request, t);
    return E.extend(n, fn.prototype, t, {
        allOwnKeys: !0
    }),
    E.extend(n, t, null, {
        allOwnKeys: !0
    }),
    n.create = function(s) {
        return Lu(hn(e, s))
    }
    ,
    n
}
const Ie = Lu(vr);
Ie.Axios = fn;
Ie.CanceledError = Pn;
Ie.CancelToken = li;
Ie.isCancel = Ru;
Ie.VERSION = Nu;
Ie.toFormData = Es;
Ie.AxiosError = oe;
Ie.Cancel = Ie.CanceledError;
Ie.all = function(t) {
    return Promise.all(t)
}
;
Ie.spread = Vb;
Ie.isAxiosError = zb;
Ie.mergeConfig = hn;
Ie.AxiosHeaders = Ze;
Ie.formToJSON = e => Tu(E.isHTMLForm(e) ? new FormData(e) : e);
Ie.getAdapter = ku.getAdapter;
Ie.HttpStatusCode = Ro;
Ie.default = Ie;
const qb = "https://itstudio.henau.edu.cn/klotskiapi"
  , Ts = Ie.create({
    baseURL: qb,
    timeout: 5e4
});
Ts.interceptors.request.use(e => {
    const t = Wb();
    return t.token && (e.headers.Authorization = t.token),
    e
}
, e => Promise.reject(e));
Ts.interceptors.response.use(e => e.data.code === 200 ? e.data : (e.data.code === 401 && vo.push("/login"),
pl(e.data.message || "服务异常"),
Promise.reject(e.data)), e => {
    var t, n, r;
    return pl(((n = (t = e.response) == null ? void 0 : t.data) == null ? void 0 : n.message) || "服务异常"),
    ((r = e.response) == null ? void 0 : r.status) === 401 && vo.push("/login"),
    Promise.reject(e)
}
);
const ev = e => Ts("/login", {
    method: "get",
    params: e
})
  , Kb = () => Ts("/userInfo", {
    method: "get"
})
  , Wb = oy("user", () => {
    const e = se("")
      , t = i => {
        e.value = i
    }
      , n = () => {
        e.value = ""
    }
      , r = se("");
    return {
        token: e,
        userInfo: t,
        removeToken: n,
        user: r,
        userSelf: async () => {
            const i = await Kb();
            r.value = i.data
        }
        ,
        removeUser: () => {
            r.value = ""
        }
    }
}
, {
    persist: !0
})
  , $u = Qm();
$u.use(gy);
const lt = wa(Wg);
lt.use(vo);
lt.use($u);
lt.use(Kr);
lt.use(Vg);
lt.use(Ag);
lt.use(kg);
lt.use(bg);
lt.use(ei);
lt.use(rg);
lt.use(sg);
lt.use(Jt);
lt.mount("#app");
export {Gb as A, da as B, Xb as C, Zu as D, kf as E, ts as F, at as G, Yb as H, Jt as I, Kr as J, kg as K, dp as L, ca as M, ha as N, ns as O, ei as P, sg as Q, rg as T, zg as _, Qb as a, ls as b, Oe as c, Ce as d, P as e, _e as f, Jb as g, kt as h, Be as i, We as j, Yr as k, ev as l, xt as m, pe as n, Ct as o, Fr as p, fr as q, wg as r, se as s, vg as t, Wb as u, Qt as v, we as w, ke as x, Ts as y, G as z};
