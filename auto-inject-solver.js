// ==================== 一键注入华容道自动求解器 ====================
// 使用方法：复制这段代码到控制台执行，会出现"一键求解"按钮
// 点击按钮后自动开始游戏并求解

(function() {
    'use strict';

    // 防止重复注入
    if (window.autoSolverInjected) {
        console.log('🔄 求解器已存在，重新激活...');
        const existingBtn = document.getElementById('auto-solver-btn');
        if (existingBtn) existingBtn.style.display = 'block';
        return;
    }
    window.autoSolverInjected = true;

    console.log('🚀 华容道一键求解器注入成功！');

    // ==================== 超快速求解器核心 ====================
    function FastPuzzleSolver(gridSize) {
        const N = gridSize;
        const totalTiles = N * N;
        
        // 目标状态：1,2,3,...,N²-1,0
        const targetState = new Array(totalTiles);
        for (let i = 0; i < totalTiles - 1; i++) {
            targetState[i] = i + 1;
        }
        targetState[totalTiles - 1] = 0;
        const targetKey = targetState.join(',');

        // 获取空格位置的所有可能移动
        function getMoves(state) {
            const emptyIndex = state.indexOf(0);
            const moves = [];
            const row = Math.floor(emptyIndex / N);
            const col = emptyIndex % N;

            function makeMove(fromIdx, toIdx) {
                const newState = state.slice();
                newState[fromIdx] = newState[toIdx];
                newState[toIdx] = 0;
                return newState;
            }

            // 四个方向的移动
            if (row > 0) moves.push(makeMove(emptyIndex, emptyIndex - N));     // 上
            if (col < N - 1) moves.push(makeMove(emptyIndex, emptyIndex + 1)); // 右
            if (row < N - 1) moves.push(makeMove(emptyIndex, emptyIndex + N)); // 下
            if (col > 0) moves.push(makeMove(emptyIndex, emptyIndex - 1));     // 左
            
            return moves;
        }

        // 超快速BFS求解
        this.solve = function(initialState) {
            const startTime = performance.now();
            const initialKey = initialState.join(',');
            
            if (initialKey === targetKey) {
                return [initialState];
            }
            
            const queue = [initialState];
            const visited = new Set([initialKey]);
            const parent = new Map();
            parent.set(initialKey, null);
            let nodesExpanded = 0;
            
            while (queue.length > 0) {
                const currentState = queue.shift();
                const currentKey = currentState.join(',');
                nodesExpanded++;
                
                if (nodesExpanded % 10000 === 0) {
                    const elapsed = performance.now() - startTime;
                    if (elapsed > 30000) {
                        throw new Error(`求解超时，建议重新开始`);
                    }
                }
                
                const possibleMoves = getMoves(currentState);
                for (const move of possibleMoves) {
                    const moveKey = move.join(',');
                    if (!visited.has(moveKey)) {
                        visited.add(moveKey);
                        parent.set(moveKey, currentKey);
                        
                        if (moveKey === targetKey) {
                            // 重建路径
                            const path = [];
                            let current = moveKey;
                            while (current !== null) {
                                const state = current.split(',').map(Number);
                                path.unshift(state);
                                current = parent.get(current);
                            }
                            
                            const elapsed = performance.now() - startTime;
                            console.log(`🎉 求解完成！步数: ${path.length - 1}, 用时: ${elapsed.toFixed(1)}ms`);
                            return path;
                        }
                        
                        queue.push(move);
                    }
                }
            }
            
            throw new Error('无解');
        };
    }

    // ==================== 自动游戏控制 ====================
    function startNewGame() {
        return new Promise((resolve) => {
            console.log('🎮 正在开始新游戏...');
            
            // 查找开始游戏按钮
            const startButtons = [
                ...document.querySelectorAll('button'),
                ...document.querySelectorAll('.btn'),
                ...document.querySelectorAll('[class*="start"]'),
                ...document.querySelectorAll('[class*="begin"]')
            ];
            
            let gameStarted = false;
            for (const btn of startButtons) {
                const text = btn.textContent.trim();
                if (text.includes('开始') || text.includes('start') || text.includes('Begin')) {
                    btn.click();
                    gameStarted = true;
                    console.log('✅ 已点击开始游戏按钮');
                    break;
                }
            }
            
            if (!gameStarted) {
                console.log('⚠️ 未找到开始按钮，尝试直接求解当前状态');
            }
            
            // 立即继续，无延迟
            setTimeout(resolve, 100);
        });
    }

    function readGameState() {
        const cells = document.querySelectorAll('.grid-cell');
        if (cells.length === 0) {
            throw new Error('未找到游戏棋盘');
        }
        
        const totalCells = cells.length;
        const gridSize = Math.sqrt(totalCells);
        
        if (!Number.isInteger(gridSize) || gridSize < 3 || gridSize > 5) {
            throw new Error(`不支持的棋盘尺寸 (${totalCells}个格子)`);
        }

        const currentState = Array.from(cells).map(cell => {
            const value = cell.textContent.trim();
            return value === '' || cell.classList.contains('empty') ? 0 : parseInt(value);
        });

        return { gridSize, currentState, cells };
    }

    function executeSteps(solution, useDelay = false) {
        return new Promise((resolve) => {
            if (!solution || solution.length <= 1) {
                console.log('❌ 无需移动或无解');
                resolve();
                return;
            }

            console.log(`⚡ 瞬间执行 ${solution.length - 1} 步`);
            const allCells = document.querySelectorAll('.grid-cell');

            if (!useDelay) {
                // 无延迟模式：瞬间执行所有步骤
                for (let stepIndex = 0; stepIndex < solution.length - 1; stepIndex++) {
                    const currentGrid = solution[stepIndex];
                    const nextGrid = solution[stepIndex + 1];
                    const emptyIndexNext = nextGrid.indexOf(0);
                    const tileToClickValue = currentGrid[emptyIndexNext];

                    let clicked = false;
                    for (const cell of allCells) {
                        const cellValue = parseInt(cell.textContent.trim());
                        if (cellValue === tileToClickValue) {
                            cell.click();
                            clicked = true;
                            console.log(`⚡ 第 ${stepIndex + 1} 步：瞬间点击 ${tileToClickValue}`);
                            break;
                        }
                    }

                    if (!clicked) {
                        console.error(`❌ 找不到数字 ${tileToClickValue}`);
                        break;
                    }
                }
                console.log('⚡ 瞬间求解完成！');
                resolve();
            } else {
                // 有延迟模式：1ms间隔
                let stepIndex = 0;
                const intervalId = setInterval(() => {
                    if (stepIndex >= solution.length - 1) {
                        clearInterval(intervalId);
                        console.log('⚡ 快速求解完成！');
                        resolve();
                        return;
                    }

                    const currentGrid = solution[stepIndex];
                    const nextGrid = solution[stepIndex + 1];
                    const emptyIndexNext = nextGrid.indexOf(0);
                    const tileToClickValue = currentGrid[emptyIndexNext];

                    let clicked = false;
                    for (const cell of allCells) {
                        const cellValue = parseInt(cell.textContent.trim());
                        if (cellValue === tileToClickValue) {
                            cell.click();
                            clicked = true;
                            console.log(`⚡ 第 ${stepIndex + 1} 步：点击 ${tileToClickValue}`);
                            break;
                        }
                    }

                    if (!clicked) {
                        console.error(`❌ 找不到数字 ${tileToClickValue}`);
                        clearInterval(intervalId);
                        resolve();
                        return;
                    }

                    stepIndex++;
                }, 1);
            }
        });
    }

    // ==================== 主要执行函数 ====================
    async function autoSolveGame(useDelay = false) {
        try {
            updateButtonText('🎮 开始游戏...');
            
            // 1. 开始新游戏
            await startNewGame();
            
            updateButtonText('📖 读取棋盘...');
            
            // 2. 读取游戏状态
            const { gridSize, currentState } = readGameState();
            console.log(`📋 检测到 ${gridSize}×${gridSize} 棋盘:`, currentState);
            
            updateButtonText('🧠 计算中...');
            
            // 3. 求解
            const solver = new FastPuzzleSolver(gridSize);
            const solution = solver.solve(currentState);
            
            updateButtonText('🎯 执行中...');
            
            // 4. 执行（根据模式选择）
            await executeSteps(solution, useDelay);
            
            updateButtonText('🎉 完成！');
            setTimeout(() => updateButtonText('⚡ 瞬间求解'), 800);
            
        } catch (error) {
            console.error('❌ 执行失败:', error.message);
            updateButtonText('❌ 失败');
            setTimeout(() => updateButtonText('⚡ 瞬间求解'), 800);
        }
    }

    // ==================== UI 创建 ====================
    let solverButton;

    function updateButtonText(text) {
        if (solverButton) {
            solverButton.textContent = text;
        }
    }

    function createUI() {
        // 创建样式
        const style = document.createElement('style');
        style.textContent = `
            #auto-solver-btn {
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 10000;
                background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
                color: white;
                border: none;
                padding: 15px 25px;
                border-radius: 25px;
                font-size: 16px;
                font-weight: bold;
                cursor: pointer;
                box-shadow: 0 4px 15px rgba(0,0,0,0.3);
                transition: all 0.3s ease;
                font-family: Arial, sans-serif;
                min-width: 150px;
                text-align: center;
            }
            #auto-solver-btn:hover {
                transform: translateY(-2px);
                box-shadow: 0 6px 20px rgba(0,0,0,0.4);
                background: linear-gradient(45deg, #FF5252, #26C6DA);
            }
            #auto-solver-btn:active {
                transform: translateY(0);
            }
            #auto-solver-btn:disabled {
                background: #ccc;
                cursor: not-allowed;
                transform: none;
            }
        `;
        document.head.appendChild(style);

        // 创建瞬间求解按钮
        solverButton = document.createElement('button');
        solverButton.id = 'auto-solver-btn';
        solverButton.textContent = '⚡ 瞬间求解';
        document.body.appendChild(solverButton);

        // 创建可视化求解按钮
        const visualButton = document.createElement('button');
        visualButton.id = 'visual-solver-btn';
        visualButton.textContent = '👁️ 可视化';
        visualButton.style.cssText = solverButton.style.cssText;
        visualButton.style.top = '80px';
        visualButton.style.minWidth = '120px';
        document.body.appendChild(visualButton);

        // 绑定事件
        solverButton.onclick = () => {
            solverButton.disabled = true;
            autoSolveGame(false).finally(() => {
                solverButton.disabled = false;
            });
        };

        visualButton.onclick = () => {
            visualButton.disabled = true;
            autoSolveGame(true).finally(() => {
                visualButton.disabled = false;
            });
        };

        console.log('✅ UI已创建，点击右上角按钮开始！');
    }

    // ==================== 初始化 ====================
    function init() {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', init);
            return;
        }

        // 等待页面完全加载
        setTimeout(() => {
            createUI();
            console.log('⚡ 使用说明：');
            console.log('   1. "⚡ 瞬间求解" - 无延迟，瞬间完成');
            console.log('   2. "👁️ 可视化" - 1ms间隔，可以看到过程');
            console.log('   3. 系统会自动开始游戏并求解');
            console.log('   4. 推荐使用瞬间求解！');
        }, 1000);
    }

    // 启动
    init();

})();
