<!DOCTYPE html>
<html lang="">
  <head>
    <meta charset="UTF-8">
    <link rel="icon" href="/apps/klotski/favicon.ico">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="format-detection" content="telephone=no">
    <title>数字华容道</title>
    <script type="module" crossorigin src="/apps/klotski/assets/index-Ruo8-Trj.js"></script>
  </head>
  <body>
    <div id="app"></div>
    <script>
      // 禁用双击缩放
      document.addEventListener('touchstart', function(event) {
        // 检查是否在对话框内容区域
        if (!event.target.closest('.dialog-content')) {
          if (event.touches.length > 1) {
            event.preventDefault();
          }
        }
      }, { passive: false });
      
      // 禁用双指缩放
      document.addEventListener('gesturestart', function(event) {
        // 检查是否在对话框内容区域
        if (!event.target.closest('.dialog-content')) {
          event.preventDefault();
        }
      }, { passive: false });

      // iOS特定的缩放预防
      if (/iPad|iPhone|iPod/.test(navigator.userAgent)) {
        // 禁用双击缩放
        let lastTouchEnd = 0;
        document.addEventListener('touchend', function(event) {
          // 检查是否在对话框内容区域
          if (!event.target.closest('.dialog-content')) {
            const now = Date.now();
            if (now - lastTouchEnd < 300) {
              event.preventDefault();
            }
            lastTouchEnd = now;
          }
        }, { passive: false });

        // 禁用缩放手势
        document.addEventListener('gesturechange', function(event) {
          // 检查是否在对话框内容区域
          if (!event.target.closest('.dialog-content')) {
            event.preventDefault();
          }
        }, { passive: false });

        document.addEventListener('gestureend', function(event) {
          // 检查是否在对话框内容区域
          if (!event.target.closest('.dialog-content')) {
            event.preventDefault();
          }
        }, { passive: false });
      }
    </script>
  </body>
</html>
