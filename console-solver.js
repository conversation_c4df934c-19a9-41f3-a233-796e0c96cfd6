// ==================== 数字华容道控制台求解器 ====================
// 使用方法：复制这段代码到浏览器控制台，然后运行
// 支持 3×3、4×4、5×5 拼图，使用BFS算法保证最短步数

(function() {
    'use strict';

    console.log('🧩 数字华容道控制台求解器已加载！');
    console.log('📖 使用说明：');
    console.log('   1. 确保游戏页面已加载完成');
    console.log('   2. 在控制台输入: autoSolve()');
    console.log('   3. 等待自动求解完成');

    // ==================== 超快速N×N 数字拼图求解器 (优化BFS) ====================
    function FastPuzzleSolver(gridSize) {
        if (![3, 4, 5].includes(gridSize)) {
            throw new Error(`不支持的棋盘尺寸：${gridSize}×${gridSize}`);
        }

        const N = gridSize;
        const totalTiles = N * N;

        // 目标状态：1,2,3,...,N²-1,0
        const targetState = new Array(totalTiles);
        for (let i = 0; i < totalTiles - 1; i++) {
            targetState[i] = i + 1;
        }
        targetState[totalTiles - 1] = 0;
        const targetKey = targetState.join(',');

        // 状态转换为字符串键（更快）
        function stateToKey(state) {
            return state.join(',');
        }

        // 获取空格位置的所有可能移动（优化版）
        function getMoves(state) {
            const emptyIndex = state.indexOf(0);
            const moves = [];
            const row = Math.floor(emptyIndex / N);
            const col = emptyIndex % N;

            // 直接在原数组上操作，避免创建新数组
            function makeMove(fromIdx, toIdx) {
                const newState = state.slice(); // 快速复制
                newState[fromIdx] = newState[toIdx];
                newState[toIdx] = 0;
                return newState;
            }

            // 四个方向的移动（优化顺序，优先尝试更可能的移动）
            if (row > 0) moves.push(makeMove(emptyIndex, emptyIndex - N));     // 上
            if (col < N - 1) moves.push(makeMove(emptyIndex, emptyIndex + 1)); // 右
            if (row < N - 1) moves.push(makeMove(emptyIndex, emptyIndex + N)); // 下
            if (col > 0) moves.push(makeMove(emptyIndex, emptyIndex - 1));     // 左

            return moves;
        }

        // 超快速BFS求解
        this.solve = function(initialState) {
            const startTime = performance.now();
            const initialKey = initialState.join(',');

            // 检查是否已经完成
            if (initialKey === targetKey) {
                console.log('🎉 拼图已经完成！');
                return [initialState];
            }

            console.log(`🔍 开始求解 ${N}×${N} 拼图...`);
            console.log('📊 初始状态:', initialState);

            // 使用Map存储路径，更高效
            const queue = [initialState];
            const visited = new Set([initialKey]);
            const parent = new Map();
            parent.set(initialKey, null);
            let nodesExpanded = 0;

            while (queue.length > 0) {
                const currentState = queue.shift();
                const currentKey = currentState.join(',');
                nodesExpanded++;

                // 每10000次检查一下时间
                if (nodesExpanded % 10000 === 0) {
                    const elapsed = performance.now() - startTime;
                    console.log(`⏱️  已搜索 ${nodesExpanded} 个状态，用时 ${elapsed.toFixed(0)}ms`);

                    if (elapsed > 30000) { // 30秒超时
                        throw new Error(`求解超时（${elapsed.toFixed(0)}ms），建议重新打乱拼图`);
                    }
                }

                const possibleMoves = getMoves(currentState);
                for (const move of possibleMoves) {
                    const moveKey = move.join(',');
                    if (!visited.has(moveKey)) {
                        visited.add(moveKey);
                        parent.set(moveKey, currentKey);

                        // 找到解
                        if (moveKey === targetKey) {
                            const elapsed = performance.now() - startTime;

                            // 重建路径
                            const path = [];
                            let current = moveKey;
                            while (current !== null) {
                                const state = current.split(',').map(Number);
                                path.unshift(state);
                                current = parent.get(current);
                            }

                            console.log(`🎉 超快速求解完成！`);
                            console.log(`📈 统计信息:`);
                            console.log(`   - 最短步数: ${path.length - 1}`);
                            console.log(`   - 扩展节点: ${nodesExpanded}`);
                            console.log(`   - 求解用时: ${elapsed.toFixed(1)}ms`);
                            console.log(`   - 搜索效率: ${(nodesExpanded / (elapsed / 1000)).toFixed(0)} 节点/秒`);
                            return path;
                        }

                        queue.push(move);
                    }
                }
            }

            throw new Error('无解：该拼图布局无法还原到目标状态');
        };
    }

    // ==================== 超快速自动执行器 ====================
    function executeSteps(solution, speed = 50) {
        if (!solution || solution.length <= 1) {
            console.log('❌ 没有找到解法或拼图已完成');
            return;
        }

        console.log(`🎯 开始超快速执行，共 ${solution.length - 1} 步`);
        let stepIndex = 0;

        // 预先缓存所有格子，避免重复查询DOM
        const allCells = document.querySelectorAll('.grid-cell');
        if (allCells.length === 0) {
            console.error('❌ 无法找到棋盘格子');
            return;
        }

        const intervalId = setInterval(() => {
            if (stepIndex >= solution.length - 1) {
                clearInterval(intervalId);
                console.log('🎉 超快速求解完成！恭喜！');
                return;
            }

            const currentGrid = solution[stepIndex];
            const nextGrid = solution[stepIndex + 1];
            const emptyIndexNext = nextGrid.indexOf(0);
            const tileToClickValue = currentGrid[emptyIndexNext];

            // 快速查找并点击对应的格子
            let clicked = false;
            for (let i = 0; i < allCells.length; i++) {
                const cell = allCells[i];
                const cellValue = parseInt(cell.textContent.trim());
                if (cellValue === tileToClickValue) {
                    cell.click();
                    clicked = true;
                    console.log(`👆 第 ${stepIndex + 1} 步：点击数字 ${tileToClickValue}`);
                    break;
                }
            }

            if (!clicked) {
                console.error(`❌ 无法找到数字 ${tileToClickValue}，执行停止`);
                clearInterval(intervalId);
                return;
            }

            stepIndex++;
        }, speed);
    }

    // ==================== 主函数 ====================
    window.autoSolve = function(speed = 50) {
        try {
            console.log('🚀 开始自动求解...');
            
            // 读取棋盘
            const cells = document.querySelectorAll('.grid-cell');
            if (cells.length === 0) {
                throw new Error('未找到游戏棋盘，请确保在华容道游戏页面运行此代码');
            }
            
            const totalCells = cells.length;
            const gridSize = Math.sqrt(totalCells);
            
            if (!Number.isInteger(gridSize) || gridSize < 3 || gridSize > 5) {
                throw new Error(`无法识别棋盘尺寸 (检测到${totalCells}个格子，仅支持3×3/4×4/5×5)`);
            }

            // 读取当前状态
            const currentState = Array.from(cells).map(cell => {
                const value = cell.textContent.trim();
                return value === '' || cell.classList.contains('empty') ? 0 : parseInt(value);
            });

            console.log(`📋 检测到 ${gridSize}×${gridSize} 棋盘`);
            
            // 求解
            const solver = new FastPuzzleSolver(gridSize);
            const solution = solver.solve(currentState);
            
            if (solution) {
                console.log(`✅ 找到最优解！准备自动执行...`);
                setTimeout(() => {
                    executeSteps(solution, speed);
                }, 1000);
            }
            
        } catch (error) {
            console.error('❌ 求解失败:', error.message);
            console.log('💡 建议：');
            console.log('   - 确保在华容道游戏页面运行');
            console.log('   - 尝试重新打乱拼图');
            console.log('   - 检查网络连接和页面加载状态');
        }
    };

    // ==================== 极速求解函数 ====================
    window.quickSolve = function() {
        autoSolve(20); // 极快的执行速度
    };

    // ==================== 慢速求解函数 ====================
    window.slowSolve = function() {
        autoSolve(200); // 更慢的执行速度，便于观察
    };

    // ==================== 闪电求解函数 ====================
    window.lightningSolve = function() {
        autoSolve(10); // 闪电般的速度
    };

    // ==================== 仅计算解法函数 ====================
    window.calculateOnly = function() {
        try {
            const cells = document.querySelectorAll('.grid-cell');
            if (cells.length === 0) {
                throw new Error('未找到游戏棋盘');
            }
            
            const totalCells = cells.length;
            const gridSize = Math.sqrt(totalCells);
            
            if (!Number.isInteger(gridSize) || gridSize < 3 || gridSize > 5) {
                throw new Error(`无法识别棋盘尺寸 (检测到${totalCells}个格子)`);
            }

            const currentState = Array.from(cells).map(cell => {
                const value = cell.textContent.trim();
                return value === '' || cell.classList.contains('empty') ? 0 : parseInt(value);
            });

            console.log(`📋 检测到 ${gridSize}×${gridSize} 棋盘`);
            
            const solver = new FastPuzzleSolver(gridSize);
            const solution = solver.solve(currentState);
            
            if (solution) {
                console.log('📝 解法步骤:');
                for (let i = 0; i < solution.length - 1; i++) {
                    const current = solution[i];
                    const next = solution[i + 1];
                    const emptyNext = next.indexOf(0);
                    const tileToMove = current[emptyNext];
                    console.log(`   第 ${i + 1} 步: 点击数字 ${tileToMove}`);
                }
            }
            
        } catch (error) {
            console.error('❌ 计算失败:', error.message);
        }
    };

    // 显示可用函数
    console.log('🎮 可用函数:');
    console.log('   autoSolve()     - 自动求解 (默认速度)');
    console.log('   quickSolve()    - 快速求解');
    console.log('   slowSolve()     - 慢速求解 (便于观察)');
    console.log('   calculateOnly() - 仅计算解法，不自动执行');
    console.log('');
    console.log('💡 推荐使用: autoSolve()');

})();
