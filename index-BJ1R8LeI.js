import {c as We, d as qe, w as se, o as me, b as oe, e as k, m as ge, p as Ee, P as Ge, f as K, n as Q, g as He, h as re, I as _e, L as Ve, i as Ze, j as we, k as je, q as Je, r as Ke, s as B, t as Qe, v as Ye, x as Xe, y as W, _ as et, u as tt, z as ie, A as M, B as l, C as nt, D as z, E as $, F as st, G as E, H as G, J as ot, K as at, M as O, N as V, O as Y, T as lt, Q as rt} from "./index-Ruo8-Trj.js";
let H = 0;
function it(c) {
    c ? (H || document.body.classList.add("van-toast--unclickable"),
    H++) : H && (H--,
    H || document.body.classList.remove("van-toast--unclickable"))
}
const [ct,U] = We("toast")
  , ut = ["show", "overlay", "teleport", "transition", "overlayClass", "overlayStyle", "closeOnClickOverlay", "zIndex"]
  , dt = {
    icon: String,
    show: Boolean,
    type: K("text"),
    overlay: Boolean,
    message: Q,
    iconSize: Q,
    duration: He(2e3),
    position: K("middle"),
    teleport: [String, Object],
    wordBreak: String,
    className: re,
    iconPrefix: String,
    transition: K("van-fade"),
    loadingType: String,
    forbidClick: Boolean,
    overlayClass: re,
    overlayStyle: Object,
    closeOnClick: Boolean,
    closeOnClickOverlay: Boolean,
    zIndex: Q
};
var ye = qe({
    name: ct,
    props: dt,
    emits: ["update:show"],
    setup(c, {emit: w, slots: i}) {
        let d, f = !1;
        const h = () => {
            const t = c.show && c.forbidClick;
            f !== t && (f = t,
            it(f))
        }
          , m = t => w("update:show", t)
          , a = () => {
            c.closeOnClick && m(!1)
        }
          , _ = () => clearTimeout(d)
          , s = () => {
            const {icon: t, type: e, iconSize: r, iconPrefix: S, loadingType: C} = c;
            if (t || e === "success" || e === "fail")
                return k(_e, {
                    name: t || e,
                    size: r,
                    class: U("icon"),
                    classPrefix: S
                }, null);
            if (e === "loading")
                return k(Ve, {
                    class: U("loading"),
                    size: r,
                    type: C
                }, null)
        }
          , n = () => {
            const {type: t, message: e} = c;
            if (i.message)
                return k("div", {
                    class: U("text")
                }, [i.message()]);
            if (Ze(e) && e !== "")
                return t === "html" ? k("div", {
                    key: 0,
                    class: U("text"),
                    innerHTML: String(e)
                }, null) : k("div", {
                    class: U("text")
                }, [e])
        }
        ;
        return se( () => [c.show, c.forbidClick], h),
        se( () => [c.show, c.type, c.message, c.duration], () => {
            _(),
            c.show && c.duration > 0 && (d = setTimeout( () => {
                m(!1)
            }
            , c.duration))
        }
        ),
        me(h),
        oe(h),
        () => k(Ge, ge({
            class: [U([c.position, c.wordBreak === "normal" ? "break-normal" : c.wordBreak, {
                [c.type]: !c.icon
            }]), c.className],
            lockScroll: !1,
            onClick: a,
            onClosed: _,
            "onUpdate:show": m
        }, Ee(c, ut)), {
            default: () => [s(), n()]
        })
    }
});
const ft = {
    icon: "",
    type: "text",
    message: "",
    className: "",
    overlay: !1,
    onClose: void 0,
    onOpened: void 0,
    duration: 2e3,
    teleport: "body",
    iconSize: void 0,
    iconPrefix: void 0,
    position: "middle",
    transition: "van-fade",
    forbidClick: !1,
    loadingType: void 0,
    overlayClass: "",
    overlayStyle: void 0,
    closeOnClick: !1,
    closeOnClickOverlay: !1
};
let Z = []
  , vt = !1
  , ce = we({}, ft);
const ht = new Map;
function pt(c) {
    return Je(c) ? c : {
        message: c
    }
}
function mt() {
    const {instance: c, unmount: w} = Ke({
        setup() {
            const i = B("")
              , {open: d, state: f, close: h, toggle: m} = Qe()
              , a = () => {}
              , _ = () => k(ye, ge(f, {
                onClosed: a,
                "onUpdate:show": m
            }), null);
            return se(i, s => {
                f.message = s
            }
            ),
            Ye().render = _,
            {
                open: d,
                close: h,
                message: i
            }
        }
    });
    return c
}
function gt() {
    if (!Z.length || vt) {
        const c = mt();
        Z.push(c)
    }
    return Z[Z.length - 1]
}
function ue(c={}) {
    if (!je)
        return {};
    const w = gt()
      , i = pt(c);
    return w.open(we({}, ce, ht.get(i.type || ce.type), i)),
    w
}
Xe(ye);
const _t = () => W({
    url: "/levels",
    method: "get"
})
  , wt = c => W({
    url: `/levels/${c}`,
    method: "get"
})
  , yt = c => W({
    url: `/ranking/${c}`,
    method: "get"
})
  , kt = c => W({
    url: `/sectionRanking/${c}`,
    method: "get"
})
  , St = () => W({
    url: "/records",
    method: "get"
})
  , Ct = c => W({
    url: "/record",
    method: "post",
    data: c
});
function xt(c) {
    return c && c.__esModule && Object.prototype.hasOwnProperty.call(c, "default") ? c.default : c
}
var X = {
    exports: {}
}, ee = {
    exports: {}
}, de;
function Tt() {
    return de || (de = 1,
    function() {
        var c = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"
          , w = {
            rotl: function(i, d) {
                return i << d | i >>> 32 - d
            },
            rotr: function(i, d) {
                return i << 32 - d | i >>> d
            },
            endian: function(i) {
                if (i.constructor == Number)
                    return w.rotl(i, 8) & 16711935 | w.rotl(i, 24) & 4278255360;
                for (var d = 0; d < i.length; d++)
                    i[d] = w.endian(i[d]);
                return i
            },
            randomBytes: function(i) {
                for (var d = []; i > 0; i--)
                    d.push(Math.floor(Math.random() * 256));
                return d
            },
            bytesToWords: function(i) {
                for (var d = [], f = 0, h = 0; f < i.length; f++,
                h += 8)
                    d[h >>> 5] |= i[f] << 24 - h % 32;
                return d
            },
            wordsToBytes: function(i) {
                for (var d = [], f = 0; f < i.length * 32; f += 8)
                    d.push(i[f >>> 5] >>> 24 - f % 32 & 255);
                return d
            },
            bytesToHex: function(i) {
                for (var d = [], f = 0; f < i.length; f++)
                    d.push((i[f] >>> 4).toString(16)),
                    d.push((i[f] & 15).toString(16));
                return d.join("")
            },
            hexToBytes: function(i) {
                for (var d = [], f = 0; f < i.length; f += 2)
                    d.push(parseInt(i.substr(f, 2), 16));
                return d
            },
            bytesToBase64: function(i) {
                for (var d = [], f = 0; f < i.length; f += 3)
                    for (var h = i[f] << 16 | i[f + 1] << 8 | i[f + 2], m = 0; m < 4; m++)
                        f * 8 + m * 6 <= i.length * 8 ? d.push(c.charAt(h >>> 6 * (3 - m) & 63)) : d.push("=");
                return d.join("")
            },
            base64ToBytes: function(i) {
                i = i.replace(/[^A-Z0-9+\/]/ig, "");
                for (var d = [], f = 0, h = 0; f < i.length; h = ++f % 4)
                    h != 0 && d.push((c.indexOf(i.charAt(f - 1)) & Math.pow(2, -2 * h + 8) - 1) << h * 2 | c.indexOf(i.charAt(f)) >>> 6 - h * 2);
                return d
            }
        };
        ee.exports = w
    }()),
    ee.exports
}
var te, fe;
function ve() {
    if (fe)
        return te;
    fe = 1;
    var c = {
        utf8: {
            stringToBytes: function(w) {
                return c.bin.stringToBytes(unescape(encodeURIComponent(w)))
            },
            bytesToString: function(w) {
                return decodeURIComponent(escape(c.bin.bytesToString(w)))
            }
        },
        bin: {
            stringToBytes: function(w) {
                for (var i = [], d = 0; d < w.length; d++)
                    i.push(w.charCodeAt(d) & 255);
                return i
            },
            bytesToString: function(w) {
                for (var i = [], d = 0; d < w.length; d++)
                    i.push(String.fromCharCode(w[d]));
                return i.join("")
            }
        }
    };
    return te = c,
    te
}
/*!
 * Determine if an object is a Buffer
 *
 * <AUTHOR> Aboukhadijeh <https://feross.org>
 * @license  MIT
 */
var ne, he;
function Bt() {
    if (he)
        return ne;
    he = 1,
    ne = function(i) {
        return i != null && (c(i) || w(i) || !!i._isBuffer)
    }
    ;
    function c(i) {
        return !!i.constructor && typeof i.constructor.isBuffer == "function" && i.constructor.isBuffer(i)
    }
    function w(i) {
        return typeof i.readFloatLE == "function" && typeof i.slice == "function" && c(i.slice(0, 0))
    }
    return ne
}
var pe;
function Ft() {
    return pe || (pe = 1,
    function() {
        var c = Tt()
          , w = ve().utf8
          , i = Bt()
          , d = ve().bin
          , f = function(h, m) {
            h.constructor == String ? m && m.encoding === "binary" ? h = d.stringToBytes(h) : h = w.stringToBytes(h) : i(h) ? h = Array.prototype.slice.call(h, 0) : !Array.isArray(h) && h.constructor !== Uint8Array && (h = h.toString());
            for (var a = c.bytesToWords(h), _ = h.length * 8, s = **********, n = -271733879, t = -**********, e = 271733878, r = 0; r < a.length; r++)
                a[r] = (a[r] << 8 | a[r] >>> 24) & 16711935 | (a[r] << 24 | a[r] >>> 8) & 4278255360;
            a[_ >>> 5] |= 128 << _ % 32,
            a[(_ + 64 >>> 9 << 4) + 14] = _;
            for (var S = f._ff, C = f._gg, T = f._hh, F = f._ii, r = 0; r < a.length; r += 16) {
                var A = s
                  , N = n
                  , b = t
                  , P = e;
                s = S(s, n, t, e, a[r + 0], 7, -680876936),
                e = S(e, s, n, t, a[r + 1], 12, -389564586),
                t = S(t, e, s, n, a[r + 2], 17, 606105819),
                n = S(n, t, e, s, a[r + 3], 22, -1044525330),
                s = S(s, n, t, e, a[r + 4], 7, -176418897),
                e = S(e, s, n, t, a[r + 5], 12, 1200080426),
                t = S(t, e, s, n, a[r + 6], 17, -1473231341),
                n = S(n, t, e, s, a[r + 7], 22, -45705983),
                s = S(s, n, t, e, a[r + 8], 7, 1770035416),
                e = S(e, s, n, t, a[r + 9], 12, -1958414417),
                t = S(t, e, s, n, a[r + 10], 17, -42063),
                n = S(n, t, e, s, a[r + 11], 22, -1990404162),
                s = S(s, n, t, e, a[r + 12], 7, 1804603682),
                e = S(e, s, n, t, a[r + 13], 12, -40341101),
                t = S(t, e, s, n, a[r + 14], 17, -1502002290),
                n = S(n, t, e, s, a[r + 15], 22, 1236535329),
                s = C(s, n, t, e, a[r + 1], 5, -165796510),
                e = C(e, s, n, t, a[r + 6], 9, -1069501632),
                t = C(t, e, s, n, a[r + 11], 14, 643717713),
                n = C(n, t, e, s, a[r + 0], 20, -373897302),
                s = C(s, n, t, e, a[r + 5], 5, -701558691),
                e = C(e, s, n, t, a[r + 10], 9, 38016083),
                t = C(t, e, s, n, a[r + 15], 14, -660478335),
                n = C(n, t, e, s, a[r + 4], 20, -405537848),
                s = C(s, n, t, e, a[r + 9], 5, 568446438),
                e = C(e, s, n, t, a[r + 14], 9, -1019803690),
                t = C(t, e, s, n, a[r + 3], 14, -187363961),
                n = C(n, t, e, s, a[r + 8], 20, 1163531501),
                s = C(s, n, t, e, a[r + 13], 5, -1444681467),
                e = C(e, s, n, t, a[r + 2], 9, -51403784),
                t = C(t, e, s, n, a[r + 7], 14, 1735328473),
                n = C(n, t, e, s, a[r + 12], 20, -1926607734),
                s = T(s, n, t, e, a[r + 5], 4, -378558),
                e = T(e, s, n, t, a[r + 8], 11, -2022574463),
                t = T(t, e, s, n, a[r + 11], 16, 1839030562),
                n = T(n, t, e, s, a[r + 14], 23, -35309556),
                s = T(s, n, t, e, a[r + 1], 4, -1530992060),
                e = T(e, s, n, t, a[r + 4], 11, 1272893353),
                t = T(t, e, s, n, a[r + 7], 16, -155497632),
                n = T(n, t, e, s, a[r + 10], 23, -1094730640),
                s = T(s, n, t, e, a[r + 13], 4, 681279174),
                e = T(e, s, n, t, a[r + 0], 11, -358537222),
                t = T(t, e, s, n, a[r + 3], 16, -722521979),
                n = T(n, t, e, s, a[r + 6], 23, 76029189),
                s = T(s, n, t, e, a[r + 9], 4, -640364487),
                e = T(e, s, n, t, a[r + 12], 11, -421815835),
                t = T(t, e, s, n, a[r + 15], 16, 530742520),
                n = T(n, t, e, s, a[r + 2], 23, -995338651),
                s = F(s, n, t, e, a[r + 0], 6, -198630844),
                e = F(e, s, n, t, a[r + 7], 10, 1126891415),
                t = F(t, e, s, n, a[r + 14], 15, -1416354905),
                n = F(n, t, e, s, a[r + 5], 21, -57434055),
                s = F(s, n, t, e, a[r + 12], 6, 1700485571),
                e = F(e, s, n, t, a[r + 3], 10, -1894986606),
                t = F(t, e, s, n, a[r + 10], 15, -1051523),
                n = F(n, t, e, s, a[r + 1], 21, -2054922799),
                s = F(s, n, t, e, a[r + 8], 6, 1873313359),
                e = F(e, s, n, t, a[r + 15], 10, -30611744),
                t = F(t, e, s, n, a[r + 6], 15, -1560198380),
                n = F(n, t, e, s, a[r + 13], 21, 1309151649),
                s = F(s, n, t, e, a[r + 4], 6, -145523070),
                e = F(e, s, n, t, a[r + 11], 10, -1120210379),
                t = F(t, e, s, n, a[r + 2], 15, 718787259),
                n = F(n, t, e, s, a[r + 9], 21, -343485551),
                s = s + A >>> 0,
                n = n + N >>> 0,
                t = t + b >>> 0,
                e = e + P >>> 0
            }
            return c.endian([s, n, t, e])
        };
        f._ff = function(h, m, a, _, s, n, t) {
            var e = h + (m & a | ~m & _) + (s >>> 0) + t;
            return (e << n | e >>> 32 - n) + m
        }
        ,
        f._gg = function(h, m, a, _, s, n, t) {
            var e = h + (m & _ | a & ~_) + (s >>> 0) + t;
            return (e << n | e >>> 32 - n) + m
        }
        ,
        f._hh = function(h, m, a, _, s, n, t) {
            var e = h + (m ^ a ^ _) + (s >>> 0) + t;
            return (e << n | e >>> 32 - n) + m
        }
        ,
        f._ii = function(h, m, a, _, s, n, t) {
            var e = h + (a ^ (m | ~_)) + (s >>> 0) + t;
            return (e << n | e >>> 32 - n) + m
        }
        ,
        f._blocksize = 16,
        f._digestsize = 16,
        X.exports = function(h, m) {
            if (h == null)
                throw new Error("Illegal argument " + h);
            var a = c.wordsToBytes(f(h, m));
            return m && m.asBytes ? a : m && m.asString ? d.bytesToString(a) : c.bytesToHex(a)
        }
    }()),
    X.exports
}
var It = Ft();
const Mt = xt(It)
  , zt = {
    class: "game-container"
}
  , $t = {
    key: 0,
    class: "fireworks-container"
}
  , Ot = {
    class: "game-content"
}
  , Rt = {
    class: "control-panel"
}
  , Dt = {
    class: "panel-row"
}
  , Lt = {
    class: "nickname-group"
}
  , bt = {
    class: "value"
}
  , At = {
    class: "panel-row"
}
  , Nt = {
    class: "panel-row"
}
  , Pt = {
    class: "game-board"
}
  , Ut = ["onClick"]
  , Wt = {
    class: "game-info"
}
  , qt = {
    class: "info-item center"
}
  , Et = {
    class: "dialog-title"
}
  , Gt = {
    class: "dialog-content"
}
  , Ht = {
    class: "rank-list"
}
  , Vt = {
    key: 0,
    class: "empty-tip"
}
  , Zt = {
    class: "rank-info"
}
  , jt = {
    class: "player-name"
}
  , Jt = {
    class: "score-info"
}
  , Kt = {
    class: "time"
}
  , Qt = {
    class: "rank-list"
}
  , Yt = {
    key: 0,
    class: "empty-tip"
}
  , Xt = {
    class: "rank-info"
}
  , en = {
    class: "player-name"
}
  , tn = {
    class: "score-info"
}
  , nn = {
    class: "time"
}
  , sn = {
    class: "dialog-title"
}
  , on = {
    class: "dialog-content"
}
  , an = {
    class: "records-list"
}
  , ln = {
    key: 0,
    class: "empty-tip"
}
  , rn = {
    class: "record-main"
}
  , cn = {
    class: "record-info"
}
  , un = {
    class: "record-difficulty"
}
  , dn = {
    class: "record-name"
}
  , fn = {
    class: "record-stats"
}
  , vn = {
    class: "record-time"
}
  , hn = {
    class: "record-date"
}
  , pn = {
    class: "dialog-title"
}
  , mn = {
    class: "dialog-content"
}
  , gn = {
    class: "difficulty-list"
}
  , _n = ["onClick"]
  , wn = {
    class: "difficulty-title"
}
  , yn = {
    class: "difficulty-desc"
}
  , kn = {
    class: "dialog-title"
}
  , Sn = {
    __name: "index",
    setup(c) {
        const w = tt()
          , i = B("玩家")
          , d = B([1, 2, 3, 4, 5, 6, 7, 8, 0])
          , f = B(0)
          , h = B(0)
          , m = B(!1);
        let a = null;
        const _ = B({
            rows: 3,
            cols: 3
        })
          , s = B([])
          , n = B(1)
          , t = B([])
          , e = B([])
          , r = B(0)
          , S = B([])
          , C = B(!1)
          , T = ie( () => {
            const u = _.value.rows * _.value.cols;
            return Array.from({
                length: u - 1
            }, (o, p) => p + 1).concat(0)
        }
        )
          , F = ie( () => ({
            gridTemplateColumns: `repeat(${_.value.cols}, 1fr)`
        }))
          , A = B(!1)
          , N = B(!1)
          , b = B(!1)
          , P = B(!1)
          , L = B("90%")
          , ke = u => u ? u.length > 9 ? u.slice(0, 9) + "..." : u : "玩家"
          , Se = async () => {
            await w.userSelf(),
            w.user && (i.value = ke(w.user.user_nickname))
        }
          , Ce = async () => {
            await j(),
            A.value = !0
        }
          , xe = async () => {
            try {
                const u = await St();
                S.value = u.data
            } catch (u) {
                console.error("获取游戏记录失败:", u)
            }
            N.value = !0
        }
          , Te = async () => {
            s.value.length === 0 && await le(),
            b.value = !0
        }
          , Be = () => {
            d.value = Le(),
            f.value = 0,
            h.value = 0,
            m.value = !0,
            a && clearInterval(a);
            const u = performance.now();
            a = setInterval( () => {
                h.value = (performance.now() - u) / 1e3
            }
            , 10)
        }
          , Fe = u => {
            if (!m.value)
                return;
            const {cols: o} = _.value
              , p = d.value.indexOf(0);
            if ([u === p - o, u === p + o, u === p - 1 && Math.floor(u / o) === Math.floor(p / o), u === p + 1 && Math.floor(u / o) === Math.floor(p / o)].some(Boolean)) {
                const x = [...d.value];
                [x[u],x[p]] = [x[p], x[u]],
                d.value = x,
                f.value++,
                Ie() && ze()
            }
        }
          , Ie = () => d.value.every( (u, o) => u === T.value[o])
          , Me = () => {
            C.value = !0,
            setTimeout( () => {
                C.value = !1
            }
            , 2e3)
        }
          , ze = async () => {
            m.value = !1,
            a && (clearInterval(a),
            a = null),
            Me(),
            ue({
                message: "恭喜完成！用时：" + $e(h.value),
                duration: 2e3,
                position: "center",
                className: "success-toast"
            });
            try {
                const u = new Date
                  , o = u.getFullYear()
                  , p = String(u.getMonth() + 1).padStart(2, "0")
                  , y = String(u.getDate()).padStart(2, "0")
                  , x = String(u.getHours()).padStart(2, "0")
                  , D = String(u.getMinutes()).padStart(2, "0")
                  , I = String(u.getSeconds()).padStart(2, "0")
                  , v = `${o}-${p}-${y} ${x}:${D}:${I}`
                  , R = Math.floor(Date.now() / 1e3)
                  , g = {
                    player_id: w.user.id,
                    level_id: n.value,
                    end_time: v,
                    steps: f.value,
                    total_time: Number(h.value.toFixed(2)),
                    timestamp: R
                }
                  , q = `${g.player_id}${g.level_id}${g.steps}${g.total_time}${R}huarongdao_game_secret`
                  , Ue = Mt(q);
                g.sign = Ue,
                await Ct(g),
                await j()
            } catch (u) {
                console.error("保存游戏记录失败:", u)
            }
        }
          , $e = u => {
            const o = Math.floor(u / 60)
              , p = Math.floor(u % 60)
              , y = Math.floor(u % 1 * 100);
            return `${o.toString().padStart(2, "0")}:${p.toString().padStart(2, "0")}.${y.toString().padStart(2, "0")}`
        }
          , Oe = u => {
            const o = Math.floor(u / 60)
              , p = Math.floor(u % 60);
            return `${o.toString().padStart(2, "0")}:${p.toString().padStart(2, "0")}`
        }
          , ae = () => {
            m.value && (m.value = !1,
            a && (clearInterval(a),
            a = null),
            f.value = 0,
            h.value = 0)
        }
          , Re = u => {
            const o = [...u];
            for (let p = o.length - 1; p > 0; p--) {
                const y = Math.floor(Math.random() * (p + 1));
                [o[p],o[y]] = [o[y], o[p]]
            }
            return o
        }
          , De = u => {
            const o = _.value.cols
              , p = u.filter(I => I !== 0);
            let y = 0;
            for (let I = 0; I < p.length - 1; I++)
                for (let v = I + 1; v < p.length; v++)
                    p[I] > p[v] && y++;
            const x = u.indexOf(0)
              , D = Math.floor(x / o) + 1;
            return o % 2 === 1 ? y % 2 === 0 : (y + D) % 2 === 0
        }
          , Le = () => {
            const u = _.value.rows * _.value.cols;
            let o, p = 0;
            const y = 1e3;
            do
                if (o = Re(Array.from({
                    length: u
                }, (x, D) => D)),
                p++,
                p >= y) {
                    const x = _.value.cols
                      , D = o.indexOf(0)
                      , I = Math.floor(D / x) + 1
                      , v = o.filter(g => g !== 0);
                    let R = 0;
                    for (let g = 0; g < v.length - 1; g++)
                        for (let q = g + 1; q < v.length; q++)
                            v[g] > v[q] && R++;
                    if (x % 2 === 1) {
                        if (R % 2 === 1) {
                            for (let g = o.length - 1; g > 0; g--)
                                if (o[g] !== 0 && o[g - 1] !== 0) {
                                    [o[g],o[g - 1]] = [o[g - 1], o[g]];
                                    break
                                }
                        }
                    } else if ((R + I) % 2 === 1) {
                        for (let g = o.length - 1; g > 0; g--)
                            if (o[g] !== 0 && o[g - 1] !== 0) {
                                [o[g],o[g - 1]] = [o[g - 1], o[g]];
                                break
                            }
                    }
                    break
                }
            while (!De(o) || be(o));
            return o
        }
          , be = u => {
            let o = 0;
            const p = T.value;
            return u.forEach( (y, x) => {
                y !== p[x] && o++
            }
            ),
            o < 5
        }
          , le = async () => {
            const u = await _t();
            s.value = u.data
        }
          , Ae = async u => {
            const p = (await wt(u.id)).data;
            n.value = p.id;
            const [y,x] = p.difficulty.toLowerCase().split("x").map(Number);
            _.value = {
                rows: y,
                cols: x
            },
            ae();
            const D = y * x;
            d.value = Array.from({
                length: D - 1
            }, (I, v) => v + 1).concat(0),
            await j(),
            b.value = !1
        }
          , j = async () => {
            try {
                const [u,o] = await Promise.all([yt(n.value), kt(n.value)]);
                t.value = u.data,
                e.value = o.data
            } catch (u) {
                console.error("获取排行榜失败:", u)
            }
        }
          , J = u => Number(u).toFixed(2) + "s"
          , Ne = () => {
            P.value = !0
        }
        ;
        me(async () => {
            window.history.replaceState(null, "", window.location.origin + "/apps/klotski/#/game"),
            await Se(),
            await le();
            const u = _.value.rows * _.value.cols;
            d.value = Array.from({
                length: u - 1
            }, (p, y) => y + 1).concat(0);
            const o = () => {
                const p = window.innerWidth;
                p > 1024 ? L.value = "500px" : p > 768 ? L.value = "600px" : p > 480 ? L.value = "85%" : L.value = "90%"
            }
            ;
            o(),
            window.addEventListener("resize", o),
            oe( () => {
                window.removeEventListener("resize", o)
            }
            )
        }
        );
        const Pe = setInterval( () => {
            const o = window.outerWidth - window.innerWidth > 160
              , p = window.outerHeight - window.innerHeight > 160;
            (o || p) && m.value && (ue({
                message: "检测到开发者工具，游戏已暂停",
                duration: 2e3,
                position: "center"
            }),
            ae())
        }
        , 1e3);
        return oe( () => {
            window.removeEventListener("resize", updateDialogWidth),
            clearInterval(Pe)
        }
        ),
        (u, o) => {
            const p = ot
              , y = _e
              , x = lt
              , D = rt
              , I = at;
            return O(),
            M("div", zt, [C.value ? (O(),
            M("div", $t, o[9] || (o[9] = [l("div", {
                class: "firework"
            }, "成功过关", -1)]))) : nt("", !0), l("div", Ot, [l("div", Rt, [l("div", Dt, [l("div", Lt, [o[10] || (o[10] = l("span", {
                class: "label"
            }, "昵称：", -1)), l("span", bt, z(i.value), 1)]), k(p, {
                onClick: Te
            }, {
                default: $( () => o[11] || (o[11] = [V("选择关卡")])),
                _: 1
            })]), l("div", At, [k(p, {
                onClick: xe
            }, {
                default: $( () => o[12] || (o[12] = [V("游戏记录")])),
                _: 1
            }), k(p, {
                onClick: Ce
            }, {
                default: $( () => o[13] || (o[13] = [V("排行榜")])),
                _: 1
            })]), l("div", Nt, [k(p, {
                type: "primary",
                onClick: Be
            }, {
                default: $( () => o[14] || (o[14] = [V("开始游戏")])),
                _: 1
            })])]), l("div", Pt, [l("div", {
                class: "grid-container",
                style: st(F.value)
            }, [(O(!0),
            M(E, null, G(d.value, (v, R) => (O(),
            M("div", {
                key: R,
                class: Y(["grid-cell", {
                    empty: v === 0
                }]),
                onClick: g => Fe(R)
            }, z(v !== 0 ? v : ""), 11, Ut))), 128))], 4)]), l("div", Wt, [o[15] || (o[15] = l("div", {
                class: "info-item left"
            }, null, -1)), l("div", qt, [k(y, {
                name: "clock-o"
            }), l("span", null, z(Oe(h.value)), 1)]), l("div", {
                class: "info-item right",
                onClick: Ne
            }, [k(y, {
                name: "question-o"
            })])])]), k(I, {
                show: A.value,
                "onUpdate:show": o[2] || (o[2] = v => A.value = v),
                title: "排行榜",
                showConfirmButton: !1,
                width: L.value
            }, {
                title: $( () => [l("div", Et, [o[16] || (o[16] = l("span", null, "排行榜", -1)), k(y, {
                    name: "cross",
                    onClick: o[0] || (o[0] = v => A.value = !1)
                })])]),
                default: $( () => [l("div", Gt, [k(D, {
                    active: r.value,
                    "onUpdate:active": o[1] || (o[1] = v => r.value = v)
                }, {
                    default: $( () => [k(x, {
                        title: "学院/部门榜"
                    }, {
                        default: $( () => [l("div", Ht, [e.value.length === 0 ? (O(),
                        M("div", Vt, " 暂无排名数据 ")) : (O(!0),
                        M(E, {
                            key: 1
                        }, G(e.value, (v, R) => (O(),
                        M("div", {
                            key: v.id,
                            class: "rank-item"
                        }, [l("div", Zt, [l("span", {
                            class: Y(["rank-num", {
                                top3: R < 3
                            }])
                        }, z(R + 1), 3), l("span", jt, z(v.user_nickname + "(" + v.user_name + ")" || "匿名玩家"), 1)]), l("div", Jt, [l("span", Kt, z(J(v.total_time)), 1)])]))), 128))])]),
                        _: 1
                    }), k(x, {
                        title: "总榜单"
                    }, {
                        default: $( () => [l("div", Qt, [t.value.length === 0 ? (O(),
                        M("div", Yt, " 暂无排名数据 ")) : (O(!0),
                        M(E, {
                            key: 1
                        }, G(t.value, v => (O(),
                        M("div", {
                            key: v.id,
                            class: "rank-item"
                        }, [l("div", Xt, [l("span", {
                            class: Y(["rank-num", {
                                top3: v.rank <= 3
                            }])
                        }, z(v.rank), 3), l("span", en, z(v.user_section + " " + v.user_name || "匿名玩家"), 1)]), l("div", tn, [l("span", nn, z(J(v.total_time)), 1)])]))), 128))])]),
                        _: 1
                    })]),
                    _: 1
                }, 8, ["active"])])]),
                _: 1
            }, 8, ["show", "width"]), k(I, {
                show: N.value,
                "onUpdate:show": o[4] || (o[4] = v => N.value = v),
                title: "游戏记录",
                showConfirmButton: !1,
                width: L.value
            }, {
                title: $( () => [l("div", sn, [o[17] || (o[17] = l("span", null, "游戏记录", -1)), k(y, {
                    name: "cross",
                    onClick: o[3] || (o[3] = v => N.value = !1)
                })])]),
                default: $( () => [l("div", on, [l("div", an, [S.value.length === 0 ? (O(),
                M("div", ln, " 暂无游戏记录 ")) : (O(!0),
                M(E, {
                    key: 1
                }, G(S.value, v => (O(),
                M("div", {
                    key: v.id,
                    class: "record-item"
                }, [l("div", rn, [l("div", cn, [l("span", un, z(v.difficulty), 1), l("span", dn, z(v.user_nickname), 1)]), l("div", fn, [l("span", vn, z(J(v.total_time)), 1)])]), l("div", hn, z(v.end_time), 1)]))), 128))])])]),
                _: 1
            }, 8, ["show", "width"]), k(I, {
                show: b.value,
                "onUpdate:show": o[6] || (o[6] = v => b.value = v),
                title: "选择关卡",
                showConfirmButton: !1,
                width: L.value
            }, {
                title: $( () => [l("div", pn, [o[18] || (o[18] = l("span", null, "选择关卡", -1)), k(y, {
                    name: "cross",
                    onClick: o[5] || (o[5] = v => b.value = !1)
                })])]),
                default: $( () => [l("div", mn, [l("div", gn, [(O(!0),
                M(E, null, G(s.value, v => (O(),
                M("div", {
                    key: v.id,
                    class: "difficulty-item",
                    onClick: R => Ae(v)
                }, [l("div", wn, z(v.difficulty), 1), l("div", yn, z(v.description), 1)], 8, _n))), 128))])])]),
                _: 1
            }, 8, ["show", "width"]), k(I, {
                show: P.value,
                "onUpdate:show": o[8] || (o[8] = v => P.value = v),
                title: "游戏规则",
                showConfirmButton: !1,
                width: L.value
            }, {
                title: $( () => [l("div", kn, [o[19] || (o[19] = l("span", null, "游戏规则", -1)), k(y, {
                    name: "cross",
                    onClick: o[7] || (o[7] = v => P.value = !1)
                })])]),
                default: $( () => [o[20] || (o[20] = l("div", {
                    class: "dialog-content"
                }, [l("div", {
                    class: "rules-container"
                }, [l("h3", {
                    class: "rules-title"
                }, "游戏目标"), l("ol", {
                    class: "rules-list"
                }, [l("li", null, "将混乱的数字方块通过滑动还原为 1-8 的顺序排列"), l("li", null, "确保空白格最终位于最后位置")]), l("h3", {
                    class: "rules-title"
                }, "基本规则"), l("ol", {
                    class: "rules-list"
                }, [l("li", null, "点击与空白格相邻的数字方块即可移动"), l("li", null, "只能水平或垂直方向移动方块"), l("li", null, "方块不能跨格移动或跳跃")]), l("h3", {
                    class: "rules-title"
                }, "游戏操作"), l("ol", {
                    class: "rules-list"
                }, [l("li", null, '点击"开始游戏"随机打乱方块开始'), l("li", null, "计时器会自动记录完成时间"), l("li", null, "完成后可查看排行榜和记录")]), l("h3", {
                    class: "rules-title"
                }, "游戏提示"), l("ol", {
                    class: "rules-list"
                }, [l("li", null, "先观察整体布局再行动"), l("li", null, "尽量避免重复移动"), l("li", null, "可以分区域逐步还原")]), l("h3", {
                    class: "rules-title"
                }, "成功案例"), l("div", {
                    class: "success-example"
                }, [l("div", {
                    class: "example-grid"
                }, [l("div", {
                    class: "example-cell"
                }, "1"), l("div", {
                    class: "example-cell"
                }, "2"), l("div", {
                    class: "example-cell"
                }, "3"), l("div", {
                    class: "example-cell"
                }, "4"), l("div", {
                    class: "example-cell"
                }, "5"), l("div", {
                    class: "example-cell"
                }, "6"), l("div", {
                    class: "example-cell"
                }, "7"), l("div", {
                    class: "example-cell"
                }, "8"), l("div", {
                    class: "example-cell empty"
                })])])])], -1))]),
                _: 1
            }, 8, ["show", "width"])])
        }
    }
}
  , Tn = et(Sn, [["__scopeId", "data-v-88486df3"]]);
export {Tn as default};
