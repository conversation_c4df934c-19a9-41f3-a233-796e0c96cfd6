// ==UserScript==
// @name         数字华容道自动求解器 (IDA* + 曼哈顿距离)
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  使用IDA*算法+曼哈顿距离启发式自动求解数字华容道，支持3×3/4×4/5×5
// <AUTHOR> Assistant
// @match        https://itstudio.henau.edu.cn/apps/klotski/*
// @grant        none
// ==/UserScript==

(function() {
    'use strict';

    // ==================== N-拼图求解器 (IDA* + 曼哈顿距离 + 线性冲突) ====================
    function solveNPuzzle(start, N, options = {}) {
        const t0 = performance.now();
        const GOAL = Array.from({length: N*N}, (_, i) => (i+1)%(N*N));
        const goalPosR = new Array(N*N);
        const goalPosC = new Array(N*N);

        for (let i = 0; i < GOAL.length; i++) {
            goalPosR[GOAL[i]] = Math.floor(i/N);
            goalPosC[GOAL[i]] = i % N;
        }

        function manhattan(s) {
            let h = 0;
            for (let i = 0; i < s.length; i++) {
                const v = s[i];
                if (v !== 0) {
                    const r = Math.floor(i/N), c = i % N;
                    h += Math.abs(r - goalPosR[v]) + Math.abs(c - goalPosC[v]);
                }
            }
            return h;
        }

        // 线性冲突启发式：检测同一行/列中的冲突
        function linearConflict(s) {
            let conflicts = 0;

            // 检查行冲突
            for (let r = 0; r < N; r++) {
                const row = [];
                for (let c = 0; c < N; c++) {
                    const val = s[r * N + c];
                    if (val !== 0 && goalPosR[val] === r) {
                        row.push({val, goalCol: goalPosC[val], currentCol: c});
                    }
                }
                // 计算该行的冲突数
                for (let i = 0; i < row.length; i++) {
                    for (let j = i + 1; j < row.length; j++) {
                        if (row[i].goalCol > row[j].goalCol && row[i].currentCol < row[j].currentCol) {
                            conflicts += 2; // 每个冲突需要2步额外移动
                        }
                    }
                }
            }

            // 检查列冲突
            for (let c = 0; c < N; c++) {
                const col = [];
                for (let r = 0; r < N; r++) {
                    const val = s[r * N + c];
                    if (val !== 0 && goalPosC[val] === c) {
                        col.push({val, goalRow: goalPosR[val], currentRow: r});
                    }
                }
                // 计算该列的冲突数
                for (let i = 0; i < col.length; i++) {
                    for (let j = i + 1; j < col.length; j++) {
                        if (col[i].goalRow > col[j].goalRow && col[i].currentRow < col[j].currentRow) {
                            conflicts += 2;
                        }
                    }
                }
            }

            return conflicts;
        }

        // 增强的启发式函数：曼哈顿距离 + 线性冲突
        function enhancedHeuristic(s) {
            return manhattan(s) + linearConflict(s);
        }

        function isSolvable(arr) {
            const inv = (() => {
                const a = arr.filter(x => x !== 0);
                let inv = 0;
                for (let i = 0; i < a.length; i++) {
                    for (let j = i + 1; j < a.length; j++) {
                        if (a[i] > a[j]) inv++;
                    }
                }
                return inv;
            })();
            
            const blankIdx = arr.indexOf(0);
            const blankRowFromBottom = N - Math.floor(blankIdx/N);
            
            if (N % 2 === 1) {
                return inv % 2 === 0;
            } else {
                return (blankRowFromBottom % 2 === 0) ? (inv % 2 === 1) : (inv % 2 === 0);
            }
        }

        if (!isSolvable(start)) {
            throw new Error("该初始局面不可解（奇偶性不匹配）");
        }

        // 根据拼图大小调整超时时间和启发式
        const maxTimeMs = options.maxTimeMs ?? (N <= 3 ? 5000 : N <= 4 ? 30000 : 60000);
        const useEnhancedHeuristic = N >= 4; // 4×4及以上使用增强启发式
        let nodesExpanded = 0;

        const DIRS = [
            {dr: -1, dc: 0, ch: 'U', rev: 'D'},
            {dr: 1, dc: 0, ch: 'D', rev: 'U'},
            {dr: 0, dc: -1, ch: 'L', rev: 'R'},
            {dr: 0, dc: 1, ch: 'R', rev: 'L'},
        ];

        // 移动优先级：优先尝试将数字移向目标位置的方向
        function getMoveOrder(blankIdx, state) {
            const r0 = Math.floor(blankIdx / N), c0 = blankIdx % N;
            const moves = [];

            for (const d of DIRS) {
                const r = r0 + d.dr, c = c0 + d.dc;
                if (r >= 0 && r < N && c >= 0 && c < N) {
                    const toIdx = r * N + c;
                    const tile = state[toIdx];
                    if (tile !== 0) {
                        // 计算移动该数字的收益（负值表示更好）
                        const tileGoalR = goalPosR[tile], tileGoalC = goalPosC[tile];
                        const currentDist = Math.abs(r - tileGoalR) + Math.abs(c - tileGoalC);
                        const newDist = Math.abs(r0 - tileGoalR) + Math.abs(c0 - tileGoalC);
                        const benefit = currentDist - newDist;
                        moves.push({dir: d, benefit});
                    }
                }
            }

            // 按收益排序，优先尝试收益大的移动
            return moves.sort((a, b) => b.benefit - a.benefit).map(m => m.dir);
        }

        function search(path, g, bound, blankIdx, h, lastMove) {
            const now = performance.now();
            if (now - t0 > maxTimeMs) return {cost: Infinity, timeout: true};

            const f = g + h;
            if (f > bound) return {cost: f};
            if (h === 0) return {found: true};

            const r0 = Math.floor(blankIdx / N), c0 = blankIdx % N;
            let minCost = Infinity;

            // 使用智能移动顺序
            const moveOrder = getMoveOrder(blankIdx, state);

            for (const d of moveOrder) {
                if (lastMove && d.ch === lastMove.rev) continue;

                const r = r0 + d.dr, c = c0 + d.dc;
                const toIdx = r * N + c;
                const tile = state[toIdx];

                // 执行移动
                state[blankIdx] = tile;
                state[toIdx] = 0;
                nodesExpanded++;

                // 重新计算启发式值（对于大拼图使用增强启发式）
                const h2 = useEnhancedHeuristic ? enhancedHeuristic(state) : manhattan(state);

                path.push(d.ch);
                const res = search(path, g + 1, bound, toIdx, h2, d);
                if (res.found) return res;
                if (res.timeout) return res;
                if (res.cost < minCost) minCost = res.cost;

                // 回溯
                path.pop();
                state[toIdx] = tile;
                state[blankIdx] = 0;
            }
            return {cost: minCost};
        }

        const state = start.slice();
        let h0 = useEnhancedHeuristic ? enhancedHeuristic(state) : manhattan(state);
        let bound = h0;
        const path = [];
        let blank = state.indexOf(0);

        console.log(`使用${useEnhancedHeuristic ? '增强' : '基础'}启发式，初始估值: ${h0}, 超时限制: ${maxTimeMs}ms`);

        for (;;) {
            const res = search(path, 0, bound, blank, h0, null);
            if (res.found) {
                return {moves: path.slice(), expanded: nodesExpanded, timeMs: performance.now() - t0};
            }
            if (res.timeout) {
                throw new Error(`求解超时（>${maxTimeMs} ms），建议尝试重新打乱或等待更好的初始状态`);
            }
            if (res.cost === Infinity) {
                throw new Error("未找到解");
            }
            bound = res.cost;

            // 每次迭代输出进度
            if (performance.now() - t0 > 1000) {
                console.log(`搜索深度: ${bound}, 已扩展: ${nodesExpanded} 节点, 用时: ${(performance.now() - t0).toFixed(0)}ms`);
            }
        }
    }

    // ==================== 页面交互函数 ====================
    function readBoard() {
        const cells = Array.from(document.querySelectorAll('.grid-cell'));
        if (cells.length === 0) {
            throw new Error("未找到游戏棋盘，请确保游戏已加载");
        }

        const nums = cells.map(el => {
            const text = el.textContent.trim();
            if (text === '' || el.classList.contains('empty')) {
                return 0;
            }
            const n = parseInt(text, 10);
            return Number.isFinite(n) ? n : 0;
        });

        const N = Math.sqrt(nums.length);
        if (!Number.isInteger(N)) {
            throw new Error(`检测到的格子数量 ${nums.length} 不是完全平方数`);
        }

        return {nums, N: Math.floor(N), cells};
    }

    function findBlankIndex(cells) {
        return cells.findIndex(el => {
            const text = el.textContent.trim();
            return text === '' || el.classList.contains('empty');
        });
    }

    function playByClicks(moves, N, delay = 80) {
        const delta = {U: -N, D: N, L: -1, R: 1};
        let i = 0;

        return new Promise((resolve) => {
            const timer = setInterval(() => {
                try {
                    const currentCells = Array.from(document.querySelectorAll('.grid-cell'));
                    const blankIdx = findBlankIndex(currentCells);

                    if (blankIdx < 0 || i >= moves.length) {
                        clearInterval(timer);
                        resolve();
                        return;
                    }

                    const move = moves[i++];
                    const targetIdx = blankIdx + delta[move];

                    if (targetIdx < 0 || targetIdx >= currentCells.length) {
                        clearInterval(timer);
                        resolve();
                        return;
                    }

                    // 点击目标格子
                    currentCells[targetIdx].click();

                    // 更新进度
                    updateProgress(i, moves.length);

                } catch (error) {
                    console.error('自动点击出错:', error);
                    clearInterval(timer);
                    resolve();
                }
            }, delay);
        });
    }

    // 添加难度评估函数
    function assessDifficulty(nums, N) {
        const goal = Array.from({length: N*N}, (_, i) => (i+1)%(N*N));

        // 计算逆序对数量
        const inversions = (() => {
            const a = nums.filter(x => x !== 0);
            let inv = 0;
            for (let i = 0; i < a.length; i++) {
                for (let j = i + 1; j < a.length; j++) {
                    if (a[i] > a[j]) inv++;
                }
            }
            return inv;
        })();

        // 计算曼哈顿距离
        const goalPosR = new Array(N*N);
        const goalPosC = new Array(N*N);
        for (let i = 0; i < goal.length; i++) {
            goalPosR[goal[i]] = Math.floor(i/N);
            goalPosC[goal[i]] = i % N;
        }

        let manhattan = 0;
        for (let i = 0; i < nums.length; i++) {
            const v = nums[i];
            if (v !== 0) {
                const r = Math.floor(i/N), c = i % N;
                manhattan += Math.abs(r - goalPosR[v]) + Math.abs(c - goalPosC[v]);
            }
        }

        // 根据拼图大小和复杂度评估难度
        const baseComplexity = N * N;
        const relativeInversions = inversions / (baseComplexity * baseComplexity / 4);
        const relativeManhattan = manhattan / (baseComplexity * 2);

        let difficulty = 'Easy';
        const complexityScore = relativeInversions + relativeManhattan;

        if (N >= 5) {
            if (complexityScore > 1.5) difficulty = 'Extreme';
            else if (complexityScore > 1.0) difficulty = 'Hard';
            else if (complexityScore > 0.5) difficulty = 'Medium';
        } else if (N >= 4) {
            if (complexityScore > 1.2) difficulty = 'Hard';
            else if (complexityScore > 0.8) difficulty = 'Medium';
        } else {
            if (complexityScore > 1.0) difficulty = 'Medium';
        }

        return {
            difficulty,
            inversions,
            manhattan,
            complexityScore: complexityScore.toFixed(2),
            estimatedTime: N <= 3 ? '<1s' : N <= 4 ? '1-10s' : difficulty === 'Extreme' ? '30-60s' : '10-30s'
        };
    }

    // ==================== UI 相关函数 ====================
    let progressElement = null;
    let solverButton = null;

    function updateProgress(current, total) {
        if (progressElement) {
            progressElement.textContent = `进度: ${current}/${total} (${Math.round(current/total*100)}%)`;
        }
    }

    function showMessage(text, type = 'info') {
        const colors = {
            info: '#2196F3',
            success: '#4CAF50',
            error: '#F44336',
            warning: '#FF9800'
        };
        
        const msg = document.createElement('div');
        msg.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            background: ${colors[type]};
            color: white;
            padding: 12px 20px;
            border-radius: 6px;
            font-size: 14px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            max-width: 300px;
            word-wrap: break-word;
        `;
        msg.textContent = text;
        document.body.appendChild(msg);
        
        setTimeout(() => {
            if (msg.parentNode) {
                msg.parentNode.removeChild(msg);
            }
        }, 5000);
    }

    function createSolverUI() {
        // 创建主容器
        const container = document.createElement('div');
        container.id = 'klotski-solver-ui';
        container.style.cssText = `
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 9999;
            background: rgba(255, 255, 255, 0.95);
            border: 2px solid #4CAF50;
            border-radius: 10px;
            padding: 15px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.2);
            font-family: Arial, sans-serif;
            min-width: 200px;
        `;

        // 标题
        const title = document.createElement('div');
        title.textContent = '🧩 华容道求解器';
        title.style.cssText = `
            font-size: 16px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
            text-align: center;
        `;

        // 求解按钮
        solverButton = document.createElement('button');
        solverButton.textContent = '🚀 一键求解';
        solverButton.style.cssText = `
            width: 100%;
            padding: 10px;
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            margin-bottom: 8px;
            transition: all 0.3s;
        `;
        
        solverButton.onmouseover = () => {
            solverButton.style.transform = 'scale(1.05)';
        };
        solverButton.onmouseout = () => {
            solverButton.style.transform = 'scale(1)';
        };

        // 进度显示
        progressElement = document.createElement('div');
        progressElement.style.cssText = `
            font-size: 12px;
            color: #666;
            text-align: center;
            margin-bottom: 8px;
            min-height: 16px;
        `;

        // 状态显示
        const statusElement = document.createElement('div');
        statusElement.style.cssText = `
            font-size: 11px;
            color: #888;
            text-align: center;
            line-height: 1.3;
        `;
        statusElement.innerHTML = '支持 3×3 / 4×4 / 5×5<br>IDA* + 曼哈顿距离';

        container.appendChild(title);
        container.appendChild(solverButton);
        container.appendChild(progressElement);
        container.appendChild(statusElement);

        document.body.appendChild(container);

        // 绑定求解事件
        solverButton.onclick = solvePuzzle;
    }

    // ==================== 主求解函数 ====================
    async function solvePuzzle() {
        try {
            solverButton.disabled = true;
            solverButton.textContent = '🔍 分析中...';
            progressElement.textContent = '正在读取棋盘...';

            // 读取棋盘状态
            const {nums, N} = readBoard();
            console.log(`检测到 ${N}×${N} 棋盘:`, nums);

            // 检查是否已经完成
            const goal = Array.from({length: N*N}, (_, i) => (i+1)%(N*N));
            if (JSON.stringify(nums) === JSON.stringify(goal)) {
                showMessage('恭喜！拼图已经完成了！', 'success');
                return;
            }

            // 评估难度
            const difficulty = assessDifficulty(nums, N);
            console.log('难度评估:', difficulty);

            progressElement.textContent = `难度: ${difficulty.difficulty} | 预计: ${difficulty.estimatedTime}`;
            showMessage(`难度: ${difficulty.difficulty}, 预计用时: ${difficulty.estimatedTime}`, 'info');

            // 根据难度调整参数
            let maxTimeMs = 20000;
            let clickDelay = 100;

            if (N >= 5) {
                maxTimeMs = difficulty.difficulty === 'Extreme' ? 90000 : 60000;
                clickDelay = 120; // 5×5拼图点击稍慢一些，避免界面卡顿
            } else if (N >= 4) {
                maxTimeMs = difficulty.difficulty === 'Hard' ? 45000 : 30000;
                clickDelay = 100;
            }

            progressElement.textContent = '正在计算最优解...';

            // 求解
            const result = solveNPuzzle(nums, N, {maxTimeMs});

            console.log('求解结果:', {
                步数: result.moves.length,
                路径: result.moves.join(''),
                扩展节点: result.expanded,
                用时: `${result.timeMs.toFixed(1)}ms`,
                效率: `${(result.expanded / (result.timeMs / 1000)).toFixed(0)} 节点/秒`
            });

            const timeStr = result.timeMs > 1000 ? `${(result.timeMs/1000).toFixed(1)}s` : `${result.timeMs.toFixed(0)}ms`;
            showMessage(`找到解！步数: ${result.moves.length}, 用时: ${timeStr}`, 'success');

            // 开始自动执行
            solverButton.textContent = '🎯 执行中...';
            progressElement.textContent = '准备开始自动操作...';

            await new Promise(resolve => setTimeout(resolve, 800)); // 短暂延迟
            await playByClicks(result.moves, N, clickDelay);

            showMessage('🎉 自动求解完成！', 'success');
            progressElement.textContent = `完成！用了 ${result.moves.length} 步`;

        } catch (error) {
            console.error('求解失败:', error);
            let errorMsg = error.message;

            // 针对不同错误给出建议
            if (errorMsg.includes('超时')) {
                errorMsg += '\n💡 建议：重新打乱拼图或等待更简单的布局';
            } else if (errorMsg.includes('不可解')) {
                errorMsg += '\n💡 这个布局无解，请重新开始游戏';
            }

            showMessage(`求解失败: ${errorMsg}`, 'error');
            progressElement.textContent = '求解失败';
        } finally {
            solverButton.disabled = false;
            solverButton.textContent = '🚀 一键求解';
        }
    }

    // ==================== 初始化 ====================
    function init() {
        // 等待页面加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', init);
            return;
        }

        // 等待游戏界面加载
        const checkGameLoaded = () => {
            const gameBoard = document.querySelector('.grid-container');
            if (gameBoard) {
                createSolverUI();
                console.log('🧩 数字华容道求解器已加载！');
            } else {
                setTimeout(checkGameLoaded, 1000);
            }
        };

        checkGameLoaded();
    }

    // 启动
    init();

})();
