// ==UserScript==
// @name         数字华容道自动求解器 (IDA* + 曼哈顿距离)
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  使用IDA*算法+曼哈顿距离启发式自动求解数字华容道，支持3×3/4×4/5×5
// <AUTHOR> Assistant
// @match        https://itstudio.henau.edu.cn/apps/klotski/*
// @grant        none
// ==/UserScript==

(function() {
    'use strict';

    // ==================== N-拼图求解器 (IDA* + 曼哈顿距离 + 线性冲突) ====================
    function solveNPuzzle(start, N, options = {}) {
        const t0 = performance.now();
        const GOAL = Array.from({length: N*N}, (_, i) => (i+1)%(N*N));
        const goalPosR = new Array(N*N);
        const goalPosC = new Array(N*N);

        for (let i = 0; i < GOAL.length; i++) {
            goalPosR[GOAL[i]] = Math.floor(i/N);
            goalPosC[GOAL[i]] = i % N;
        }

        function manhattan(s) {
            let h = 0;
            for (let i = 0; i < s.length; i++) {
                const v = s[i];
                if (v !== 0) {
                    const r = Math.floor(i/N), c = i % N;
                    h += Math.abs(r - goalPosR[v]) + Math.abs(c - goalPosC[v]);
                }
            }
            return h;
        }

        // 优化的线性冲突启发式：使用缓存和快速计算
        const conflictCache = new Map();

        function linearConflict(s) {
            const key = s.join(',');
            if (conflictCache.has(key)) {
                return conflictCache.get(key);
            }

            let conflicts = 0;

            // 检查行冲突（优化版）
            for (let r = 0; r < N; r++) {
                const rowTiles = [];
                for (let c = 0; c < N; c++) {
                    const val = s[r * N + c];
                    if (val !== 0 && goalPosR[val] === r) {
                        rowTiles.push({goalCol: goalPosC[val], currentCol: c});
                    }
                }
                // 快速冲突计算
                for (let i = 0; i < rowTiles.length - 1; i++) {
                    for (let j = i + 1; j < rowTiles.length; j++) {
                        if (rowTiles[i].goalCol > rowTiles[j].goalCol &&
                            rowTiles[i].currentCol < rowTiles[j].currentCol) {
                            conflicts++;
                        }
                    }
                }
            }

            // 检查列冲突（优化版）
            for (let c = 0; c < N; c++) {
                const colTiles = [];
                for (let r = 0; r < N; r++) {
                    const val = s[r * N + c];
                    if (val !== 0 && goalPosC[val] === c) {
                        colTiles.push({goalRow: goalPosR[val], currentRow: r});
                    }
                }
                // 快速冲突计算
                for (let i = 0; i < colTiles.length - 1; i++) {
                    for (let j = i + 1; j < colTiles.length; j++) {
                        if (colTiles[i].goalRow > colTiles[j].goalRow &&
                            colTiles[i].currentRow < colTiles[j].currentRow) {
                            conflicts++;
                        }
                    }
                }
            }

            const result = conflicts * 2; // 每个冲突需要2步额外移动

            // 限制缓存大小
            if (conflictCache.size > 10000) {
                conflictCache.clear();
            }
            conflictCache.set(key, result);

            return result;
        }

        // 增强的启发式函数：曼哈顿距离 + 线性冲突
        function enhancedHeuristic(s) {
            return manhattan(s) + linearConflict(s);
        }

        // 快速启发式：仅使用曼哈顿距离（用于快速模式）
        function fastHeuristic(s) {
            return manhattan(s);
        }

        function isSolvable(arr) {
            const inv = (() => {
                const a = arr.filter(x => x !== 0);
                let inv = 0;
                for (let i = 0; i < a.length; i++) {
                    for (let j = i + 1; j < a.length; j++) {
                        if (a[i] > a[j]) inv++;
                    }
                }
                return inv;
            })();
            
            const blankIdx = arr.indexOf(0);
            const blankRowFromBottom = N - Math.floor(blankIdx/N);
            
            if (N % 2 === 1) {
                return inv % 2 === 0;
            } else {
                return (blankRowFromBottom % 2 === 0) ? (inv % 2 === 1) : (inv % 2 === 0);
            }
        }

        if (!isSolvable(start)) {
            throw new Error("该初始局面不可解（奇偶性不匹配）");
        }

        // 根据拼图大小调整超时时间和启发式
        const maxTimeMs = options.maxTimeMs ?? (N <= 3 ? 3000 : N <= 4 ? 15000 : 30000);
        const useEnhancedHeuristic = options.fastMode ? false : (N >= 4); // 快速模式只用曼哈顿距离
        let nodesExpanded = 0;

        // 状态缓存，避免重复计算
        const stateCache = new Map();

        const DIRS = [
            {dr: -1, dc: 0, ch: 'U', rev: 'D'},
            {dr: 1, dc: 0, ch: 'D', rev: 'U'},
            {dr: 0, dc: -1, ch: 'L', rev: 'R'},
            {dr: 0, dc: 1, ch: 'R', rev: 'L'},
        ];

        // 移动优先级：优先尝试将数字移向目标位置的方向
        function getMoveOrder(blankIdx, state) {
            const r0 = Math.floor(blankIdx / N), c0 = blankIdx % N;
            const moves = [];

            for (const d of DIRS) {
                const r = r0 + d.dr, c = c0 + d.dc;
                if (r >= 0 && r < N && c >= 0 && c < N) {
                    const toIdx = r * N + c;
                    const tile = state[toIdx];
                    if (tile !== 0) {
                        // 计算移动该数字的收益（负值表示更好）
                        const tileGoalR = goalPosR[tile], tileGoalC = goalPosC[tile];
                        const currentDist = Math.abs(r - tileGoalR) + Math.abs(c - tileGoalC);
                        const newDist = Math.abs(r0 - tileGoalR) + Math.abs(c0 - tileGoalC);
                        const benefit = currentDist - newDist;
                        moves.push({dir: d, benefit});
                    }
                }
            }

            // 按收益排序，优先尝试收益大的移动
            return moves.sort((a, b) => b.benefit - a.benefit).map(m => m.dir);
        }

        function search(path, g, bound, blankIdx, h, lastMove) {
            const now = performance.now();
            if (now - t0 > maxTimeMs) return {cost: Infinity, timeout: true};

            const f = g + h;
            if (f > bound) return {cost: f};
            if (h === 0) return {found: true};

            const r0 = Math.floor(blankIdx / N), c0 = blankIdx % N;
            let minCost = Infinity;

            // 使用智能移动顺序
            const moveOrder = getMoveOrder(blankIdx, state);

            for (const d of moveOrder) {
                if (lastMove && d.ch === lastMove.rev) continue;

                const r = r0 + d.dr, c = c0 + d.dc;
                const toIdx = r * N + c;
                const tile = state[toIdx];

                // 执行移动
                state[blankIdx] = tile;
                state[toIdx] = 0;
                nodesExpanded++;

                // 重新计算启发式值（对于大拼图使用增强启发式）
                const h2 = useEnhancedHeuristic ? enhancedHeuristic(state) : manhattan(state);

                path.push(d.ch);
                const res = search(path, g + 1, bound, toIdx, h2, d);
                if (res.found) return res;
                if (res.timeout) return res;
                if (res.cost < minCost) minCost = res.cost;

                // 回溯
                path.pop();
                state[toIdx] = tile;
                state[blankIdx] = 0;
            }
            return {cost: minCost};
        }

        const state = start.slice();
        let h0 = useEnhancedHeuristic ? enhancedHeuristic(state) : manhattan(state);
        let bound = h0;
        const path = [];
        let blank = state.indexOf(0);

        console.log(`使用${useEnhancedHeuristic ? '增强' : '基础'}启发式，初始估值: ${h0}, 超时限制: ${maxTimeMs}ms`);

        for (;;) {
            const res = search(path, 0, bound, blank, h0, null);
            if (res.found) {
                return {moves: path.slice(), expanded: nodesExpanded, timeMs: performance.now() - t0};
            }
            if (res.timeout) {
                throw new Error(`求解超时（>${maxTimeMs} ms），建议尝试重新打乱或等待更好的初始状态`);
            }
            if (res.cost === Infinity) {
                throw new Error("未找到解");
            }
            bound = res.cost;

            // 每次迭代输出进度
            if (performance.now() - t0 > 1000) {
                console.log(`搜索深度: ${bound}, 已扩展: ${nodesExpanded} 节点, 用时: ${(performance.now() - t0).toFixed(0)}ms`);
            }
        }
    }

    // ==================== 页面交互函数 ====================
    function readBoard() {
        const cells = Array.from(document.querySelectorAll('.grid-cell'));
        if (cells.length === 0) {
            throw new Error("未找到游戏棋盘，请确保游戏已加载");
        }

        const nums = cells.map(el => {
            const text = el.textContent.trim();
            if (text === '' || el.classList.contains('empty')) {
                return 0;
            }
            const n = parseInt(text, 10);
            return Number.isFinite(n) ? n : 0;
        });

        const N = Math.sqrt(nums.length);
        if (!Number.isInteger(N)) {
            throw new Error(`检测到的格子数量 ${nums.length} 不是完全平方数`);
        }

        return {nums, N: Math.floor(N), cells};
    }

    function findBlankIndex(cells) {
        return cells.findIndex(el => {
            const text = el.textContent.trim();
            return text === '' || el.classList.contains('empty');
        });
    }

    function playByClicks(moves, N, delay = 80) {
        const delta = {U: -N, D: N, L: -1, R: 1};
        let i = 0;

        return new Promise((resolve) => {
            const timer = setInterval(() => {
                try {
                    const currentCells = Array.from(document.querySelectorAll('.grid-cell'));
                    const blankIdx = findBlankIndex(currentCells);

                    if (blankIdx < 0 || i >= moves.length) {
                        clearInterval(timer);
                        resolve();
                        return;
                    }

                    const move = moves[i++];
                    const targetIdx = blankIdx + delta[move];

                    if (targetIdx < 0 || targetIdx >= currentCells.length) {
                        clearInterval(timer);
                        resolve();
                        return;
                    }

                    // 点击目标格子
                    currentCells[targetIdx].click();

                    // 更新进度
                    updateProgress(i, moves.length);

                } catch (error) {
                    console.error('自动点击出错:', error);
                    clearInterval(timer);
                    resolve();
                }
            }, delay);
        });
    }

    // 添加难度评估函数
    function assessDifficulty(nums, N) {
        const goal = Array.from({length: N*N}, (_, i) => (i+1)%(N*N));

        // 计算逆序对数量
        const inversions = (() => {
            const a = nums.filter(x => x !== 0);
            let inv = 0;
            for (let i = 0; i < a.length; i++) {
                for (let j = i + 1; j < a.length; j++) {
                    if (a[i] > a[j]) inv++;
                }
            }
            return inv;
        })();

        // 计算曼哈顿距离
        const goalPosR = new Array(N*N);
        const goalPosC = new Array(N*N);
        for (let i = 0; i < goal.length; i++) {
            goalPosR[goal[i]] = Math.floor(i/N);
            goalPosC[goal[i]] = i % N;
        }

        let manhattan = 0;
        for (let i = 0; i < nums.length; i++) {
            const v = nums[i];
            if (v !== 0) {
                const r = Math.floor(i/N), c = i % N;
                manhattan += Math.abs(r - goalPosR[v]) + Math.abs(c - goalPosC[v]);
            }
        }

        // 根据拼图大小和复杂度评估难度
        const baseComplexity = N * N;
        const relativeInversions = inversions / (baseComplexity * baseComplexity / 4);
        const relativeManhattan = manhattan / (baseComplexity * 2);

        let difficulty = 'Easy';
        const complexityScore = relativeInversions + relativeManhattan;

        if (N >= 5) {
            if (complexityScore > 1.5) difficulty = 'Extreme';
            else if (complexityScore > 1.0) difficulty = 'Hard';
            else if (complexityScore > 0.5) difficulty = 'Medium';
        } else if (N >= 4) {
            if (complexityScore > 1.2) difficulty = 'Hard';
            else if (complexityScore > 0.8) difficulty = 'Medium';
        } else {
            if (complexityScore > 1.0) difficulty = 'Medium';
        }

        return {
            difficulty,
            inversions,
            manhattan,
            complexityScore: complexityScore.toFixed(2),
            estimatedTime: N <= 3 ? '<1s' : N <= 4 ? '1-10s' : difficulty === 'Extreme' ? '30-60s' : '10-30s'
        };
    }

    // ==================== UI 相关函数 ====================
    let progressElement = null;
    let solverButton = null;

    function updateProgress(current, total) {
        if (progressElement) {
            progressElement.textContent = `进度: ${current}/${total} (${Math.round(current/total*100)}%)`;
        }
    }

    function showMessage(text, type = 'info') {
        const colors = {
            info: '#2196F3',
            success: '#4CAF50',
            error: '#F44336',
            warning: '#FF9800'
        };
        
        const msg = document.createElement('div');
        msg.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            background: ${colors[type]};
            color: white;
            padding: 12px 20px;
            border-radius: 6px;
            font-size: 14px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            max-width: 300px;
            word-wrap: break-word;
        `;
        msg.textContent = text;
        document.body.appendChild(msg);
        
        setTimeout(() => {
            if (msg.parentNode) {
                msg.parentNode.removeChild(msg);
            }
        }, 5000);
    }

    function createSolverUI() {
        // 创建主容器
        const container = document.createElement('div');
        container.id = 'klotski-solver-ui';
        container.style.cssText = `
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 9999;
            background: rgba(255, 255, 255, 0.95);
            border: 2px solid #4CAF50;
            border-radius: 10px;
            padding: 15px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.2);
            font-family: Arial, sans-serif;
            min-width: 200px;
            max-width: 350px;
            resize: both;
            overflow: auto;
            cursor: move;
        `;

        // 标题栏（可拖动）
        const titleBar = document.createElement('div');
        titleBar.style.cssText = `
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            cursor: move;
            user-select: none;
        `;

        // 标题
        const title = document.createElement('div');
        title.textContent = '🧩 华容道求解器';
        title.style.cssText = `
            font-size: 16px;
            font-weight: bold;
            color: #333;
            flex: 1;
        `;

        // 控制按钮容器
        const controls = document.createElement('div');
        controls.style.cssText = `
            display: flex;
            gap: 5px;
        `;

        // 最小化按钮
        const minimizeBtn = document.createElement('button');
        minimizeBtn.textContent = '−';
        minimizeBtn.style.cssText = `
            width: 20px;
            height: 20px;
            border: none;
            background: #ff9500;
            color: white;
            border-radius: 3px;
            cursor: pointer;
            font-size: 14px;
            line-height: 1;
        `;

        // 关闭按钮
        const closeBtn = document.createElement('button');
        closeBtn.textContent = '×';
        closeBtn.style.cssText = `
            width: 20px;
            height: 20px;
            border: none;
            background: #ff5f57;
            color: white;
            border-radius: 3px;
            cursor: pointer;
            font-size: 14px;
            line-height: 1;
        `;

        controls.appendChild(minimizeBtn);
        controls.appendChild(closeBtn);
        titleBar.appendChild(title);
        titleBar.appendChild(controls);

        // 内容区域
        const content = document.createElement('div');
        content.id = 'solver-content';
        content.style.cssText = `
            transition: all 0.3s ease;
        `;

        // 按钮容器
        const buttonContainer = document.createElement('div');
        buttonContainer.style.cssText = `
            display: flex;
            gap: 5px;
            margin-bottom: 8px;
        `;

        // 求解按钮
        solverButton = document.createElement('button');
        solverButton.textContent = '🚀 最优解';
        solverButton.style.cssText = `
            flex: 1;
            padding: 10px;
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 13px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
        `;

        // 快速求解按钮
        const fastButton = document.createElement('button');
        fastButton.textContent = '⚡ 快速';
        fastButton.style.cssText = `
            flex: 1;
            padding: 10px;
            background: linear-gradient(45deg, #FF9800, #F57C00);
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 13px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
        `;

        solverButton.onmouseover = () => {
            solverButton.style.transform = 'scale(1.05)';
        };
        solverButton.onmouseout = () => {
            solverButton.style.transform = 'scale(1)';
        };

        fastButton.onmouseover = () => {
            fastButton.style.transform = 'scale(1.05)';
        };
        fastButton.onmouseout = () => {
            fastButton.style.transform = 'scale(1)';
        };

        buttonContainer.appendChild(solverButton);
        buttonContainer.appendChild(fastButton);

        // 进度显示
        progressElement = document.createElement('div');
        progressElement.style.cssText = `
            font-size: 12px;
            color: #666;
            text-align: center;
            margin-bottom: 8px;
            min-height: 16px;
        `;

        // 状态显示
        const statusElement = document.createElement('div');
        statusElement.style.cssText = `
            font-size: 11px;
            color: #888;
            text-align: center;
            line-height: 1.3;
        `;
        statusElement.innerHTML = '支持 3×3 / 4×4 / 5×5<br>IDA* + 线性冲突启发式<br>智能移动排序优化';

        // 组装UI
        content.appendChild(buttonContainer);
        content.appendChild(progressElement);
        content.appendChild(statusElement);

        container.appendChild(titleBar);
        container.appendChild(content);
        document.body.appendChild(container);

        // 拖动功能
        let isDragging = false;
        let dragOffset = {x: 0, y: 0};

        titleBar.onmousedown = (e) => {
            isDragging = true;
            dragOffset.x = e.clientX - container.offsetLeft;
            dragOffset.y = e.clientY - container.offsetTop;
            container.style.cursor = 'grabbing';
            e.preventDefault();
        };

        document.onmousemove = (e) => {
            if (isDragging) {
                const newX = Math.max(0, Math.min(window.innerWidth - container.offsetWidth, e.clientX - dragOffset.x));
                const newY = Math.max(0, Math.min(window.innerHeight - container.offsetHeight, e.clientY - dragOffset.y));
                container.style.left = newX + 'px';
                container.style.top = newY + 'px';
            }
        };

        document.onmouseup = () => {
            if (isDragging) {
                isDragging = false;
                container.style.cursor = 'move';
            }
        };

        // 最小化功能
        let isMinimized = false;
        minimizeBtn.onclick = () => {
            isMinimized = !isMinimized;
            if (isMinimized) {
                content.style.display = 'none';
                container.style.height = 'auto';
                minimizeBtn.textContent = '+';
            } else {
                content.style.display = 'block';
                minimizeBtn.textContent = '−';
            }
        };

        // 关闭功能
        closeBtn.onclick = () => {
            container.style.display = 'none';
        };

        // 双击标题栏重置位置
        titleBar.ondblclick = () => {
            container.style.left = '20px';
            container.style.top = '20px';
            container.style.width = 'auto';
            container.style.height = 'auto';
        };

        // 绑定求解事件
        solverButton.onclick = () => solvePuzzle(false); // 最优解模式
        fastButton.onclick = () => solvePuzzle(true);    // 快速模式

        // 添加键盘快捷键
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === 's') {
                e.preventDefault();
                if (container.style.display !== 'none') {
                    solvePuzzle(false); // 最优解
                }
            }
            if (e.ctrlKey && e.key === 'f') {
                e.preventDefault();
                if (container.style.display !== 'none') {
                    solvePuzzle(true); // 快速模式
                }
            }
            if (e.key === 'Escape') {
                container.style.display = container.style.display === 'none' ? 'block' : 'none';
            }
        });
    }

    // ==================== 主求解函数 ====================
    async function solvePuzzle(fastMode = false) {
        try {
            solverButton.disabled = true;
            solverButton.textContent = fastMode ? '⚡ 计算中...' : '🔍 分析中...';
            progressElement.textContent = '正在读取棋盘...';

            // 读取棋盘状态
            const {nums, N} = readBoard();
            console.log(`检测到 ${N}×${N} 棋盘:`, nums);

            // 检查是否已经完成
            const goal = Array.from({length: N*N}, (_, i) => (i+1)%(N*N));
            if (JSON.stringify(nums) === JSON.stringify(goal)) {
                showMessage('恭喜！拼图已经完成了！', 'success');
                return;
            }

            // 评估难度
            const difficulty = assessDifficulty(nums, N);
            console.log('难度评估:', difficulty);

            if (!fastMode) {
                progressElement.textContent = `难度: ${difficulty.difficulty} | 预计: ${difficulty.estimatedTime}`;
                showMessage(`难度: ${difficulty.difficulty}, 预计用时: ${difficulty.estimatedTime}`, 'info');
            } else {
                progressElement.textContent = '快速模式：优先速度而非最优解';
                showMessage('快速模式：可能不是最短步数，但求解更快', 'info');
            }

            // 根据模式和难度调整参数
            let maxTimeMs = 5000;
            let clickDelay = 60;

            if (fastMode) {
                // 快速模式：更短超时，仅使用曼哈顿距离
                maxTimeMs = N <= 3 ? 1000 : N <= 4 ? 3000 : 8000;
                clickDelay = 60;
            } else {
                // 最优解模式：较长超时，使用增强启发式
                if (N >= 5) {
                    maxTimeMs = difficulty.difficulty === 'Extreme' ? 30000 : 20000;
                    clickDelay = 80;
                } else if (N >= 4) {
                    maxTimeMs = difficulty.difficulty === 'Hard' ? 15000 : 10000;
                    clickDelay = 70;
                }
            }

            progressElement.textContent = fastMode ? '正在快速计算解法...' : '正在计算最优解...';

            // 求解
            const result = solveNPuzzle(nums, N, {maxTimeMs, fastMode: fastMode});

            console.log('求解结果:', {
                步数: result.moves.length,
                路径: result.moves.join(''),
                扩展节点: result.expanded,
                用时: `${result.timeMs.toFixed(1)}ms`,
                效率: `${(result.expanded / (result.timeMs / 1000)).toFixed(0)} 节点/秒`
            });

            const timeStr = result.timeMs > 1000 ? `${(result.timeMs/1000).toFixed(1)}s` : `${result.timeMs.toFixed(0)}ms`;
            showMessage(`找到解！步数: ${result.moves.length}, 用时: ${timeStr}`, 'success');

            // 开始自动执行
            solverButton.textContent = '🎯 执行中...';
            progressElement.textContent = '准备开始自动操作...';

            await new Promise(resolve => setTimeout(resolve, 600)); // 短暂延迟
            await playByClicks(result.moves, N, clickDelay);

            const modeText = fastMode ? '快速求解' : '最优求解';
            showMessage(`🎉 ${modeText}完成！`, 'success');
            progressElement.textContent = `完成！用了 ${result.moves.length} 步`;

        } catch (error) {
            console.error('求解失败:', error);
            let errorMsg = error.message;

            // 针对不同错误给出建议
            if (errorMsg.includes('超时')) {
                errorMsg += '\n💡 建议：重新打乱拼图或等待更简单的布局';
            } else if (errorMsg.includes('不可解')) {
                errorMsg += '\n💡 这个布局无解，请重新开始游戏';
            }

            showMessage(`求解失败: ${errorMsg}`, 'error');
            progressElement.textContent = '求解失败';
        } finally {
            solverButton.disabled = false;
            solverButton.textContent = fastMode ? '⚡ 快速' : '🚀 最优解';
        }
    }

    // ==================== 初始化 ====================
    function init() {
        // 等待页面加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', init);
            return;
        }

        // 等待游戏界面加载
        const checkGameLoaded = () => {
            const gameBoard = document.querySelector('.grid-container');
            if (gameBoard) {
                createSolverUI();
                console.log('🧩 数字华容道求解器已加载！');
            } else {
                setTimeout(checkGameLoaded, 1000);
            }
        };

        checkGameLoaded();
    }

    // 启动
    init();

})();
