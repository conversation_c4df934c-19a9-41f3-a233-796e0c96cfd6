import {u as r, l as s, a} from "./index-Ruo8-Trj.js";
const p = {
    __name: "index",
    setup(i) {
        const t = a()
          , n = encodeURIComponent(window.location.origin + "/apps/klotski/")
          , c = r();
        return (async () => {
            const o = new URLSearchParams(window.location.search).get("code");
            if (!o)
                window.location.href = `https://oauth.henau.edu.cn/oauth2_qr_connect/login?appid=nd79d17d43c672c01f&redirect_uri=${n}&response_type=code&scope=henauapi_login&state=STATE`;
            else
                try {
                    const e = await s({
                        code: o
                    });
                    c.userInfo(e.data),
                    t.replace("/game")
                } catch (e) {
                    console.log("登录失败:", e),
                    window.location.href = `https://oauth.henau.edu.cn/oauth2_qr_connect/login?appid=nd79d17d43c672c01f&redirect_uri=${n}&response_type=code&scope=henauapi_login&state=STATE`
                }
        }
        )(),
        (o, e) => null
    }
};
export {p as default};
