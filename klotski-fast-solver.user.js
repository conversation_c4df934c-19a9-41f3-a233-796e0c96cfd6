// ==UserScript==
// @name         数字华容道快速求解器 (BFS广度优先)
// @namespace    http://tampermonkey.net/
// @version      2.0
// @description  使用BFS广度优先搜索快速求解数字华容道，支持3×3/4×4/5×5，速度极快
// <AUTHOR> Assistant
// @match        https://itstudio.henau.edu.cn/apps/klotski/*
// @grant        none
// ==/UserScript==

(function() {
    'use strict';

    const buttonId = 'fast-solver-btn';
    
    // 防止重复创建
    if (document.getElementById(buttonId)) {
        console.log('快速求解器已存在，无需重复创建。');
        return;
    }

    // ==================== N×N 数字拼图通用求解器 (BFS) ====================
    function PuzzleSolver(gridSize) {
        if (![3, 4, 5].includes(gridSize)) {
            throw new Error(`不支持的棋盘尺寸：${gridSize}×${gridSize}`);
        }
        
        const N = gridSize;
        const totalTiles = N * N;
        
        // 目标状态：1,2,3,...,N²-1,0
        const targetState = [];
        for (let i = 1; i < totalTiles; i++) {
            targetState.push(i);
        }
        targetState.push(0);
        const targetStateStr = targetState.toString();

        // 获取所有可能的移动
        function getMoves(state) {
            const emptyIndex = state.indexOf(0);
            if (emptyIndex === -1) return [];
            
            const moves = [];
            const row = Math.floor(emptyIndex / N);
            const col = emptyIndex % N;

            function swap(stateArr, i, j) {
                const newState = [...stateArr];
                [newState[i], newState[j]] = [newState[j], newState[i]];
                return newState;
            }

            // 四个方向的移动
            if (row > 0) moves.push(swap(state, emptyIndex, emptyIndex - N));     // 上
            if (row < N - 1) moves.push(swap(state, emptyIndex, emptyIndex + N)); // 下
            if (col > 0) moves.push(swap(state, emptyIndex, emptyIndex - 1));     // 左
            if (col < N - 1) moves.push(swap(state, emptyIndex, emptyIndex + 1)); // 右
            
            return moves;
        }

        // BFS求解
        this.solve = function(initialState) {
            const startTime = performance.now();
            
            // 检查是否已经完成
            if (initialState.toString() === targetStateStr) {
                return [initialState];
            }
            
            const queue = [[initialState]];
            const visited = new Set([initialState.toString()]);
            let nodesExpanded = 0;
            
            while (queue.length > 0) {
                const path = queue.shift();
                const currentState = path[path.length - 1];
                nodesExpanded++;
                
                // 每1000次检查一下时间，避免超时
                if (nodesExpanded % 1000 === 0) {
                    const elapsed = performance.now() - startTime;
                    if (elapsed > 30000) { // 30秒超时
                        throw new Error(`求解超时（${elapsed.toFixed(0)}ms），建议重新打乱拼图`);
                    }
                }
                
                const possibleMoves = getMoves(currentState);
                for (const move of possibleMoves) {
                    const moveStr = move.toString();
                    if (!visited.has(moveStr)) {
                        visited.add(moveStr);
                        const newPath = [...path, move];
                        
                        // 找到解
                        if (moveStr === targetStateStr) {
                            const elapsed = performance.now() - startTime;
                            console.log(`🎉 BFS求解完成！步数: ${newPath.length - 1}, 扩展节点: ${nodesExpanded}, 用时: ${elapsed.toFixed(1)}ms`);
                            return newPath;
                        }
                        
                        queue.push(newPath);
                    }
                }
            }
            
            throw new Error('无解：该拼图布局无法还原到目标状态');
        };
    }

    // ==================== 自动操作执行器 ====================
    function autoExecute(solution, button, speed = 150) {
        if (!solution || solution.length <= 1) {
            console.log('没有找到解法或拼图已完成。');
            resetButtonState(button);
            return;
        }

        console.log(`开始自动执行，共 ${solution.length - 1} 步`);
        let stepIndex = 0;
        
        const intervalId = setInterval(() => {
            if (stepIndex >= solution.length - 1) {
                clearInterval(intervalId);
                console.log('🎉 自动求解完成！');
                showMessage('自动求解完成！', 'success');
                resetButtonState(button);
                return;
            }

            const currentGrid = solution[stepIndex];
            const nextGrid = solution[stepIndex + 1];
            const emptyIndexNext = nextGrid.indexOf(0);
            const tileToClickValue = currentGrid[emptyIndexNext];
            
            // 查找并点击对应的格子
            const allCells = document.querySelectorAll('.grid-cell');
            if (allCells.length === 0) {
                console.error('无法找到棋盘格子，自动求解已停止。');
                clearInterval(intervalId);
                resetButtonState(button);
                return;
            }

            for (const cell of allCells) {
                const cellValue = parseInt(cell.textContent.trim());
                if (cellValue === tileToClickValue) {
                    cell.click();
                    updateProgress(stepIndex + 1, solution.length - 1);
                    break;
                }
            }
            
            stepIndex++;
        }, speed);
    }

    // ==================== UI 相关函数 ====================
    let progressElement = null;

    function resetButtonState(button) {
        button.disabled = false;
        button.textContent = '⚡ 快速求解';
        if (progressElement) {
            progressElement.textContent = '';
        }
    }

    function updateProgress(current, total) {
        if (progressElement) {
            progressElement.textContent = `进度: ${current}/${total}`;
        }
    }

    function showMessage(text, type = 'info') {
        const colors = {
            info: '#2196F3',
            success: '#4CAF50',
            error: '#F44336',
            warning: '#FF9800'
        };
        
        const msg = document.createElement('div');
        msg.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 10000;
            background: ${colors[type]};
            color: white;
            padding: 12px 20px;
            border-radius: 6px;
            font-size: 14px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            max-width: 300px;
            text-align: center;
        `;
        msg.textContent = text;
        document.body.appendChild(msg);
        
        setTimeout(() => {
            if (msg.parentNode) {
                msg.parentNode.removeChild(msg);
            }
        }, 2000);
    }

    // ==================== 创建UI ====================
    function createUI() {
        // 样式
        const style = document.createElement('style');
        style.textContent = `
            #${buttonId} {
                position: fixed;
                bottom: 20px;
                right: 20px;
                background: linear-gradient(45deg, #FF5722, #E64A19);
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 8px;
                cursor: pointer;
                z-index: 9999;
                font-size: 14px;
                font-weight: bold;
                box-shadow: 0 4px 15px rgba(0,0,0,0.3);
                font-family: Arial, sans-serif;
                transition: all 0.3s ease;
                min-width: 120px;
            }
            #${buttonId}:hover {
                transform: translateY(-2px);
                box-shadow: 0 6px 20px rgba(0,0,0,0.4);
            }
            #${buttonId}:disabled {
                background: #ccc;
                cursor: not-allowed;
                transform: none;
                box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            }
            .progress-text {
                position: fixed;
                bottom: 70px;
                right: 20px;
                background: rgba(0,0,0,0.8);
                color: white;
                padding: 8px 12px;
                border-radius: 4px;
                font-size: 12px;
                z-index: 9998;
                font-family: Arial, sans-serif;
            }
        `;
        document.head.appendChild(style);

        // 主按钮
        const autoSolveBtn = document.createElement('button');
        autoSolveBtn.id = buttonId;
        autoSolveBtn.textContent = '⚡ 快速求解';
        document.body.appendChild(autoSolveBtn);

        // 进度显示
        progressElement = document.createElement('div');
        progressElement.className = 'progress-text';
        progressElement.style.display = 'none';
        document.body.appendChild(progressElement);

        // 点击事件
        autoSolveBtn.onclick = () => {
            autoSolveBtn.disabled = true;
            autoSolveBtn.textContent = '🔍 分析中...';
            progressElement.style.display = 'block';
            progressElement.textContent = '正在读取棋盘...';
            
            setTimeout(() => {
                try {
                    solvePuzzle(autoSolveBtn);
                } catch (error) {
                    console.error('求解出错:', error);
                    showMessage(`求解失败: ${error.message}`, 'error');
                    resetButtonState(autoSolveBtn);
                    progressElement.style.display = 'none';
                }
            }, 100);
        };
    }

    // ==================== 主求解函数 ====================
    function solvePuzzle(button) {
        const cells = document.querySelectorAll('.grid-cell');
        const totalCells = cells.length;
        
        // 检测棋盘尺寸
        const gridSize = Math.sqrt(totalCells);
        if (!Number.isInteger(gridSize) || gridSize < 3 || gridSize > 5) {
            throw new Error(`无法识别棋盘尺寸或尺寸不支持 (检测到${totalCells}个格子，仅支持3×3/4×4/5×5)`);
        }

        // 读取当前状态
        const currentState = Array.from(cells).map(cell => {
            const value = cell.textContent.trim();
            return value === '' || cell.classList.contains('empty') ? 0 : parseInt(value);
        });

        console.log(`检测到 ${gridSize}×${gridSize} 棋盘:`, currentState);
        progressElement.textContent = `${gridSize}×${gridSize} 棋盘，正在求解...`;

        // 求解
        const solver = new PuzzleSolver(gridSize);
        const solution = solver.solve(currentState);
        
        if (solution) {
            showMessage(`找到解法！共${solution.length - 1}步`, 'success');
            button.textContent = '🎯 执行中...';
            
            // 根据棋盘大小调整执行速度
            const speed = gridSize <= 3 ? 120 : gridSize <= 4 ? 140 : 160;
            autoExecute(solution, button, speed);
        } else {
            throw new Error('无法找到解决方案！');
        }
    }

    // ==================== 初始化 ====================
    function init() {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', init);
            return;
        }

        const checkGameLoaded = () => {
            const gameBoard = document.querySelector('.grid-container');
            if (gameBoard) {
                createUI();
                console.log('⚡ 数字华容道快速求解器已加载！(BFS算法)');
            } else {
                setTimeout(checkGameLoaded, 1000);
            }
        };

        checkGameLoaded();
    }

    // 启动
    init();

})();
